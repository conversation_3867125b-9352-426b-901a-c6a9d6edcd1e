// Subject Management Component (Admin Only)
// Manages CRUD operations for subjects/mata pelajaran

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Input,
  Textarea,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Chip,
  Tooltip,
  Spinner
} from '@heroui/react';
import { Plus, Edit, Trash2, BookOpen, Users, FileText } from 'lucide-react';
import EmptyState from './common/EmptyState';
import { useAuthStore } from '../stores/authStore';

interface Subject {
  id: string;
  name: string;
  code: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  _count?: {
    classes: number;
    grades: number;
  };
}

interface SubjectFormData {
  name: string;
  code: string;
  description: string;
}

const SubjectManager: React.FC = () => {
  const { user } = useAuthStore();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [editingSubject, setEditingSubject] = useState<Subject | null>(null);
  const [formData, setFormData] = useState<SubjectFormData>({
    name: '',
    code: '',
    description: ''
  });

  // Fetch subjects
  const fetchSubjects = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:5000/api/subjects', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });

      if (response.ok) {
        const result = await response.json();
        setSubjects(result.data);
      }
    } catch (error) {
      console.error('Error fetching subjects:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSubjects();
  }, []);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      const url = editingSubject ? `/api/subjects/${editingSubject.id}` : '/api/subjects';
      const method = editingSubject ? 'PUT' : 'POST';

      const response = await fetch(`http://localhost:5000${url}`, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        await fetchSubjects();
        handleCloseModal();
      } else {
        const error = await response.json();
        alert(error.message || 'Terjadi kesalahan');
      }
    } catch (error) {
      console.error('Error saving subject:', error);
      alert('Terjadi kesalahan saat menyimpan data');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle delete
  const handleDelete = async (subject: Subject) => {
    if (!confirm(`Apakah Anda yakin ingin menghapus mata pelajaran "${subject.name}"?`)) {
      return;
    }

    try {
      const response = await fetch(`http://localhost:5000/api/subjects/${subject.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });

      if (response.ok) {
        await fetchSubjects();
      } else {
        const error = await response.json();
        alert(error.message || 'Gagal menghapus mata pelajaran');
      }
    } catch (error) {
      console.error('Error deleting subject:', error);
      alert('Terjadi kesalahan saat menghapus data');
    }
  };

  // Handle modal open for create/edit
  const handleOpenModal = (subject?: Subject) => {
    if (subject) {
      setEditingSubject(subject);
      setFormData({
        name: subject.name,
        code: subject.code,
        description: subject.description || ''
      });
    } else {
      setEditingSubject(null);
      setFormData({
        name: '',
        code: '',
        description: ''
      });
    }
    onOpen();
  };

  // Handle modal close
  const handleCloseModal = () => {
    setEditingSubject(null);
    setFormData({
      name: '',
      code: '',
      description: ''
    });
    onClose();
  };

  // Check if user is admin
  if (user?.role !== 'ADMIN') {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500">Akses ditolak. Hanya admin yang dapat mengakses halaman ini.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Action Button */}
      <div className="flex justify-end">
        <Button
          color="primary"
          startContent={<Plus className="w-4 h-4" />}
          onPress={() => handleOpenModal()}
        >
          Tambah Mata Pelajaran
        </Button>
      </div>

      {/* Subjects Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <BookOpen className="w-5 h-5" />
            <span className="font-semibold">Daftar Mata Pelajaran</span>
          </div>
        </CardHeader>
        <CardBody>
          {loading ? (
            <div className="flex justify-center py-8">
              <Spinner size="lg" />
            </div>
          ) : (
            <Table aria-label="Subjects table">
              <TableHeader>
                <TableColumn>KODE</TableColumn>
                <TableColumn>NAMA MATA PELAJARAN</TableColumn>
                <TableColumn>DESKRIPSI</TableColumn>
                <TableColumn>KELAS</TableColumn>
                <TableColumn>STATUS</TableColumn>
                <TableColumn>AKSI</TableColumn>
              </TableHeader>
              <TableBody emptyContent={
                <EmptyState
                  icon={BookOpen}
                  title="Belum ada mata pelajaran"
                  description="Mulai dengan menambahkan mata pelajaran pertama untuk memulai kurikulum sekolah"
                  actionLabel="Tambah Mata Pelajaran"
                  onAction={() => handleOpenModal()}
                  actionColor="primary"
                />
              }>
                {subjects.map((subject) => (
                  <TableRow key={subject.id}>
                    <TableCell>
                      <Chip size="sm" variant="flat" color="primary">
                        {subject.code}
                      </Chip>
                    </TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">{subject.name}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <p className="text-sm text-gray-600 max-w-xs truncate">
                        {subject.description || '-'}
                      </p>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Users className="w-4 h-4 text-gray-400" />
                        <span className="text-sm">{subject._count?.classes || 0}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Chip
                        size="sm"
                        color={subject.isActive ? "success" : "danger"}
                        variant="flat"
                      >
                        {subject.isActive ? "Aktif" : "Nonaktif"}
                      </Chip>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Tooltip content="Edit">
                          <Button
                            isIconOnly
                            size="sm"
                            variant="light"
                            onPress={() => handleOpenModal(subject)}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                        </Tooltip>
                        <Tooltip content="Hapus">
                          <Button
                            isIconOnly
                            size="sm"
                            variant="light"
                            color="danger"
                            onPress={() => handleDelete(subject)}
                            isDisabled={(subject._count?.classes || 0) > 0}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </Tooltip>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardBody>
      </Card>

      {/* Create/Edit Modal */}
      <Modal isOpen={isOpen} onClose={handleCloseModal} size="lg">
        <ModalContent>
          <form onSubmit={handleSubmit}>
            <ModalHeader>
              <div className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                <span>{editingSubject ? 'Edit' : 'Tambah'} Mata Pelajaran</span>
              </div>
            </ModalHeader>
            <ModalBody className="space-y-4">
              <Input
                label="Nama Mata Pelajaran"
                placeholder="Contoh: Matematika"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                isRequired
              />
              <Input
                label="Kode Mata Pelajaran"
                placeholder="Contoh: MTK"
                value={formData.code}
                onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
                isRequired
              />
              <Textarea
                label="Deskripsi"
                placeholder="Deskripsi mata pelajaran (opsional)"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              />
            </ModalBody>
            <ModalFooter>
              <Button variant="light" onPress={handleCloseModal}>
                Batal
              </Button>
              <Button
                color="primary"
                type="submit"
                isLoading={submitting}
              >
                {editingSubject ? 'Update' : 'Simpan'}
              </Button>
            </ModalFooter>
          </form>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default SubjectManager;
