// Test Badge Creation - Guru Digital Pelangi
import fetch from 'node-fetch';

const API_BASE_URL = 'http://localhost:5000/api';

// Test credentials (adjust as needed)
const testCredentials = {
  email: '<EMAIL>',
  password: 'admin123'
};

async function testBadgeCreation() {
  try {
    console.log('🔐 Logging in...');
    
    // Login to get token
    const loginResponse = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testCredentials)
    });

    const loginResult = await loginResponse.json();
    
    if (!loginResult.success) {
      console.error('❌ Login failed:', loginResult.error);
      return;
    }

    const token = loginResult.data.token;
    console.log('✅ Login successful');

    // Test badge creation
    console.log('🏆 Creating test badge...');
    
    const badgeData = {
      name: 'Test Badge',
      description: 'Badge untuk testing sistem',
      xpReward: 50,
      icon: '🏆'
    };

    const createResponse = await fetch(`${API_BASE_URL}/badges`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(badgeData)
    });

    const createResult = await createResponse.json();
    
    if (createResult.success) {
      console.log('✅ Badge created successfully:', createResult.data);
    } else {
      console.error('❌ Badge creation failed:', createResult.error);
    }

    // Test get badges
    console.log('📋 Getting all badges...');
    
    const getBadgesResponse = await fetch(`${API_BASE_URL}/badges`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const getBadgesResult = await getBadgesResponse.json();
    
    if (getBadgesResult.success) {
      console.log('✅ Badges retrieved:', getBadgesResult.data.length, 'badges found');
      getBadgesResult.data.forEach((badge, index) => {
        console.log(`  ${index + 1}. ${badge.icon} ${badge.name} (${badge.xpReward} XP)`);
      });
    } else {
      console.error('❌ Failed to get badges:', getBadgesResult.error);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run test
testBadgeCreation();
