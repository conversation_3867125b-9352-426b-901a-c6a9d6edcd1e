import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Card, 
  CardBody, 
  CardHeader, 
  Button, 
  Input, 
  Select,
  SelectItem,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Avatar,
  Chip
} from '@heroui/react';

const SiswaContent = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterKelas, setFilterKelas] = useState('all');
  
  const siswaData = [
    {
      id: 1,
      name: '<PERSON><PERSON><PERSON>',
      username: 'adityap',
      email: '<EMAIL>',
      kelas: '9C',
      status: 'active',
      avatar: '👨‍🎓',
      joinDate: '15 Jan 2025',
      lastActivity: '2 jam lalu'
    },
    {
      id: 2,
      name: 'Sit<PERSON>',
      username: 'siti',
      email: '<EMAIL>',
      kelas: '7A',
      status: 'active',
      avatar: '👩‍🎓',
      joinDate: '20 Jan 2025',
      lastActivity: '1 hari lalu'
    },
    {
      id: 3,
      name: '<PERSON><PERSON>',
      username: 'budi',
      email: '<EMAIL>',
      kelas: '9E',
      status: 'inactive',
      avatar: '👨‍🎓',
      joinDate: '10 Feb 2025',
      lastActivity: '1 minggu lalu'
    }
  ];

  const kelasOptions = [
    { key: 'all', label: 'Semua Kelas' },
    { key: '9C', label: '9C' },
    { key: '7A', label: '7A' },
    { key: '9E', label: '9E' },
    { key: 'XI PA1', label: 'XI PA1' }
  ];

  const filteredSiswa = siswaData.filter(siswa => {
    const matchesSearch = siswa.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         siswa.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         siswa.kelas.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesKelas = filterKelas === 'all' || siswa.kelas === filterKelas;
    return matchesSearch && matchesKelas;
  });

  return (
    <div className="p-8">
      {/* Header */}
      <div className="flex justify-between items-start mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Siswa</h1>
          <p className="text-gray-600">Kelola semua siswa dan penempatan kelas</p>
        </div>
        <div className="flex space-x-3">
          <Button 
            variant="bordered" 
            startContent={<span>📊</span>}
          >
            Debug Data
          </Button>
          <Button 
            color="primary" 
            startContent={<span>👥</span>}
          >
            Tambah Siswa
          </Button>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row gap-4">
          <Input
            type="text"
            placeholder="Cari siswa berdasarkan nama, username, atau kelas..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            startContent={<span className="text-gray-400">🔍</span>}
            className="flex-1"
          />
          <div className="flex items-center space-x-4">
            <Select 
              placeholder="Filter Kelas"
              selectedKeys={[filterKelas]}
              onSelectionChange={(keys) => setFilterKelas(Array.from(keys)[0] as string)}
              className="w-48"
            >
              {kelasOptions.map((kelas) => (
                <SelectItem key={kelas.key} value={kelas.key}>
                  {kelas.label}
                </SelectItem>
              ))}
            </Select>
            <Chip color="primary" variant="flat">
              {filteredSiswa.length} Siswa
            </Chip>
          </div>
        </div>
      </div>

      {/* Students Table */}
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <Card>
          <CardBody className="p-0">
            <Table aria-label="Tabel Siswa">
              <TableHeader>
                <TableColumn>SISWA</TableColumn>
                <TableColumn>USERNAME</TableColumn>
                <TableColumn>KELAS</TableColumn>
                <TableColumn>BERGABUNG</TableColumn>
                <TableColumn align="center">AKSI</TableColumn>
              </TableHeader>
              <TableBody>
                {filteredSiswa.map((siswa, index) => (
                  <TableRow key={siswa.id}>
                    <TableCell>
                      <div className="flex items-center space-x-4">
                        <Avatar 
                          name={siswa.avatar}
                          className="bg-gradient-to-r from-blue-500 to-purple-500 text-white"
                        />
                        <div>
                          <p className="font-semibold text-gray-900">{siswa.name}</p>
                          <p className="text-gray-600 text-sm">{siswa.email}</p>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Chip variant="flat" color="default">
                        {siswa.username}
                      </Chip>
                    </TableCell>
                    <TableCell>
                      <Chip variant="flat" color="primary">
                        📚 {siswa.kelas}
                      </Chip>
                    </TableCell>
                    <TableCell>
                      <div>
                        <p className="text-gray-900 font-medium">{siswa.joinDate}</p>
                        <p className="text-gray-500 text-sm">Terakhir: {siswa.lastActivity}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex justify-center space-x-2">
                        <Button isIconOnly size="sm" variant="light">👁️</Button>
                        <Button isIconOnly size="sm" variant="light">✏️</Button>
                        <Button isIconOnly size="sm" variant="light">📧</Button>
                        <Button isIconOnly size="sm" variant="light">🗑️</Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardBody>
        </Card>
      </motion.div>
    </div>
  );
};

export default SiswaContent;
