{"name": "guru-digital-pelangi-backend", "version": "1.0.0", "description": "Backend API for Guru Digital Pelangi - Modern Teacher Administration System", "type": "module", "main": "src/index.js", "scripts": {"dev": "bun --watch src/index.js", "start": "bun src/index.js", "db:generate": "bunx prisma generate", "db:migrate": "bunx prisma migrate dev", "db:deploy": "bunx prisma migrate deploy", "db:studio": "bunx prisma studio", "db:seed": "bun prisma/seed.js", "db:reset": "bunx prisma migrate reset", "postinstall": "bunx prisma generate"}, "keywords": ["express", "prisma", "mysql", "education", "teacher", "student", "management"], "author": "Guru Digital Pelangi Team", "license": "MIT", "dependencies": {"express": "^4.21.2", "@prisma/client": "^6.1.0", "mysql2": "^3.11.4", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.4.7", "zod": "^3.24.1", "helmet": "^8.0.0", "express-rate-limit": "^7.4.1", "morgan": "^1.10.0"}, "devDependencies": {"prisma": "^6.1.0"}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}}