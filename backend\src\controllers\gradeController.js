// Grade Controller
// Handles CRUD operations for grades
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Get all grades
 * GET /api/grades
 */
export const getGrades = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      classId, 
      studentId, 
      subjectId, 
      gradeType 
    } = req.query;
    const skip = (page - 1) * limit;

    // Build where clause
    const where = {};
    if (classId) where.classId = classId;
    if (studentId) where.studentId = studentId;
    if (subjectId) where.subjectId = subjectId;
    if (gradeType) where.gradeType = gradeType;

    // Get grades with pagination
    const [grades, total] = await Promise.all([
      prisma.grade.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          student: {
            select: {
              id: true,
              studentId: true,
              firstName: true,
              lastName: true
            }
          },
          subject: {
            select: {
              id: true,
              name: true,
              code: true
            }
          },
          class: {
            select: {
              id: true,
              name: true,
              gradeLevel: true
            }
          },
          createdByUser: {
            select: {
              firstName: true,
              lastName: true
            }
          }
        },
        orderBy: { date: 'desc' }
      }),
      prisma.grade.count({ where })
    ]);

    res.json({
      success: true,
      data: {
        grades,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get grades error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat mengambil data nilai'
    });
  }
};

/**
 * Get grade by ID
 * GET /api/grades/:id
 */
export const getGradeById = async (req, res) => {
  try {
    const { id } = req.params;

    const grade = await prisma.grade.findUnique({
      where: { id },
      include: {
        student: {
          select: {
            id: true,
            studentId: true,
            firstName: true,
            lastName: true
          }
        },
        subject: {
          select: {
            id: true,
            name: true,
            code: true
          }
        },
        class: {
          select: {
            id: true,
            name: true,
            gradeLevel: true
          }
        },
        createdByUser: {
          select: {
            firstName: true,
            lastName: true
          }
        }
      }
    });

    if (!grade) {
      return res.status(404).json({
        success: false,
        message: 'Nilai tidak ditemukan'
      });
    }

    res.json({
      success: true,
      data: { grade }
    });

  } catch (error) {
    console.error('Get grade by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat mengambil data nilai'
    });
  }
};

/**
 * Create new grade
 * POST /api/grades
 */
export const createGrade = async (req, res) => {
  try {
    const {
      studentId,
      subjectId,
      classId,
      gradeType,
      score,
      maxScore,
      description,
      date
    } = req.body;

    const grade = await prisma.grade.create({
      data: {
        studentId,
        subjectId,
        classId,
        gradeType,
        score: parseFloat(score),
        maxScore: parseFloat(maxScore),
        description,
        date: new Date(date),
        createdBy: req.user.id
      },
      include: {
        student: {
          select: {
            id: true,
            studentId: true,
            firstName: true,
            lastName: true
          }
        },
        subject: {
          select: {
            id: true,
            name: true,
            code: true
          }
        },
        class: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      message: 'Nilai berhasil dibuat',
      data: { grade }
    });

  } catch (error) {
    console.error('Create grade error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat membuat nilai'
    });
  }
};

/**
 * Update grade
 * PUT /api/grades/:id
 */
export const updateGrade = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      studentId,
      subjectId,
      classId,
      gradeType,
      score,
      maxScore,
      description,
      date
    } = req.body;

    // Check if grade exists
    const existingGrade = await prisma.grade.findUnique({
      where: { id }
    });

    if (!existingGrade) {
      return res.status(404).json({
        success: false,
        message: 'Nilai tidak ditemukan'
      });
    }

    const grade = await prisma.grade.update({
      where: { id },
      data: {
        studentId,
        subjectId,
        classId,
        gradeType,
        score: parseFloat(score),
        maxScore: parseFloat(maxScore),
        description,
        date: new Date(date)
      },
      include: {
        student: {
          select: {
            id: true,
            studentId: true,
            firstName: true,
            lastName: true
          }
        },
        subject: {
          select: {
            id: true,
            name: true,
            code: true
          }
        },
        class: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    res.json({
      success: true,
      message: 'Nilai berhasil diupdate',
      data: { grade }
    });

  } catch (error) {
    console.error('Update grade error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat update nilai'
    });
  }
};

/**
 * Delete grade
 * DELETE /api/grades/:id
 */
export const deleteGrade = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if grade exists
    const existingGrade = await prisma.grade.findUnique({
      where: { id }
    });

    if (!existingGrade) {
      return res.status(404).json({
        success: false,
        message: 'Nilai tidak ditemukan'
      });
    }

    await prisma.grade.delete({
      where: { id }
    });

    res.json({
      success: true,
      message: 'Nilai berhasil dihapus'
    });

  } catch (error) {
    console.error('Delete grade error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat menghapus nilai'
    });
  }
};

/**
 * Get grades by class
 * GET /api/grades/class/:classId
 */
export const getGradesByClass = async (req, res) => {
  try {
    const { classId } = req.params;
    const { subjectId, gradeType } = req.query;

    const where = { classId };
    if (subjectId) where.subjectId = subjectId;
    if (gradeType) where.gradeType = gradeType;

    const grades = await prisma.grade.findMany({
      where,
      include: {
        student: {
          select: {
            id: true,
            studentId: true,
            firstName: true,
            lastName: true
          }
        },
        subject: {
          select: {
            id: true,
            name: true,
            code: true
          }
        }
      },
      orderBy: [
        { student: { firstName: 'asc' } },
        { date: 'desc' }
      ]
    });

    res.json({
      success: true,
      data: { grades }
    });

  } catch (error) {
    console.error('Get grades by class error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat mengambil nilai kelas'
    });
  }
};

/**
 * Get grades by student
 * GET /api/grades/student/:studentId
 */
export const getGradesByStudent = async (req, res) => {
  try {
    const { studentId } = req.params;
    const { subjectId, gradeType } = req.query;

    const where = { studentId };
    if (subjectId) where.subjectId = subjectId;
    if (gradeType) where.gradeType = gradeType;

    const grades = await prisma.grade.findMany({
      where,
      include: {
        subject: {
          select: {
            id: true,
            name: true,
            code: true
          }
        },
        class: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: { date: 'desc' }
    });

    // Calculate average by subject
    const subjectAverages = {};
    grades.forEach(grade => {
      const subjectId = grade.subject.id;
      if (!subjectAverages[subjectId]) {
        subjectAverages[subjectId] = {
          subject: grade.subject,
          grades: [],
          average: 0
        };
      }
      subjectAverages[subjectId].grades.push(grade);
    });

    // Calculate averages
    Object.keys(subjectAverages).forEach(subjectId => {
      const subjectGrades = subjectAverages[subjectId].grades;
      const totalScore = subjectGrades.reduce((sum, grade) => {
        return sum + (grade.score / grade.maxScore) * 100;
      }, 0);
      subjectAverages[subjectId].average = totalScore / subjectGrades.length;
    });

    res.json({
      success: true,
      data: { 
        grades,
        subjectAverages: Object.values(subjectAverages)
      }
    });

  } catch (error) {
    console.error('Get grades by student error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat mengambil nilai siswa'
    });
  }
};
