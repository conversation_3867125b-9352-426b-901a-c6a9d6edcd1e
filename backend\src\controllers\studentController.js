// Student Controller
// Handles CRUD operations for students
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Get all students
 * GET /api/students
 */
export const getStudents = async (req, res) => {
  try {
    const { page = 1, limit = 10, search, classId, status } = req.query;
    const skip = (page - 1) * limit;

    // Build where clause
    const where = {};
    
    if (search) {
      where.OR = [
        { firstName: { contains: search, mode: 'insensitive' } },
        { lastName: { contains: search, mode: 'insensitive' } },
        { studentId: { contains: search } }
      ];
    }
    
    if (classId) {
      where.classId = classId;
    }
    
    if (status) {
      where.status = status;
    }

    // Get students with pagination
    const [students, total] = await Promise.all([
      prisma.student.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          class: {
            select: {
              id: true,
              name: true,
              gradeLevel: true
            }
          },
          studentXp: {
            select: {
              totalXp: true,
              level: true,
              levelName: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.student.count({ where })
    ]);

    res.json({
      success: true,
      data: {
        students,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get students error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat mengambil data siswa'
    });
  }
};

/**
 * Get student by ID
 * GET /api/students/:id
 */
export const getStudentById = async (req, res) => {
  try {
    const { id } = req.params;

    const student = await prisma.student.findUnique({
      where: { id },
      include: {
        class: {
          select: {
            id: true,
            name: true,
            gradeLevel: true,
            teacher: {
              select: {
                firstName: true,
                lastName: true
              }
            }
          }
        },
        studentXp: {
          select: {
            totalXp: true,
            level: true,
            levelName: true
          }
        },
        studentBadges: {
          include: {
            badge: {
              select: {
                id: true,
                name: true,
                description: true,
                icon: true
              }
            }
          }
        }
      }
    });

    if (!student) {
      return res.status(404).json({
        success: false,
        message: 'Siswa tidak ditemukan'
      });
    }

    res.json({
      success: true,
      data: { student }
    });

  } catch (error) {
    console.error('Get student by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat mengambil data siswa'
    });
  }
};

/**
 * Create new student
 * POST /api/students
 */
export const createStudent = async (req, res) => {
  try {
    const {
      studentId,
      firstName,
      lastName,
      email,
      classId,
      dateOfBirth,
      gender,
      address,
      phone,
      parentName,
      parentPhone,
      status = 'ACTIVE'
    } = req.body;

    // Check if student ID already exists
    const existingStudent = await prisma.student.findUnique({
      where: { studentId }
    });

    if (existingStudent) {
      return res.status(400).json({
        success: false,
        message: 'NISN sudah terdaftar'
      });
    }

    // Create student
    const student = await prisma.student.create({
      data: {
        studentId,
        firstName,
        lastName,
        email: email || null,
        classId: classId || null,
        dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : null,
        gender,
        address,
        phone,
        parentName,
        parentPhone,
        status
      },
      include: {
        class: {
          select: {
            id: true,
            name: true,
            gradeLevel: true
          }
        }
      }
    });

    // Create initial XP record
    await prisma.studentXp.create({
      data: {
        studentId: student.id,
        totalXp: 0,
        level: 1,
        levelName: 'Pemula'
      }
    });

    res.status(201).json({
      success: true,
      message: 'Siswa berhasil dibuat',
      data: { student }
    });

  } catch (error) {
    console.error('Create student error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat membuat siswa'
    });
  }
};

/**
 * Update student
 * PUT /api/students/:id
 */
export const updateStudent = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      studentId,
      firstName,
      lastName,
      email,
      classId,
      dateOfBirth,
      gender,
      address,
      phone,
      parentName,
      parentPhone,
      status
    } = req.body;

    // Check if student exists
    const existingStudent = await prisma.student.findUnique({
      where: { id }
    });

    if (!existingStudent) {
      return res.status(404).json({
        success: false,
        message: 'Siswa tidak ditemukan'
      });
    }

    // Check if new student ID is already taken by another student
    if (studentId !== existingStudent.studentId) {
      const duplicateStudent = await prisma.student.findUnique({
        where: { studentId }
      });

      if (duplicateStudent) {
        return res.status(400).json({
          success: false,
          message: 'NISN sudah digunakan oleh siswa lain'
        });
      }
    }

    const student = await prisma.student.update({
      where: { id },
      data: {
        studentId,
        firstName,
        lastName,
        email: email || null,
        classId: classId || null,
        dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : null,
        gender,
        address,
        phone,
        parentName,
        parentPhone,
        status
      },
      include: {
        class: {
          select: {
            id: true,
            name: true,
            gradeLevel: true
          }
        }
      }
    });

    res.json({
      success: true,
      message: 'Siswa berhasil diupdate',
      data: { student }
    });

  } catch (error) {
    console.error('Update student error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat update siswa'
    });
  }
};

/**
 * Delete student
 * DELETE /api/students/:id
 */
export const deleteStudent = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if student exists
    const existingStudent = await prisma.student.findUnique({
      where: { id }
    });

    if (!existingStudent) {
      return res.status(404).json({
        success: false,
        message: 'Siswa tidak ditemukan'
      });
    }

    // Delete student (cascade will handle related records)
    await prisma.student.delete({
      where: { id }
    });

    res.json({
      success: true,
      message: 'Siswa berhasil dihapus'
    });

  } catch (error) {
    console.error('Delete student error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat menghapus siswa'
    });
  }
};

/**
 * Get student grades
 * GET /api/students/:id/grades
 */
export const getStudentGrades = async (req, res) => {
  try {
    const { id } = req.params;
    const { subjectId, gradeType } = req.query;

    // Build where clause
    const where = { studentId: id };
    if (subjectId) where.subjectId = subjectId;
    if (gradeType) where.gradeType = gradeType;

    const grades = await prisma.grade.findMany({
      where,
      include: {
        subject: {
          select: {
            id: true,
            name: true,
            code: true
          }
        },
        class: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: { date: 'desc' }
    });

    res.json({
      success: true,
      data: { grades }
    });

  } catch (error) {
    console.error('Get student grades error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat mengambil nilai siswa'
    });
  }
};

/**
 * Get student attendance
 * GET /api/students/:id/attendance
 */
export const getStudentAttendance = async (req, res) => {
  try {
    const { id } = req.params;
    const { startDate, endDate, status } = req.query;

    // Build where clause
    const where = { studentId: id };
    
    if (startDate && endDate) {
      where.date = {
        gte: new Date(startDate),
        lte: new Date(endDate)
      };
    }
    
    if (status) {
      where.status = status;
    }

    const attendance = await prisma.attendance.findMany({
      where,
      include: {
        class: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: { date: 'desc' }
    });

    // Calculate attendance statistics
    const stats = {
      total: attendance.length,
      present: attendance.filter(a => a.status === 'PRESENT').length,
      absent: attendance.filter(a => a.status === 'ABSENT').length,
      late: attendance.filter(a => a.status === 'LATE').length,
      excused: attendance.filter(a => a.status === 'EXCUSED').length
    };

    res.json({
      success: true,
      data: { 
        attendance,
        stats
      }
    });

  } catch (error) {
    console.error('Get student attendance error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat mengambil presensi siswa'
    });
  }
};
