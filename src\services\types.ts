// Types for Guru Digital Pelangi API Services
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  nip?: string;
  role: 'ADMIN' | 'GURU';
  status: 'ACTIVE' | 'INACTIVE';
}

export interface Class {
  id: string;
  name: string;
  subjectId?: string;
  subject?: {
    id: string;
    name: string;
    code: string;
  };
  description?: string;
  studentCount?: number;
  gradeLevel?: string;
  academicYear?: string;
  schoolId?: string;
  classTeachers?: Array<{
    id: string;
    teacherId: string;
    teacher: {
      id: string;
      fullName: string;
      email: string;
    };
  }>;
  teachers?: Array<{
    id: string;
    fullName: string;
    email: string;
  }>;
  school?: {
    name: string;
  };
}

export interface Student {
  id: string;
  studentId: string; // NISN
  fullName: string;
  email?: string;
  classId?: string;
  dateOfBirth?: string;
  gender?: 'L' | 'P';
  address?: string;
  phone?: string;
  parentName?: string;
  parentPhone?: string;
  status: 'ACTIVE' | 'INACTIVE' | 'GRADUATED';
  class?: {
    name: string;
    gradeLevel: string;
  };
  studentXp?: {
    totalXp: number;
    level: number;
    levelName: string;
  };
}

export interface Attendance {
  id: string;
  studentId: string;
  classId: string;
  date: string;
  status: 'PRESENT' | 'ABSENT' | 'LATE' | 'EXCUSED';
  reason?: 'ALPA' | 'IZIN' | 'SAKIT';
  timeIn?: string;
  notes?: string;
  student?: {
    firstName: string;
    lastName: string;
    studentId: string;
  };
  class?: {
    name: string;
  };
}

export interface Grade {
  id: string;
  studentId: string;
  subjectId: string;
  classId: string;
  gradeType: 'TUGAS_HARIAN' | 'QUIZ' | 'ULANGAN_HARIAN' | 'PTS' | 'PAS' | 'PRAKTIK' | 'SIKAP' | 'KETERAMPILAN';
  score: number;
  maxScore: number;
  description?: string;
  date: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  student?: {
    id: string;
    studentId: string;
    fullName: string;
  };
  subject?: {
    id: string;
    name: string;
  };
  class?: {
    id: string;
    name: string;
  };
  createdByUser?: {
    id: string;
    firstName: string;
    lastName: string;
  };
}

export interface StudentXp {
  id: string;
  studentId: string;
  totalXp: number;
  level: number;
  levelName: string;
  attendanceStreak: number;
  assignmentStreak: number;
  lastAttendance?: string;
  lastAssignment?: string;
  updatedAt: string;
  student?: {
    id: string;
    fullName: string;
    studentId: string;
  };
}

export interface Achievement {
  id: string;
  studentId: string;
  type: string;
  title: string;
  description?: string;
  xpReward: number;
  earnedAt: string;
  metadata?: any;
}

export interface GamificationSettings {
  id: string;
  name: string;
  description?: string;
  xpPerGrade: number;
  xpAttendanceBonus: number;
  xpAbsentPenalty: number;
  levelThresholds: Array<{
    level: number;
    name: string;
    xp: number;
  }>;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface School {
  id: string;
  name: string;
  address?: string;
  phone?: string;
  email?: string;
}
