import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Sidebar from './Sidebar';
import DashboardContent from './DashboardContent';
import KelasContent from './KelasContent';
import SiswaContent from './SiswaContent';
import PresensiContent from './PresensiContent';
import ClassManager from './modules/class/ClassManager';
import StudentManager from './modules/student/StudentManager';
import AttendanceManager from './modules/attendance/AttendanceManager';
import GradeManager from './modules/grade/GradeManager';
import GamificationDashboard from './modules/gamification/GamificationDashboard';
import AssignmentManager from './modules/assignment/AssignmentManager';
import SettingsManager from './SettingsManager';

const Dashboard = () => {
  const [activeMenu, setActiveMenu] = useState('dashboard');

  const renderContent = () => {
    switch(activeMenu) {
      case 'dashboard':
        return <DashboardContent />;
      case 'kelas':
        return <ClassManager />;
      case 'siswa':
        return <StudentManager />;
      case 'presensi':
        return <AttendanceManager />;
      case 'nilai':
        return <GradeManager />;
      case 'tugas':
        return <AssignmentManager />;
      case 'rpp':
        return (
          <div className="p-8 text-center">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-4xl">📖</span>
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-2">RPP/Modul Ajar</h3>
            <p className="text-gray-600">Fitur RPP akan segera hadir</p>
          </div>
        );
      case 'bank-soal':
        return (
          <div className="p-8 text-center">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-4xl">❓</span>
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-2">Bank Soal</h3>
            <p className="text-gray-600">Fitur bank soal akan segera hadir</p>
          </div>
        );
      case 'gamifikasi':
        return <GamificationDashboard />;
      case 'pengaturan':
        return <SettingsManager />;
      default:
        return (
          <div className="p-8 text-center">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-4xl">🚧</span>
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-2">Halaman Dalam Pengembangan</h3>
            <p className="text-gray-600">Fitur {activeMenu} sedang dalam tahap pengembangan</p>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Sidebar activeMenu={activeMenu} setActiveMenu={setActiveMenu} />
      <main className="ml-72 min-h-screen overflow-auto">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeMenu}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="min-h-screen"
          >
            {renderContent()}
          </motion.div>
        </AnimatePresence>
      </main>
    </div>
  );
};

export default Dashboard;
