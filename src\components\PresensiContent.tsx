import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Card, 
  CardBody, 
  CardHeader, 
  Button, 
  Input, 
  Select,
  SelectItem,
  Avatar,
  Chip
} from '@heroui/react';

const PresensiContent = () => {
  const [selectedDate, setSelectedDate] = useState('2025-06-12');
  const [selectedKelas, setSelectedKelas] = useState('');
  
  const attendanceStats = [
    { 
      title: 'Total Hadir', 
      value: '33', 
      subtitle: 'Semua data',
      icon: '✅',
      color: 'success'
    },
    { 
      title: 'Sakit', 
      value: '2', 
      subtitle: 'Semua data',
      icon: '💊',
      color: 'primary'
    },
    { 
      title: 'Izin', 
      value: '1', 
      subtitle: 'Semua data',
      icon: '⚠️',
      color: 'warning'
    },
    { 
      title: 'Alfa', 
      value: '1', 
      subtitle: 'Semua data',
      icon: '❌',
      color: 'danger'
    },
    { 
      title: 'Minggu Ini', 
      value: '0', 
      subtitle: 'Semua data',
      icon: '⚡',
      color: 'warning'
    },
    { 
      title: 'Rata-rata Poin', 
      value: '100', 
      subtitle: 'Semua data',
      icon: '🏆',
      color: 'secondary'
    }
  ];

  const kelasOptions = [
    { key: '', label: 'Pilih Kelas...' },
    { key: '9C', label: '9C' },
    { key: '7A', label: '7A' },
    { key: '9E', label: '9E' },
    { key: 'XI PA1', label: 'XI PA1' }
  ];

  return (
    <div className="p-8">
      {/* Header */}
      <div className="flex justify-between items-start mb-8">
        <div className="flex items-center space-x-4">
          <Avatar 
            name="👥"
            className="bg-gradient-to-r from-blue-500 to-purple-500 text-white"
            size="lg"
          />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Presensi Siswa</h1>
            <p className="text-gray-600">Kelola kehadiran siswa dengan mudah</p>
          </div>
        </div>
        <Button 
          color="success" 
          startContent={<span>📤</span>}
        >
          Export
        </Button>
      </div>

      {/* Real-time Stats Header */}
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <Card>
          <CardHeader className="flex justify-between items-center">
            <h3 className="text-xl font-bold text-gray-900">Statistik Presensi Real-Time</h3>
            <Button 
              variant="flat" 
              color="primary" 
              startContent={<span>🔄</span>}
              size="sm"
            >
              Refresh Data
            </Button>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="text-center">
                <Chip color="primary" variant="flat">• Total Records: 37</Chip>
              </div>
              <div className="text-center">
                <Chip color="success" variant="flat">• Tanggal Recorded: 4 hari</Chip>
              </div>
              <div className="text-center">
                <Chip color="secondary" variant="flat">• Siswa Tercatat: 9 siswa</Chip>
              </div>
              <div className="text-center">
                <Chip color="warning" variant="flat">• Periode: 29 Mei - 07 Jun</Chip>
              </div>
            </div>

            {/* Stats Grid */}
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
              {attendanceStats.map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className={`bg-gradient-to-br from-${stat.color}-500 to-${stat.color}-600`}>
                    <CardBody className="p-4 text-white text-center">
                      <div className="text-2xl mb-2">{stat.icon}</div>
                      <h4 className="text-2xl font-bold mb-1">{stat.value}</h4>
                      <p className="text-white/80 text-sm">{stat.title}</p>
                      <p className="text-white/60 text-xs">{stat.subtitle}</p>
                    </CardBody>
                  </Card>
                </motion.div>
              ))}
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Attendance Controls */}
      <Card className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 mb-8">
        <CardBody className="p-6 text-white">
          <h3 className="text-xl font-bold mb-4">📅 Presensi Harian</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
            <div>
              <label className="block text-white/80 text-sm mb-2">Pilih Kelas</label>
              <Select 
                placeholder="Pilih Kelas..."
                selectedKeys={selectedKelas ? [selectedKelas] : []}
                onSelectionChange={(keys) => setSelectedKelas(Array.from(keys)[0] as string)}
                className="bg-white/20 backdrop-blur-sm"
              >
                {kelasOptions.map((kelas) => (
                  <SelectItem key={kelas.key} value={kelas.key}>
                    {kelas.label}
                  </SelectItem>
                ))}
              </Select>
            </div>
            
            <div>
              <label className="block text-white/80 text-sm mb-2">Tanggal</label>
              <div className="flex items-center space-x-2">
                <Button isIconOnly className="bg-white/20 backdrop-blur-sm">📅</Button>
                <Input
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="flex-1 bg-white/20 backdrop-blur-sm"
                />
              </div>
            </div>
            
            <div className="flex space-x-3">
              <Button color="primary" variant="solid">
                Hari Ini
              </Button>
              <Button color="success" variant="solid">
                Semua Hadir
              </Button>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Class Selection Placeholder */}
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <Card>
          <CardBody className="p-12 text-center">
            <Avatar 
              name="📚"
              className="bg-gray-100 text-gray-600 mx-auto mb-6"
              size="lg"
            />
            <h3 className="text-xl font-bold text-gray-900 mb-2">Pilih Kelas untuk Memulai Presensi</h3>
            <p className="text-gray-600 mb-6">Silahkan pilih kelas terlebih dahulu untuk melihat daftar siswa dan melakukan presensi</p>
            <Button color="primary">
              Pilih Kelas
            </Button>
          </CardBody>
        </Card>
      </motion.div>
    </div>
  );
};

export default PresensiContent;
