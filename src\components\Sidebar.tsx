import React from 'react';
import { motion } from 'framer-motion';
import { Button, Avatar, Card, CardBody } from '@heroui/react';

interface SidebarProps {
  activeMenu: string;
  setActiveMenu: (menu: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ activeMenu, setActiveMenu }) => {
  const menuItems = [
    { 
      id: 'dashboard', 
      name: 'Dashboard', 
      icon: '🏠',
      color: 'primary'
    },
    { 
      id: 'kelas', 
      name: '<PERSON><PERSON>', 
      icon: '📚',
      color: 'secondary'
    },
    { 
      id: 'siswa', 
      name: 'Siswa', 
      icon: '👥',
      color: 'success'
    },
    { 
      id: 'presensi', 
      name: 'Presensi', 
      icon: '✅',
      color: 'warning'
    },
    { 
      id: 'tugas', 
      name: 'Tu<PERSON>', 
      icon: '📝',
      color: 'danger'
    },
    { 
      id: 'nilai', 
      name: '<PERSON><PERSON>', 
      icon: '📊',
      color: 'primary'
    },
    { 
      id: 'jurnal', 
      name: 'Jurnal', 
      icon: '📖',
      color: 'secondary'
    },
    { 
      id: 'planner', 
      name: 'Teacher Planner', 
      icon: '📅',
      color: 'success'
    },
    { 
      id: 'rpp', 
      name: 'RPP & Modul', 
      icon: '📋',
      color: 'warning'
    },
    { 
      id: 'bank-soal', 
      name: 'Bank Soal', 
      icon: '🎯',
      color: 'danger'
    }
  ];

  return (
    <motion.div 
      initial={{ x: -300 }}
      animate={{ x: 0 }}
      className="w-72 h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-blue-800 text-white p-6 shadow-2xl"
    >
      <div className="mb-8">
        <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
          Kelas Guru
        </h2>
        <p className="text-gray-300 text-sm">Sistem Administrasi Pembelajaran</p>
      </div>

      <nav className="space-y-2 mb-8">
        {menuItems.map((item) => (
          <Button
            key={item.id}
            variant={activeMenu === item.id ? "solid" : "light"}
            color={activeMenu === item.id ? item.color as any : "default"}
            className={`w-full justify-start h-12 ${
              activeMenu === item.id 
                ? 'shadow-lg' 
                : 'hover:bg-white/10'
            }`}
            startContent={<span className="text-xl">{item.icon}</span>}
            onPress={() => setActiveMenu(item.id)}
          >
            <span className="font-medium">{item.name}</span>
          </Button>
        ))}
      </nav>

      <div className="absolute bottom-6 left-6 right-6">
        <Card className="bg-white/10 backdrop-blur-lg">
          <CardBody className="p-4">
            <div className="flex items-center space-x-3">
              <Avatar 
                name="D"
                className="bg-gradient-to-r from-blue-400 to-purple-400"
              />
              <div>
                <p className="font-semibold text-white">Devi Saidulloh</p>
                <p className="text-gray-300 text-sm">Admin</p>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </motion.div>
  );
};

export default Sidebar;
