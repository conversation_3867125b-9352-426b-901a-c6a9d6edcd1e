import React from 'react';
import { motion } from 'framer-motion';
import { Button, Avatar, Card, CardBody, Dropdown, DropdownTrigger, DropdownMenu, DropdownItem } from '@heroui/react';
import { useAuthStore } from '../stores/authStore';
import { useNavigate } from 'react-router-dom';

interface SidebarProps {
  activeMenu: string;
  setActiveMenu: (menu: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ activeMenu, setActiveMenu }) => {
  const { user, logout } = useAuthStore();
  const navigate = useNavigate();

  const handleLogout = async () => {
    await logout();
    navigate('/login');
  };
  const menuItems = [
    { 
      id: 'dashboard', 
      name: 'Dashboard', 
      icon: '🏠',
      color: 'primary'
    },
    { 
      id: 'kelas', 
      name: 'Kelas', 
      icon: '📚',
      color: 'secondary'
    },
    { 
      id: 'siswa', 
      name: '<PERSON><PERSON><PERSON>', 
      icon: '👥',
      color: 'success'
    },
    { 
      id: 'presensi', 
      name: 'Presensi', 
      icon: '✅',
      color: 'warning'
    },
    { 
      id: 'tugas', 
      name: 'Tugas', 
      icon: '📝',
      color: 'danger'
    },
    { 
      id: 'nilai', 
      name: 'Nilai', 
      icon: '📊',
      color: 'primary'
    },
    { 
      id: 'jurnal', 
      name: 'Jurnal', 
      icon: '📖',
      color: 'secondary'
    },
    { 
      id: 'planner', 
      name: 'Teacher Planner', 
      icon: '📅',
      color: 'success'
    },
    { 
      id: 'rpp', 
      name: 'RPP & Modul', 
      icon: '📋',
      color: 'warning'
    },
    { 
      id: 'bank-soal', 
      name: 'Bank Soal', 
      icon: '🎯',
      color: 'danger'
    }
  ];

  return (
    <motion.div 
      initial={{ x: -300 }}
      animate={{ x: 0 }}
      className="w-72 h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-blue-800 text-white p-6 shadow-2xl"
    >
      <div className="mb-8">
        <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
          Kelas Guru
        </h2>
        <p className="text-gray-300 text-sm">Sistem Administrasi Pembelajaran</p>
      </div>

      <nav className="space-y-2 mb-8">
        {menuItems.map((item) => (
          <Button
            key={item.id}
            variant={activeMenu === item.id ? "solid" : "light"}
            color={activeMenu === item.id ? item.color as any : "default"}
            className={`w-full justify-start h-12 ${
              activeMenu === item.id 
                ? 'shadow-lg' 
                : 'hover:bg-white/10'
            }`}
            startContent={<span className="text-xl">{item.icon}</span>}
            onPress={() => setActiveMenu(item.id)}
          >
            <span className="font-medium">{item.name}</span>
          </Button>
        ))}
      </nav>

      <div className="absolute bottom-6 left-6 right-6">
        <Dropdown placement="top">
          <DropdownTrigger>
            <Card className="bg-white/10 backdrop-blur-lg cursor-pointer hover:bg-white/20 transition-colors">
              <CardBody className="p-4">
                <div className="flex items-center space-x-3">
                  <Avatar
                    name={user?.first_name?.charAt(0) || "U"}
                    className="bg-gradient-to-r from-blue-400 to-purple-400"
                  />
                  <div className="flex-1">
                    <p className="font-semibold text-white">
                      {user?.first_name} {user?.last_name}
                    </p>
                    <p className="text-gray-300 text-sm capitalize">
                      {user?.role}
                      {user?.role === 'siswa' && user?.class_name && ` - ${user.class_name}`}
                    </p>
                  </div>
                  <span className="text-gray-300">⚙️</span>
                </div>
              </CardBody>
            </Card>
          </DropdownTrigger>
          <DropdownMenu aria-label="User menu">
            <DropdownItem key="profile" startContent="👤">
              Profil
            </DropdownItem>
            <DropdownItem key="settings" startContent="⚙️">
              Pengaturan
            </DropdownItem>
            <DropdownItem
              key="logout"
              startContent="🚪"
              color="danger"
              onPress={handleLogout}
            >
              Keluar
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </div>
    </motion.div>
  );
};

export default Sidebar;
