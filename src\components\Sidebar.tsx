import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Button, Avatar, Card, CardBody, Modal, ModalContent, <PERSON>dal<PERSON><PERSON>er, ModalBody, Modal<PERSON>ooter, useDisclosure } from '@heroui/react';
import { useAuthStore } from '../stores/authStore';
import { useNavigate } from 'react-router-dom';
import { LogOut } from 'lucide-react';

interface SidebarProps {
  activeMenu: string;
  setActiveMenu: (menu: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ activeMenu, setActiveMenu }) => {
  const { user, logout } = useAuthStore();
  const navigate = useNavigate();
  const { isOpen, onOpen, onOpenChange } = useDisclosure();

  const handleLogout = async () => {
    await logout();
    navigate('/login');
  };

  const getUserInitial = () => {
    if (user?.firstName) {
      return user.firstName.charAt(0).toUpperCase();
    }
    if (user?.name) {
      return user.name.charAt(0).toUpperCase();
    }
    return 'U';
  };

  const getUserName = () => {
    if (user?.firstName && user?.lastName) {
      return `${user.firstName} ${user.lastName}`;
    }
    if (user?.name) {
      return user.name;
    }
    return 'User';
  };
  const menuItems = [
    { 
      id: 'dashboard', 
      name: 'Dashboard', 
      icon: '🏠',
      color: 'primary'
    },
    { 
      id: 'kelas', 
      name: 'Kelas', 
      icon: '📚',
      color: 'secondary'
    },
    { 
      id: 'siswa', 
      name: 'Siswa', 
      icon: '👥',
      color: 'success'
    },
    { 
      id: 'presensi', 
      name: 'Presensi', 
      icon: '✅',
      color: 'warning'
    },
    { 
      id: 'tugas', 
      name: 'Tugas', 
      icon: '📝',
      color: 'danger'
    },
    {
      id: 'nilai',
      name: 'Nilai',
      icon: '📊',
      color: 'primary'
    },
    {
      id: 'gamifikasi',
      name: 'Gamifikasi',
      icon: '🎮',
      color: 'secondary'
    },
    {
      id: 'jurnal',
      name: 'Jurnal',
      icon: '📖',
      color: 'secondary'
    },
    { 
      id: 'planner', 
      name: 'Teacher Planner', 
      icon: '📅',
      color: 'success'
    },
    { 
      id: 'rpp', 
      name: 'RPP & Modul', 
      icon: '📋',
      color: 'warning'
    },
    { 
      id: 'bank-soal', 
      name: 'Bank Soal', 
      icon: '🎯',
      color: 'danger'
    }
  ];

  return (
    <motion.div
      initial={{ x: -300 }}
      animate={{ x: 0 }}
      className="w-72 h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-blue-800 text-white shadow-2xl flex flex-col fixed left-0 top-0 z-50"
    >
      {/* Header */}
      <div className="p-6 pb-4 flex-shrink-0">
        <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
          Kelas Guru
        </h2>
        <p className="text-gray-300 text-sm">Sistem Administrasi Pembelajaran</p>
      </div>

      {/* Navigation Menu - Fixed height, no scroll */}
      <nav className="flex-1 px-6 space-y-2 flex flex-col justify-start">
        {menuItems.map((item) => (
          <Button
            key={item.id}
            variant={activeMenu === item.id ? "solid" : "light"}
            color={activeMenu === item.id ? item.color as any : "default"}
            className={`w-full justify-start h-11 ${
              activeMenu === item.id
                ? 'shadow-lg'
                : 'hover:bg-white/10'
            }`}
            startContent={<span className="text-lg">{item.icon}</span>}
            onPress={() => setActiveMenu(item.id)}
          >
            <span className="font-medium text-sm">{item.name}</span>
          </Button>
        ))}
      </nav>

      {/* Bottom Section - User Profile + Logout - Fixed at bottom */}
      <div className="p-6 pt-4 space-y-3 flex-shrink-0 mt-auto">
        {/* User Profile Section */}
        <Card className="bg-white/10 backdrop-blur-lg">
          <CardBody className="p-3">
            <div className="flex items-center space-x-3">
              <Avatar
                name={getUserInitial()}
                className="bg-gradient-to-r from-blue-400 to-purple-400 text-white font-semibold"
                size="sm"
              />
              <div className="flex-1">
                <p className="font-semibold text-white text-sm">
                  {getUserName()}
                </p>
                <p className="text-gray-300 text-xs capitalize">
                  {user?.role?.toLowerCase() || 'user'}
                </p>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Logout Button */}
        <Button
          color="danger"
          variant="flat"
          className="w-full justify-start h-11 bg-red-500/20 hover:bg-red-500/30 text-red-200 hover:text-white border border-red-500/30"
          startContent={<LogOut className="w-4 h-4" />}
          onPress={onOpen}
        >
          <span className="font-medium text-sm">Keluar</span>
        </Button>
      </div>

      {/* Logout Confirmation Modal */}
      <Modal isOpen={isOpen} onOpenChange={onOpenChange} placement="center">
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                <div className="flex items-center gap-2">
                  <LogOut className="w-5 h-5 text-red-500" />
                  <span>Konfirmasi Keluar</span>
                </div>
              </ModalHeader>
              <ModalBody>
                <p className="text-gray-600">
                  Apakah Anda yakin ingin keluar dari sistem?
                  Anda akan diarahkan kembali ke halaman login.
                </p>
              </ModalBody>
              <ModalFooter>
                <Button
                  color="default"
                  variant="light"
                  onPress={onClose}
                >
                  Batal
                </Button>
                <Button
                  color="danger"
                  onPress={() => {
                    onClose();
                    handleLogout();
                  }}
                  startContent={<LogOut className="w-4 h-4" />}
                >
                  Ya, Keluar
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </motion.div>
  );
};

export default Sidebar;
