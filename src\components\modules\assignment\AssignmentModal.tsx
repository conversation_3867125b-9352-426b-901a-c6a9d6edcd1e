// Assignment Create/Edit Modal Component - Guru Digital Pelangi
import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON>dal<PERSON>eader,
  ModalBody,
  ModalFooter,
  Button,
  Input,
  Textarea,
  Select,
  SelectItem,
  DatePicker,
  Card,
  CardBody
} from '@heroui/react';
import { FileText, Calendar, Award, Users } from 'lucide-react';
import { assignmentService, classService } from '../../../services/expressApi';

interface AssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  assignment?: any; // For edit mode
}

interface Class {
  id: string;
  name: string;
  subject?: {
    name: string;
  };
}

const AssignmentModal: React.FC<AssignmentModalProps> = ({ 
  isOpen, 
  onClose, 
  onSuccess, 
  assignment 
}) => {
  const [classes, setClasses] = useState<Class[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    instructions: '',
    points: 100,
    deadline: '',
    type: 'TUGAS_HARIAN',
    classId: ''
  });

  const assignmentTypes = [
    { key: 'TUGAS_HARIAN', label: 'Tugas Harian' },
    { key: 'QUIZ', label: 'Quiz' },
    { key: 'ULANGAN_HARIAN', label: 'Ulangan Harian' },
    { key: 'PTS', label: 'PTS' },
    { key: 'PAS', label: 'PAS' },
    { key: 'PRAKTIK', label: 'Praktik' },
    { key: 'PROYEK', label: 'Proyek' }
  ];

  useEffect(() => {
    if (isOpen) {
      loadClasses();
      if (assignment) {
        // Edit mode - populate form with existing data
        setFormData({
          title: assignment.title || '',
          description: assignment.description || '',
          instructions: assignment.instructions || '',
          points: assignment.points || 100,
          deadline: assignment.deadline ? assignment.deadline.split('T')[0] : '',
          type: assignment.type || 'TUGAS_HARIAN',
          classId: assignment.classId || ''
        });
      } else {
        // Create mode - reset form
        setFormData({
          title: '',
          description: '',
          instructions: '',
          points: 100,
          deadline: '',
          type: 'TUGAS_HARIAN',
          classId: ''
        });
      }
    }
  }, [isOpen, assignment]);

  const loadClasses = async () => {
    const response = await classService.getClasses();
    if (response.success && response.data) {
      setClasses(response.data);
    }
  };

  const handleSubmit = async () => {
    if (!formData.title || !formData.deadline || !formData.classId) {
      alert('Judul, deadline, dan kelas wajib diisi!');
      return;
    }

    setIsLoading(true);

    try {
      const submitData = {
        ...formData,
        deadline: new Date(formData.deadline).toISOString(),
        points: parseInt(formData.points.toString())
      };

      let response;
      if (assignment) {
        // Edit mode
        response = await assignmentService.updateAssignment(assignment.id, submitData);
      } else {
        // Create mode
        response = await assignmentService.createAssignment(submitData);
      }

      if (response.success) {
        onSuccess();
        onClose();
      } else {
        alert(response.error || 'Gagal menyimpan tugas');
      }
    } catch (error) {
      console.error('Error saving assignment:', error);
      alert('Terjadi kesalahan saat menyimpan tugas');
    }

    setIsLoading(false);
  };

  const handleClose = () => {
    setFormData({
      title: '',
      description: '',
      instructions: '',
      points: 100,
      deadline: '',
      type: 'TUGAS_HARIAN',
      classId: ''
    });
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="3xl" scrollBehavior="inside">
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader>
              <div className="flex items-center gap-2">
                <FileText className="w-5 h-5 text-blue-600" />
                <span>{assignment ? 'Edit Tugas' : 'Buat Tugas Baru'}</span>
              </div>
            </ModalHeader>
            <ModalBody>
              <div className="space-y-6">
                {/* Basic Information */}
                <Card>
                  <CardBody className="space-y-4">
                    <h4 className="font-semibold text-gray-900 flex items-center gap-2">
                      <FileText className="w-4 h-4" />
                      Informasi Dasar
                    </h4>
                    
                    <Input
                      label="Judul Tugas"
                      placeholder="Masukkan judul tugas"
                      value={formData.title}
                      onChange={(e) => setFormData({...formData, title: e.target.value})}
                      isRequired
                    />

                    <Textarea
                      label="Deskripsi"
                      placeholder="Masukkan deskripsi singkat tugas"
                      value={formData.description}
                      onChange={(e) => setFormData({...formData, description: e.target.value})}
                      minRows={2}
                    />

                    <Textarea
                      label="Instruksi Lengkap"
                      placeholder="Masukkan instruksi detail untuk mengerjakan tugas"
                      value={formData.instructions}
                      onChange={(e) => setFormData({...formData, instructions: e.target.value})}
                      minRows={3}
                    />
                  </CardBody>
                </Card>

                {/* Assignment Settings */}
                <Card>
                  <CardBody className="space-y-4">
                    <h4 className="font-semibold text-gray-900 flex items-center gap-2">
                      <Award className="w-4 h-4" />
                      Pengaturan Tugas
                    </h4>

                    <div className="grid grid-cols-2 gap-4">
                      <Select
                        label="Jenis Tugas"
                        placeholder="Pilih jenis tugas"
                        selectedKeys={formData.type ? [formData.type] : []}
                        onSelectionChange={(keys) => setFormData({...formData, type: Array.from(keys)[0] as string})}
                        isRequired
                      >
                        {assignmentTypes.map((type) => (
                          <SelectItem key={type.key} value={type.key}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </Select>

                      <Input
                        label="Poin Maksimal"
                        type="number"
                        placeholder="100"
                        value={formData.points.toString()}
                        onChange={(e) => setFormData({...formData, points: parseInt(e.target.value) || 100})}
                        endContent={<span className="text-sm text-gray-500">poin</span>}
                        isRequired
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <Select
                        label="Kelas"
                        placeholder="Pilih kelas"
                        selectedKeys={formData.classId ? [formData.classId] : []}
                        onSelectionChange={(keys) => setFormData({...formData, classId: Array.from(keys)[0] as string})}
                        isRequired
                      >
                        {classes.map((cls) => (
                          <SelectItem key={cls.id} value={cls.id}>
                            {cls.name} {cls.subject?.name ? `- ${cls.subject.name}` : ''}
                          </SelectItem>
                        ))}
                      </Select>

                      <Input
                        label="Deadline"
                        type="date"
                        value={formData.deadline}
                        onChange={(e) => setFormData({...formData, deadline: e.target.value})}
                        isRequired
                      />
                    </div>
                  </CardBody>
                </Card>

                {/* Preview */}
                {formData.title && (
                  <Card className="bg-blue-50 border-blue-200">
                    <CardBody>
                      <h4 className="font-semibold text-blue-900 mb-2">Preview</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex items-center gap-2">
                          <FileText className="w-4 h-4 text-blue-600" />
                          <span className="font-medium">{formData.title}</span>
                        </div>
                        {formData.description && (
                          <p className="text-gray-700 ml-6">{formData.description}</p>
                        )}
                        <div className="flex items-center gap-4 ml-6 text-gray-600">
                          <div className="flex items-center gap-1">
                            <Award className="w-3 h-3" />
                            <span>{formData.points} poin</span>
                          </div>
                          {formData.deadline && (
                            <div className="flex items-center gap-1">
                              <Calendar className="w-3 h-3" />
                              <span>{new Date(formData.deadline).toLocaleDateString('id-ID')}</span>
                            </div>
                          )}
                          {formData.classId && (
                            <div className="flex items-center gap-1">
                              <Users className="w-3 h-3" />
                              <span>{classes.find(c => c.id === formData.classId)?.name}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                )}
              </div>
            </ModalBody>
            <ModalFooter>
              <Button variant="light" onPress={handleClose} isDisabled={isLoading}>
                Batal
              </Button>
              <Button 
                color="primary" 
                onPress={handleSubmit}
                isLoading={isLoading}
                startContent={!isLoading ? <FileText className="w-4 h-4" /> : null}
              >
                {isLoading ? 'Menyimpan...' : (assignment ? 'Update Tugas' : 'Buat Tugas')}
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};

export default AssignmentModal;
