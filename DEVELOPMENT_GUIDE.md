# 📚 Guru Digital Pelangi - Development Guide

## 🎯 Project Overview

**Guru Digital Pelangi** adalah sistem administrasi guru modern yang dibangun dengan teknologi terkini untuk memudahkan pengelolaan kelas, siswa, nilai, R<PERSON>, bank soal, dan sistem gamifikasi.

### Tech Stack
- **Frontend**: React 18 + TypeScript + Vite
- **UI Library**: HeroUI + Tailwind CSS
- **State Management**: Zustand
- **Backend**: Directus Headless CMS
- **Database**: PostgreSQL
- **Authentication**: NIP/NISN based login

---

## 🔄 System Workflow

### 1. Authentication Flow
```
User Input (NIP/NISN/Email) → Auto-detect User Type → Validate Credentials → Set User Session → Redirect to Dashboard
```

### 2. User Roles & Permissions
- **Admin**: Full access to all features, user management, system settings
- **Guru**: Manage classes, students, grades, RPP, questions, assign XP/badges
- **Siswa**: View own grades, XP, badges, leaderboard (read-only)

### 3. Core Features Workflow
```
Dashboard → Navigation → Feature Modules → CRUD Operations → Real-time Updates → Gamification Triggers
```

---

## 📋 Development Roadmap (7 Days)

### ✅ **HARI 1: Authentication System** (COMPLETED)
- [x] NIP/NISN login implementation
- [x] Auto user type detection
- [x] Protected routes
- [x] User profile integration
- [x] Demo mode for testing

### 🔄 **HARI 2: Core CRUD + Presensi** (IN PROGRESS)
- [ ] Setup Directus collections
- [ ] Kelas management (CRUD)
- [ ] Siswa management (CRUD)
- [ ] Nilai/Grades system
- [ ] Presensi (Attendance) system
- [ ] Sample data creation

### 📅 **HARI 3: Gamifikasi System**
- [ ] XP manual assignment by guru
- [ ] Level calculation & display
- [ ] Custom task creation by admin
- [ ] XP history tracking
- [ ] Level up notifications

### 🏆 **HARI 4: Badges & Leaderboard**
- [ ] Badge creation & management
- [ ] Badge earning criteria
- [ ] Student badge collection
- [ ] Leaderboard per kelas
- [ ] Real-time ranking updates

### 📝 **HARI 5: Bank Soal**
- [ ] Question types implementation
- [ ] Question bank management
- [ ] Import/Export functionality
- [ ] Fisher-Yates shuffle algorithm
- [ ] Quiz creation tools

### 📖 **HARI 6: RPP/Modul Ajar**
- [ ] Kurikulum Merdeka format
- [ ] RPP editor with templates
- [ ] Component-based RPP builder
- [ ] Auto-save functionality
- [ ] RPP sharing & export

### 🚀 **HARI 7: Polish & Testing**
- [ ] Unit testing implementation
- [ ] UI/UX refinements
- [ ] Performance optimization
- [ ] Documentation completion
- [ ] Deployment preparation

---

## ✅ Development Checklist

### Pre-Development
- [x] Project structure setup
- [x] Dependencies installation
- [x] Environment configuration
- [x] Design system guidelines (HeroUI)

### Authentication Module
- [x] Login form with NIP/NISN support
- [x] User type auto-detection
- [x] Authentication service
- [x] Protected route implementation
- [x] User session management
- [x] Logout functionality

### Backend Setup
- [ ] Directus collections creation
- [ ] Database schema implementation
- [ ] Sample data insertion
- [ ] API endpoints testing
- [ ] Permissions configuration

### Core Features
- [ ] Dashboard with real data
- [ ] Kelas CRUD operations
- [ ] Siswa CRUD operations
- [ ] Nilai/Grades management
- [ ] Presensi system
- [ ] Data validation & error handling

### Gamifikasi Features
- [ ] XP system implementation
- [ ] Level calculation logic
- [ ] Badge system
- [ ] Leaderboard functionality
- [ ] Activity logging

### Advanced Features
- [ ] Bank soal with multiple question types
- [ ] RPP editor with Kurikulum Merdeka format
- [ ] Import/Export functionality
- [ ] Real-time notifications

### 📱 Responsive Design Implementation
**Status**: 🔄 Planned (Desktop-first approach currently implemented)
**Priority**: Medium (after core features completed)
**Estimated Time**: 2-3 hours

#### Current State Analysis:
- ✅ **Mobile Hook Available**: `useIsMobile()` hook ready in `src/hooks/use-mobile.tsx`
- ✅ **Responsive UI Components**: HeroUI sidebar component with mobile support exists
- ❌ **Current Sidebar**: Fixed width (`w-72`) not responsive
- ❌ **Layout**: Main content uses fixed margin (`ml-72`)
- ❌ **Mobile Navigation**: No hamburger menu implemented

#### Implementation Tasks:
- [ ] **Replace Sidebar Component**
  - [ ] Integrate `src/components/ui/sidebar.tsx` (responsive version)
  - [ ] Remove fixed width classes from current sidebar
  - [ ] Implement mobile sheet/drawer pattern

- [ ] **Mobile Navigation**
  - [ ] Add hamburger menu button for mobile
  - [ ] Implement slide-out navigation
  - [ ] Add overlay for mobile menu

- [ ] **Layout Responsiveness**
  - [ ] Update Dashboard.tsx layout for responsive margins
  - [ ] Implement responsive grid for content cards
  - [ ] Adjust typography scales for mobile

- [ ] **Breakpoint Implementation**
  - [ ] Mobile: `< 768px` (existing breakpoint)
  - [ ] Tablet: `768px - 1024px`
  - [ ] Desktop: `> 1024px`

- [ ] **Component Adjustments**
  - [ ] Make data tables horizontally scrollable on mobile
  - [ ] Adjust form layouts for mobile screens
  - [ ] Optimize modal sizes for mobile

#### Testing Requirements:
- [ ] Test on mobile devices (iOS/Android)
- [ ] Test on tablets (iPad/Android tablets)
- [ ] Test responsive breakpoints in browser dev tools
- [ ] Verify touch interactions work properly
- [ ] Test landscape/portrait orientations

#### Files to Modify:
- `src/components/Sidebar.tsx` - Replace with responsive version
- `src/components/Dashboard.tsx` - Update layout logic
- `src/components/modules/*` - Adjust component responsiveness
- `tailwind.config.ts` - Add custom breakpoints if needed

### Testing & Quality
- [ ] Unit tests for services
- [ ] Integration tests for API
- [ ] UI/UX testing
- [ ] Accessibility compliance
- [ ] Performance optimization

### Deployment
- [ ] Build optimization
- [ ] Environment variables setup
- [ ] Cloud deployment configuration
- [ ] Monitoring & logging setup
- [ ] Backup & recovery plan

---

## 🎨 HeroUI Design System Guidelines

### Component Usage
```typescript
// ✅ CORRECT - Using HeroUI components
import { Button, Card, Input, Modal } from '@heroui/react';

// ❌ WRONG - Using custom HTML
<button className="custom-btn">Click me</button>
```

### Color Palette
- **Primary**: Blue tones for main actions
- **Secondary**: Purple tones for secondary actions  
- **Success**: Green for positive feedback
- **Warning**: Orange for warnings
- **Danger**: Red for destructive actions

### Component Patterns
- Always use HeroUI props and APIs
- Follow consistent spacing (p-4, m-2, gap-4)
- Implement proper loading states
- Use HeroUI animations and transitions
- Maintain accessibility standards

---

## 📁 Project Structure

```
src/
├── components/           # Reusable UI components
│   ├── auth/            # Authentication components
│   ├── common/          # Common UI components
│   ├── layout/          # Layout components
│   └── modules/         # Feature-specific components
│       ├── class/       # Kelas management
│       ├── student/     # Siswa management
│       ├── grade/       # Nilai management
│       ├── attendance/  # Presensi system
│       ├── gamification/ # XP, badges, leaderboard
│       ├── questions/   # Bank soal
│       └── rpp/         # RPP/Modul Ajar
├── hooks/               # Custom React hooks
├── services/            # API services
├── stores/              # Zustand state management
├── types/               # TypeScript definitions
├── utils/               # Utility functions
└── pages/               # Page components
```

---

## 🔧 Development Commands

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run ESLint

# Backend (Directus)
cd backend
docker-compose up -d # Start Directus server
```

---

## 📊 Progress Tracking

### Current Status: **HARI 1 COMPLETED** ✅
- Authentication system fully implemented
- Demo mode working for testing
- HeroUI design system established
- Project structure finalized

### Next Milestone: **HARI 2 - Core CRUD + Presensi**
- Setup Directus collections
- Implement CRUD operations
- Create presensi system
- Generate sample data

---

## 🚨 Important Notes

1. **Always use HeroUI components** - No custom HTML elements
2. **Follow TypeScript strictly** - Proper type definitions required
3. **Test on multiple user roles** - Admin, Guru, Siswa
4. **Maintain responsive design** - Mobile-first approach
5. **Document all new features** - Update this guide regularly

---

## 📞 Support & Resources

- **HeroUI Documentation**: https://www.heroui.com/docs/
- **Directus Documentation**: https://docs.directus.io/
- **Project Repository**: Current workspace
- **Backend Setup Guide**: `backend/setup-collections.md`

---

*Last Updated: Day 1 - Authentication System Completed*
