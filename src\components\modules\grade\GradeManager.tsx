// Enhanced Grade Management Component for Guru Digital Pelangi
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Input,
  Select,
  SelectItem,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Chip,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Textarea,
  ButtonGroup,
  Divider
} from '@heroui/react';
import { 
  BookOpen, 
  Plus, 
  Edit, 
  Trash2, 
  Download, 
  Save, 
  Users, 
  TrendingUp,
  Award,
  Target,
  BarChart3
} from 'lucide-react';
import EmptyState from '../../common/EmptyState';
import { gradeService, classService, studentService } from '../../../services/expressApi';

interface GradeRecord {
  id?: string;
  studentId: string;
  subjectId: string;
  classId: string;
  gradeType: 'TUGAS_HARIAN' | 'QUIZ' | 'ULANGAN_HARIAN' | 'PTS' | 'PAS' | 'PRAKTIK' | 'SIKAP' | 'KETERAMPILAN';
  score: number;
  maxScore: number;
  description?: string;
  date: string;
  student?: {
    id: string;
    studentId: string;
    fullName: string;
  };
  subject?: {
    id: string;
    name: string;
  };
  class?: {
    id: string;
    name: string;
  };
}

interface Student {
  id: string;
  studentId: string;
  fullName: string;
  classId?: string;
}

interface Class {
  id: string;
  name: string;
  subject: string;
}

const GradeManager = () => {
  const [selectedClass, setSelectedClass] = useState<string>('');
  const [selectedGradeType, setSelectedGradeType] = useState<string>('');
  const [selectedDate, setSelectedDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [grades, setGrades] = useState<GradeRecord[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [classes, setClasses] = useState<Class[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [selectedGrade, setSelectedGrade] = useState<GradeRecord | null>(null);
  const [editScore, setEditScore] = useState<string>('');
  const [editMaxScore, setEditMaxScore] = useState<string>('');
  const [editDescription, setEditDescription] = useState<string>('');

  // Load classes on component mount
  useEffect(() => {
    loadClasses();
  }, []);

  // Load students when class is selected
  useEffect(() => {
    if (selectedClass) {
      loadStudents();
      loadGrades();
    }
  }, [selectedClass, selectedGradeType, selectedDate]);

  const loadClasses = async () => {
    const response = await classService.getClasses();
    if (response.success && response.data) {
      setClasses(response.data);
    }
  };

  const loadStudents = async () => {
    if (!selectedClass) return;
    
    setIsLoading(true);
    const response = await studentService.getStudents({ classId: selectedClass });
    if (response.success && response.data) {
      setStudents(response.data);
    }
    setIsLoading(false);
  };

  const loadGrades = async () => {
    if (!selectedClass) return;

    setIsLoading(true);
    const params: any = {
      classId: selectedClass,
      date: selectedDate
    };

    if (selectedGradeType) params.gradeType = selectedGradeType;

    const response = await gradeService.getGrades(params);
    if (response.success && response.data) {
      setGrades(response.data);
    }
    setIsLoading(false);
  };

  const handleQuickGrade = async (studentId: string, score: number) => {
    if (!selectedClass || !selectedGradeType) {
      alert('Pilih kelas dan jenis tugas terlebih dahulu');
      return;
    }

    // Get subject from selected class
    const selectedClassData = classes.find(c => c.id === selectedClass);
    if (!selectedClassData) {
      alert('Data kelas tidak ditemukan');
      return;
    }

    setIsSaving(true);
    try {
      const gradeData = {
        studentId,
        subjectId: `subject-${selectedClassData.subject.toLowerCase()}`, // Generate subject ID from class
        classId: selectedClass,
        gradeType: selectedGradeType as any,
        score,
        maxScore: 100,
        description: `${getGradeTypeText(selectedGradeType)} - ${selectedClassData.subject} - ${selectedDate}`,
        date: selectedDate
      };

      const response = await gradeService.createGrade(gradeData);
      if (response.success) {
        loadGrades();
        alert('Nilai berhasil disimpan!');
      } else {
        alert('Error: ' + (response.error || 'Gagal menyimpan nilai'));
      }
    } catch (error) {
      console.error('Error saving grade:', error);
      alert('Terjadi error saat menyimpan nilai');
    }
    setIsSaving(false);
  };

  const handleEditGrade = (grade: GradeRecord) => {
    setSelectedGrade(grade);
    setEditScore(grade.score.toString());
    setEditMaxScore(grade.maxScore.toString());
    setEditDescription(grade.description || '');
    onOpen();
  };

  const handleSaveEdit = async () => {
    if (!selectedGrade) return;

    setIsSaving(true);
    try {
      const updateData = {
        score: parseFloat(editScore),
        maxScore: parseFloat(editMaxScore),
        description: editDescription
      };

      // Note: Update API belum diimplementasi di service, perlu ditambahkan
      alert('Fitur edit akan segera tersedia');
      onClose();
    } catch (error) {
      console.error('Error updating grade:', error);
      alert('Terjadi error saat update nilai');
    }
    setIsSaving(false);
  };

  const getGradeTypeText = (type: string) => {
    const types: Record<string, string> = {
      'TUGAS_HARIAN': 'Tugas Harian',
      'QUIZ': 'Quiz',
      'ULANGAN_HARIAN': 'Ulangan Harian',
      'PTS': 'PTS',
      'PAS': 'PAS',
      'PRAKTIK': 'Praktik',
      'SIKAP': 'Sikap',
      'KETERAMPILAN': 'Keterampilan'
    };
    return types[type] || type;
  };

  const getScoreColor = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100;
    if (percentage >= 90) return 'success';
    if (percentage >= 80) return 'primary';
    if (percentage >= 70) return 'warning';
    return 'danger';
  };

  const getExistingGrade = (studentId: string) => {
    return grades.find(g => g.studentId === studentId);
  };

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card>
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-blue-100 rounded-full">
                  <BookOpen className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold">Manajemen Nilai</h1>
                  <p className="text-gray-600">Kelola nilai siswa per mata pelajaran</p>
                </div>
              </div>
              <Button
                color="primary"
                startContent={<Download className="w-4 h-4" />}
                variant="flat"
              >
                Export Laporan
              </Button>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Filter & Pengaturan</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Select
                label="Pilih Kelas & Mata Pelajaran"
                placeholder="Pilih kelas"
                selectedKeys={selectedClass ? [selectedClass] : []}
                onSelectionChange={(keys) => {
                  const classId = Array.from(keys)[0] as string;
                  setSelectedClass(classId);
                }}
              >
                {classes.map((cls) => (
                  <SelectItem key={cls.id} textValue={`${cls.name} - ${cls.subject}`}>
                    {cls.name} - {cls.subject}
                  </SelectItem>
                ))}
              </Select>

              <Select
                label="Jenis Tugas"
                placeholder="Pilih jenis tugas"
                selectedKeys={selectedGradeType ? [selectedGradeType] : []}
                onSelectionChange={(keys) => {
                  const gradeType = Array.from(keys)[0] as string;
                  setSelectedGradeType(gradeType);
                }}
              >
                <SelectItem key="TUGAS_HARIAN" textValue="Tugas Harian">Tugas Harian</SelectItem>
                <SelectItem key="QUIZ" textValue="Quiz">Quiz</SelectItem>
                <SelectItem key="ULANGAN_HARIAN" textValue="Ulangan Harian">Ulangan Harian</SelectItem>
                <SelectItem key="PTS" textValue="PTS">PTS</SelectItem>
                <SelectItem key="PAS" textValue="PAS">PAS</SelectItem>
                <SelectItem key="PRAKTIK" textValue="Praktik">Praktik</SelectItem>
                <SelectItem key="SIKAP" textValue="Sikap">Sikap</SelectItem>
                <SelectItem key="KETERAMPILAN" textValue="Keterampilan">Keterampilan</SelectItem>
              </Select>

              <Input
                type="date"
                label="Tanggal"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
              />
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Empty State or Grade Input Table */}
      {!selectedClass ? (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card>
            <CardBody>
              <EmptyState
                icon={BarChart3}
                title="Pilih kelas untuk mulai input nilai"
                description="Pilih kelas dan jenis tugas di atas untuk mulai menginput nilai siswa"
                showAction={false}
              />
            </CardBody>
          </Card>
        </motion.div>
      ) : selectedClass && selectedGradeType && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between w-full">
                <h3 className="text-lg font-semibold">
                  Input Nilai - {getGradeTypeText(selectedGradeType)}
                </h3>
                <div className="flex gap-2">
                  <Button
                    color="success"
                    variant="flat"
                    startContent={<Users className="w-4 h-4" />}
                    size="sm"
                  >
                    {students.length} Siswa
                  </Button>
                  <Button
                    color="primary"
                    startContent={<Plus className="w-4 h-4" />}
                    size="sm"
                  >
                    Bulk Input
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardBody>
              <Table aria-label="Grade input table">
                <TableHeader>
                  <TableColumn>NAMA SISWA</TableColumn>
                  <TableColumn>NISN</TableColumn>
                  <TableColumn>NILAI SAAT INI</TableColumn>
                  <TableColumn>INPUT CEPAT</TableColumn>
                  <TableColumn>AKSI</TableColumn>
                </TableHeader>
                <TableBody>
                  {students.map((student) => {
                    const existingGrade = getExistingGrade(student.id);
                    return (
                      <TableRow key={student.id}>
                        <TableCell>
                          <div className="font-medium">{student.fullName}</div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm text-gray-600">{student.studentId}</div>
                        </TableCell>
                        <TableCell>
                          {existingGrade ? (
                            <Chip
                              color={getScoreColor(existingGrade.score, existingGrade.maxScore)}
                              variant="flat"
                            >
                              {existingGrade.score}/{existingGrade.maxScore}
                            </Chip>
                          ) : (
                            <span className="text-gray-400">Belum dinilai</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {!existingGrade && (
                            <ButtonGroup size="sm" variant="flat">
                              <Button
                                color="success"
                                onPress={() => handleQuickGrade(student.id, 100)}
                                isLoading={isSaving}
                              >
                                100
                              </Button>
                              <Button
                                color="primary"
                                onPress={() => handleQuickGrade(student.id, 90)}
                                isLoading={isSaving}
                              >
                                90
                              </Button>
                              <Button
                                color="warning"
                                onPress={() => handleQuickGrade(student.id, 80)}
                                isLoading={isSaving}
                              >
                                80
                              </Button>
                              <Button
                                color="danger"
                                onPress={() => handleQuickGrade(student.id, 70)}
                                isLoading={isSaving}
                              >
                                70
                              </Button>
                            </ButtonGroup>
                          )}
                        </TableCell>
                        <TableCell>
                          {existingGrade ? (
                            <Button
                              size="sm"
                              variant="light"
                              startContent={<Edit className="w-4 h-4" />}
                              onPress={() => handleEditGrade(existingGrade)}
                            >
                              Edit
                            </Button>
                          ) : (
                            <Button
                              size="sm"
                              color="primary"
                              variant="light"
                              startContent={<Plus className="w-4 h-4" />}
                              onPress={() => {
                                // Open custom input modal
                                const selectedClassData = classes.find(c => c.id === selectedClass);
                                setSelectedGrade({
                                  studentId: student.id,
                                  subjectId: `subject-${selectedClassData?.subject.toLowerCase()}`,
                                  classId: selectedClass,
                                  gradeType: selectedGradeType as any,
                                  score: 0,
                                  maxScore: 100,
                                  date: selectedDate,
                                  student
                                });
                                setEditScore('');
                                setEditMaxScore('100');
                                setEditDescription('');
                                onOpen();
                              }}
                            >
                              Input
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </CardBody>
          </Card>
        </motion.div>
      )}

      {/* Edit/Input Grade Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="lg">
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader>
                <h3>
                  {selectedGrade?.id ? 'Edit Nilai' : 'Input Nilai'} - {selectedGrade?.student?.fullName}
                </h3>
              </ModalHeader>
              <ModalBody>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <Input
                      type="number"
                      label="Nilai"
                      placeholder="0-100"
                      value={editScore}
                      onChange={(e) => setEditScore(e.target.value)}
                      min="0"
                      max={editMaxScore}
                    />
                    <Input
                      type="number"
                      label="Nilai Maksimal"
                      placeholder="100"
                      value={editMaxScore}
                      onChange={(e) => setEditMaxScore(e.target.value)}
                      min="1"
                    />
                  </div>

                  <Textarea
                    label="Deskripsi"
                    placeholder="Tambahkan deskripsi tugas (opsional)"
                    value={editDescription}
                    onChange={(e) => setEditDescription(e.target.value)}
                  />

                  {editScore && editMaxScore && (
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Persentase:</span>
                        <Chip
                          color={getScoreColor(parseFloat(editScore), parseFloat(editMaxScore))}
                          variant="flat"
                        >
                          {Math.round((parseFloat(editScore) / parseFloat(editMaxScore)) * 100)}%
                        </Chip>
                      </div>
                    </div>
                  )}
                </div>
              </ModalBody>
              <ModalFooter>
                <Button variant="light" onPress={onClose}>
                  Batal
                </Button>
                <Button
                  color="primary"
                  onPress={selectedGrade?.id ? handleSaveEdit : async () => {
                    if (selectedGrade && editScore && editMaxScore) {
                      await handleQuickGrade(
                        selectedGrade.studentId,
                        parseFloat(editScore)
                      );
                      onClose();
                    }
                  }}
                  isLoading={isSaving}
                  startContent={<Save className="w-4 h-4" />}
                >
                  {selectedGrade?.id ? 'Update' : 'Simpan'} Nilai
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </div>
  );
};

export default GradeManager;
