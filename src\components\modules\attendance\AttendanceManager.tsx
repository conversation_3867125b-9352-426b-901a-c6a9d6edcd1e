// Enhanced Attendance Management Component for Guru Digital Pelangi
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Select,
  SelectItem,
  Chip,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Input,
  Textarea,
  ButtonGroup
} from '@heroui/react';
import { Calendar, Clock, Users, CheckCircle, XCircle, AlertCircle, Download, Save, UserCheck } from 'lucide-react';
import { classService, studentService, attendanceService } from '../../../services/expressApi';

interface AttendanceRecord {
  id?: string;
  studentId: string;
  classId: string;
  date: string;
  status: 'PRESENT' | 'ABSENT' | 'LATE' | 'EXCUSED';
  reason?: 'ALPA' | 'IZIN' | 'SAKIT';
  timeIn?: string;
  notes?: string;
  student: {
    id: string;
    studentId: string;
    fullName: string;
  };
}

interface Student {
  id: string;
  studentId: string;
  fullName: string;
  status: string;
}

const AttendanceManager: React.FC = () => {
  const [selectedClass, setSelectedClass] = useState<string>('');
  const [selectedDate, setSelectedDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [attendanceData, setAttendanceData] = useState<AttendanceRecord[]>([]);
  const [allStudents, setAllStudents] = useState<Student[]>([]);
  const [classes, setClasses] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isAbsentModalOpen, onOpen: onAbsentModalOpen, onClose: onAbsentModalClose } = useDisclosure();
  const [selectedStudent, setSelectedStudent] = useState<any>(null);
  const [selectedStudentForAbsent, setSelectedStudentForAbsent] = useState<Student | null>(null);
  const [editStatus, setEditStatus] = useState<string>('');
  const [editReason, setEditReason] = useState<string>('');
  const [editNotes, setEditNotes] = useState<string>('');
  const [absentReason, setAbsentReason] = useState<'ALPA' | 'IZIN' | 'SAKIT'>('ALPA');
  const [absentNotes, setAbsentNotes] = useState<string>('');

  // Load classes on component mount
  useEffect(() => {
    loadClasses();
  }, []);

  // Load attendance when class or date changes
  useEffect(() => {
    if (selectedClass && selectedDate) {
      loadAttendanceAndStudents();
    }
  }, [selectedClass, selectedDate]);

  const loadClasses = async () => {
    const response = await classService.getClasses();
    if (response.success && response.data) {
      setClasses(response.data);
      if (response.data.length > 0) {
        setSelectedClass(response.data[0].id);
      }
    }
  };

  const loadAttendanceAndStudents = async () => {
    setIsLoading(true);
    try {
      // Load students in the class
      const studentsResponse = await studentService.getStudents({ classId: selectedClass });
      if (studentsResponse.success && studentsResponse.data) {
        setAllStudents(studentsResponse.data);
      }

      // Load existing attendance for the date
      const attendanceResponse = await attendanceService.getAttendance({
        classId: selectedClass,
        startDate: selectedDate,
        endDate: selectedDate
      });

      if (attendanceResponse.success && attendanceResponse.data) {
        setAttendanceData(attendanceResponse.data);
      } else {
        setAttendanceData([]);
      }
    } catch (error) {
      console.error('Error loading data:', error);
    }
    setIsLoading(false);
  };

  const handleQuickMarkAttendance = async (studentId: string, status: 'PRESENT' | 'LATE' | 'EXCUSED') => {
    setIsSaving(true);
    try {
      const attendanceRecord = {
        studentId,
        classId: selectedClass,
        date: selectedDate,
        status,
        timeIn: new Date().toLocaleTimeString('id-ID', { hour: '2-digit', minute: '2-digit' }),
        notes: ''
      };

      const response = await attendanceService.createAttendance(attendanceRecord);
      if (response.success) {
        loadAttendanceAndStudents(); // Refresh data
        alert('Presensi berhasil disimpan!');
      } else {
        alert('Error: ' + (response.error || 'Gagal menyimpan presensi'));
      }
    } catch (error) {
      console.error('Error marking attendance:', error);
      alert('Terjadi error saat menyimpan presensi');
    }
    setIsSaving(false);
  };

  const handleAbsentClick = (student: Student) => {
    setSelectedStudentForAbsent(student);
    setAbsentReason('ALPA');
    setAbsentNotes('');
    onAbsentModalOpen();
  };

  const handleAbsentSubmit = async () => {
    if (!selectedStudentForAbsent) return;

    setIsSaving(true);
    try {
      const attendanceRecord = {
        studentId: selectedStudentForAbsent.id,
        classId: selectedClass,
        date: selectedDate,
        status: 'ABSENT' as const,
        reason: absentReason,
        timeIn: new Date().toLocaleTimeString('id-ID', { hour: '2-digit', minute: '2-digit' }),
        notes: absentNotes
      };

      const response = await attendanceService.createAttendance(attendanceRecord);
      if (response.success) {
        loadAttendanceAndStudents();
        onAbsentModalClose();
        alert('Presensi berhasil disimpan!');
      } else {
        alert('Error: ' + (response.error || 'Gagal menyimpan presensi'));
      }
    } catch (error) {
      console.error('Error marking attendance:', error);
      alert('Terjadi error saat menyimpan presensi');
    }
    setIsSaving(false);
  };

  const handleEditAttendance = async () => {
    if (!selectedStudent) return;

    setIsSaving(true);
    try {
      const updateData: any = {
        status: editStatus,
        notes: editNotes
      };

      if (editStatus === 'ABSENT') {
        updateData.reason = editReason;
      }

      const response = await attendanceService.updateAttendance(selectedStudent.id, updateData);

      if (response.success) {
        loadAttendanceAndStudents();
        onClose();
        alert('Presensi berhasil diupdate!');
      } else {
        alert('Error: ' + (response.error || 'Gagal update presensi'));
      }
    } catch (error) {
      console.error('Error updating attendance:', error);
      alert('Terjadi error saat update presensi');
    }
    setIsSaving(false);
  };

  const handleBulkMarkPresent = async () => {
    if (!confirm('Tandai semua siswa sebagai hadir?')) return;

    setIsSaving(true);
    try {
      const attendanceRecords = allStudents.map(student => ({
        studentId: student.id,
        classId: selectedClass,
        date: selectedDate,
        status: 'PRESENT' as const,
        timeIn: new Date().toLocaleTimeString('id-ID', { hour: '2-digit', minute: '2-digit' }),
        notes: ''
      }));

      const response = await attendanceService.bulkCreateAttendance(attendanceRecords);
      if (response.success) {
        loadAttendanceAndStudents();
        alert('Semua siswa berhasil ditandai hadir!');
      } else {
        alert('Error: ' + (response.error || 'Gagal menyimpan presensi bulk'));
      }
    } catch (error) {
      console.error('Error bulk marking attendance:', error);
      alert('Terjadi error saat menyimpan presensi bulk');
    }
    setIsSaving(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PRESENT': return 'success';
      case 'LATE': return 'warning';
      case 'ABSENT': return 'danger';
      case 'EXCUSED': return 'primary';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PRESENT': return <CheckCircle className="w-4 h-4" />;
      case 'LATE': return <AlertCircle className="w-4 h-4" />;
      case 'ABSENT': return <XCircle className="w-4 h-4" />;
      case 'EXCUSED': return <Clock className="w-4 h-4" />;
      default: return null;
    }
  };

  const getStatusText = (status: string, reason?: string) => {
    switch (status) {
      case 'PRESENT': return 'Hadir';
      case 'LATE': return 'Terlambat';
      case 'ABSENT':
        if (reason) {
          switch (reason) {
            case 'ALPA': return 'Tidak Hadir (Alpa)';
            case 'IZIN': return 'Tidak Hadir (Izin)';
            case 'SAKIT': return 'Tidak Hadir (Sakit)';
            default: return 'Tidak Hadir';
          }
        }
        return 'Tidak Hadir';
      case 'EXCUSED': return 'Izin';
      default: return 'Belum Dicek';
    }
  };

  // Get attendance status for a student
  const getStudentAttendance = (studentId: string) => {
    return attendanceData.find(att => att.studentId === studentId);
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold">Presensi Siswa</h1>
                <p className="text-gray-600">Kelola kehadiran siswa harian</p>
              </div>
            </div>
            <Button
              color="primary"
              startContent={<Download className="w-4 h-4" />}
              variant="flat"
            >
              Export Laporan
            </Button>
          </CardHeader>
        </Card>
      </motion.div>

      {/* Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Select
                label="Pilih Kelas"
                placeholder="Pilih kelas"
                selectedKeys={selectedClass ? [selectedClass] : []}
                onSelectionChange={(keys) => {
                  const classId = Array.from(keys)[0] as string;
                  setSelectedClass(classId);
                }}
              >
                {classes.map((cls) => (
                  <SelectItem key={cls.id} value={cls.id} textValue={`${cls.name} - ${cls.subject}`}>
                    {cls.name} - {cls.subject}
                  </SelectItem>
                ))}
              </Select>

              <Input
                type="date"
                label="Tanggal"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
              />

              <div className="flex items-end gap-2">
                <Button
                  color="primary"
                  className="flex-1"
                  onPress={loadAttendanceAndStudents}
                  isLoading={isLoading}
                >
                  Muat Data
                </Button>
                <Button
                  color="success"
                  variant="flat"
                  onPress={handleBulkMarkPresent}
                  isLoading={isSaving}
                  startContent={<UserCheck className="w-4 h-4" />}
                >
                  Tandai Semua Hadir
                </Button>
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Attendance Table */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <h3 className="text-lg font-semibold">Daftar Presensi - {selectedDate}</h3>
            <Chip color="primary" variant="flat">
              {allStudents.length} Siswa
            </Chip>
          </CardHeader>
          <CardBody>
            <Table aria-label="Attendance table">
              <TableHeader>
                <TableColumn>NAMA SISWA</TableColumn>
                <TableColumn>NISN</TableColumn>
                <TableColumn>STATUS</TableColumn>
                <TableColumn>WAKTU MASUK</TableColumn>
                <TableColumn>CATATAN</TableColumn>
                <TableColumn>AKSI CEPAT</TableColumn>
              </TableHeader>
              <TableBody>
                {allStudents.map((student) => {
                  const attendance = getStudentAttendance(student.id);
                  return (
                    <TableRow key={student.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{student.fullName}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm text-gray-600">{student.studentId}</span>
                      </TableCell>
                      <TableCell>
                        {attendance ? (
                          <Chip
                            color={getStatusColor(attendance.status) as any}
                            variant="flat"
                            startContent={getStatusIcon(attendance.status)}
                          >
                            {getStatusText(attendance.status, attendance.reason)}
                          </Chip>
                        ) : (
                          <Chip color="default" variant="flat">
                            Belum Dicek
                          </Chip>
                        )}
                      </TableCell>
                      <TableCell>
                        {attendance?.timeIn || '-'}
                      </TableCell>
                      <TableCell>
                        <span className="text-sm text-gray-600">
                          {attendance?.notes || '-'}
                        </span>
                      </TableCell>
                      <TableCell>
                        {attendance ? (
                          <Button
                            size="sm"
                            variant="light"
                            onPress={() => {
                              setSelectedStudent(attendance);
                              setEditStatus(attendance.status);
                              setEditReason(attendance.reason || '');
                              setEditNotes(attendance.notes || '');
                              onOpen();
                            }}
                          >
                            Edit
                          </Button>
                        ) : (
                          <ButtonGroup size="sm" variant="flat">
                            <Button
                              color="success"
                              onPress={() => handleQuickMarkAttendance(student.id, 'PRESENT')}
                              isLoading={isSaving}
                            >
                              Hadir
                            </Button>
                            <Button
                              color="warning"
                              onPress={() => handleQuickMarkAttendance(student.id, 'LATE')}
                              isLoading={isSaving}
                            >
                              Terlambat
                            </Button>
                            <Button
                              color="danger"
                              onPress={() => handleAbsentClick(student)}
                              isLoading={isSaving}
                            >
                              Tidak Hadir
                            </Button>
                          </ButtonGroup>
                        )}
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </CardBody>
        </Card>
      </motion.div>

      {/* Edit Attendance Modal */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader>
                <h3>Edit Presensi</h3>
              </ModalHeader>
              <ModalBody>
                {selectedStudent && (
                  <div className="space-y-4">
                    <div>
                      <p className="font-medium">
                        {selectedStudent.student?.fullName}
                      </p>
                      <p className="text-sm text-gray-600">
                        {selectedDate}
                      </p>
                    </div>

                    <Select
                      label="Status Kehadiran"
                      placeholder="Pilih status"
                      selectedKeys={editStatus ? [editStatus] : []}
                      onSelectionChange={(keys) => {
                        const status = Array.from(keys)[0] as string;
                        setEditStatus(status);
                      }}
                    >
                      <SelectItem key="PRESENT" value="PRESENT" textValue="Hadir">
                        Hadir
                      </SelectItem>
                      <SelectItem key="LATE" value="LATE" textValue="Terlambat">
                        Terlambat
                      </SelectItem>
                      <SelectItem key="ABSENT" value="ABSENT" textValue="Tidak Hadir">
                        Tidak Hadir
                      </SelectItem>
                      <SelectItem key="EXCUSED" value="EXCUSED" textValue="Izin">
                        Izin
                      </SelectItem>
                    </Select>

                    {editStatus === 'ABSENT' && (
                      <Select
                        label="Alasan Tidak Hadir"
                        placeholder="Pilih alasan"
                        selectedKeys={editReason ? [editReason] : []}
                        onSelectionChange={(keys) => {
                          const reason = Array.from(keys)[0] as string;
                          setEditReason(reason);
                        }}
                      >
                        <SelectItem key="ALPA" textValue="Alpa">
                          Alpa (Tanpa Keterangan)
                        </SelectItem>
                        <SelectItem key="IZIN" textValue="Izin">
                          Izin
                        </SelectItem>
                        <SelectItem key="SAKIT" textValue="Sakit">
                          Sakit
                        </SelectItem>
                      </Select>
                    )}

                    <Textarea
                      label="Catatan"
                      placeholder="Tambahkan catatan (opsional)"
                      value={editNotes}
                      onChange={(e) => setEditNotes(e.target.value)}
                    />
                  </div>
                )}
              </ModalBody>
              <ModalFooter>
                <Button variant="light" onPress={onClose}>
                  Batal
                </Button>
                <Button
                  color="primary"
                  onPress={handleEditAttendance}
                  isLoading={isSaving}
                  startContent={<Save className="w-4 h-4" />}
                >
                  Simpan
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>

      {/* Absent Reason Modal */}
      <Modal isOpen={isAbsentModalOpen} onClose={onAbsentModalClose}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader>
                <h3>Tandai Tidak Hadir</h3>
              </ModalHeader>
              <ModalBody>
                {selectedStudentForAbsent && (
                  <div className="space-y-4">
                    <div>
                      <p className="font-medium">
                        {selectedStudentForAbsent.fullName}
                      </p>
                      <p className="text-sm text-gray-600">
                        {selectedDate}
                      </p>
                    </div>

                    <div>
                      <p className="text-sm font-medium mb-2">Alasan Tidak Hadir:</p>
                      <div className="space-y-2">
                        <label className="flex items-center space-x-2">
                          <input
                            type="radio"
                            name="absentReason"
                            value="ALPA"
                            checked={absentReason === 'ALPA'}
                            onChange={(e) => setAbsentReason(e.target.value as 'ALPA')}
                            className="text-danger"
                          />
                          <span>Alpa (Tanpa Keterangan)</span>
                        </label>
                        <label className="flex items-center space-x-2">
                          <input
                            type="radio"
                            name="absentReason"
                            value="IZIN"
                            checked={absentReason === 'IZIN'}
                            onChange={(e) => setAbsentReason(e.target.value as 'IZIN')}
                            className="text-primary"
                          />
                          <span>Izin</span>
                        </label>
                        <label className="flex items-center space-x-2">
                          <input
                            type="radio"
                            name="absentReason"
                            value="SAKIT"
                            checked={absentReason === 'SAKIT'}
                            onChange={(e) => setAbsentReason(e.target.value as 'SAKIT')}
                            className="text-warning"
                          />
                          <span>Sakit</span>
                        </label>
                      </div>
                    </div>

                    <Textarea
                      label="Keterangan"
                      placeholder="Tambahkan keterangan (opsional)"
                      value={absentNotes}
                      onChange={(e) => setAbsentNotes(e.target.value)}
                    />
                  </div>
                )}
              </ModalBody>
              <ModalFooter>
                <Button variant="light" onPress={onClose}>
                  Batal
                </Button>
                <Button
                  color="danger"
                  onPress={handleAbsentSubmit}
                  isLoading={isSaving}
                  startContent={<Save className="w-4 h-4" />}
                >
                  Tandai Tidak Hadir
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </div>
  );
};

export default AttendanceManager;
