// Attendance Management Component for Guru Digital Pelangi
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Card, 
  CardBody, 
  CardHeader, 
  Button, 
  Table, 
  TableHeader, 
  TableColumn, 
  TableBody, 
  TableRow, 
  TableCell,
  Select,
  SelectItem,
  Chip,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Input,
  Textarea,
  DatePicker
} from '@heroui/react';
import { Calendar, Clock, Users, CheckCircle, XCircle, AlertCircle, Download } from 'lucide-react';
import { attendanceService, classService } from '../../../services/directus';

interface AttendanceRecord {
  id: string;
  student_id: string;
  class_id: string;
  date: string;
  status: 'present' | 'absent' | 'late' | 'excused';
  time_in?: string;
  notes?: string;
  student: {
    first_name: string;
    last_name: string;
  };
}

const AttendanceManager: React.FC = () => {
  const [selectedClass, setSelectedClass] = useState<string>('');
  const [selectedDate, setSelectedDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [attendanceData, setAttendanceData] = useState<AttendanceRecord[]>([]);
  const [classes, setClasses] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [selectedStudent, setSelectedStudent] = useState<any>(null);

  // Load classes on component mount
  useEffect(() => {
    loadClasses();
  }, []);

  // Load attendance when class or date changes
  useEffect(() => {
    if (selectedClass && selectedDate) {
      loadAttendance();
    }
  }, [selectedClass, selectedDate]);

  const loadClasses = async () => {
    const response = await classService.getClasses();
    if (response.success && response.data) {
      setClasses(response.data);
      if (response.data.length > 0) {
        setSelectedClass(response.data[0].id);
      }
    }
  };

  const loadAttendance = async () => {
    setIsLoading(true);
    const response = await attendanceService.getAttendance(selectedClass, selectedDate);
    if (response.success && response.data) {
      setAttendanceData(response.data);
    }
    setIsLoading(false);
  };

  const handleMarkAttendance = async (studentId: string, status: string, notes?: string) => {
    const attendanceRecord = {
      student_id: studentId,
      class_id: selectedClass,
      date: selectedDate,
      status,
      time_in: new Date().toLocaleTimeString('id-ID', { hour: '2-digit', minute: '2-digit' }),
      notes: notes || ''
    };

    const response = await attendanceService.markAttendance(attendanceRecord);
    if (response.success) {
      loadAttendance(); // Refresh data
      onClose();
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'present': return 'success';
      case 'late': return 'warning';
      case 'absent': return 'danger';
      case 'excused': return 'primary';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'present': return <CheckCircle className="w-4 h-4" />;
      case 'late': return <AlertCircle className="w-4 h-4" />;
      case 'absent': return <XCircle className="w-4 h-4" />;
      case 'excused': return <Clock className="w-4 h-4" />;
      default: return null;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'present': return 'Hadir';
      case 'late': return 'Terlambat';
      case 'absent': return 'Tidak Hadir';
      case 'excused': return 'Izin';
      default: return 'Belum Dicek';
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold">Presensi Siswa</h1>
                <p className="text-gray-600">Kelola kehadiran siswa harian</p>
              </div>
            </div>
            <Button
              color="primary"
              startContent={<Download className="w-4 h-4" />}
              variant="flat"
            >
              Export Laporan
            </Button>
          </CardHeader>
        </Card>
      </motion.div>

      {/* Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Select
                label="Pilih Kelas"
                placeholder="Pilih kelas"
                selectedKeys={selectedClass ? [selectedClass] : []}
                onSelectionChange={(keys) => {
                  const classId = Array.from(keys)[0] as string;
                  setSelectedClass(classId);
                }}
              >
                {classes.map((cls) => (
                  <SelectItem key={cls.id} value={cls.id}>
                    {cls.name} - {cls.grade_level}
                  </SelectItem>
                ))}
              </Select>

              <Input
                type="date"
                label="Tanggal"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
              />

              <div className="flex items-end">
                <Button
                  color="primary"
                  className="w-full"
                  onPress={loadAttendance}
                  isLoading={isLoading}
                >
                  Muat Data Presensi
                </Button>
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Attendance Table */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Daftar Presensi</h3>
          </CardHeader>
          <CardBody>
            <Table aria-label="Attendance table">
              <TableHeader>
                <TableColumn>NAMA SISWA</TableColumn>
                <TableColumn>STATUS</TableColumn>
                <TableColumn>WAKTU MASUK</TableColumn>
                <TableColumn>CATATAN</TableColumn>
                <TableColumn>AKSI</TableColumn>
              </TableHeader>
              <TableBody>
                {attendanceData.map((record) => (
                  <TableRow key={record.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">
                          {record.student.first_name} {record.student.last_name}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Chip
                        color={getStatusColor(record.status) as any}
                        variant="flat"
                        startContent={getStatusIcon(record.status)}
                      >
                        {getStatusText(record.status)}
                      </Chip>
                    </TableCell>
                    <TableCell>
                      {record.time_in || '-'}
                    </TableCell>
                    <TableCell>
                      <span className="text-sm text-gray-600">
                        {record.notes || '-'}
                      </span>
                    </TableCell>
                    <TableCell>
                      <Button
                        size="sm"
                        variant="light"
                        onPress={() => {
                          setSelectedStudent(record);
                          onOpen();
                        }}
                      >
                        Edit
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardBody>
        </Card>
      </motion.div>

      {/* Edit Attendance Modal */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader>
                <h3>Edit Presensi</h3>
              </ModalHeader>
              <ModalBody>
                {selectedStudent && (
                  <div className="space-y-4">
                    <div>
                      <p className="font-medium">
                        {selectedStudent.student.first_name} {selectedStudent.student.last_name}
                      </p>
                      <p className="text-sm text-gray-600">
                        {selectedDate}
                      </p>
                    </div>

                    <Select
                      label="Status Kehadiran"
                      placeholder="Pilih status"
                      defaultSelectedKeys={[selectedStudent.status]}
                    >
                      <SelectItem key="present" value="present">
                        Hadir
                      </SelectItem>
                      <SelectItem key="late" value="late">
                        Terlambat
                      </SelectItem>
                      <SelectItem key="absent" value="absent">
                        Tidak Hadir
                      </SelectItem>
                      <SelectItem key="excused" value="excused">
                        Izin
                      </SelectItem>
                    </Select>

                    <Textarea
                      label="Catatan"
                      placeholder="Tambahkan catatan (opsional)"
                      defaultValue={selectedStudent.notes}
                    />
                  </div>
                )}
              </ModalBody>
              <ModalFooter>
                <Button variant="light" onPress={onClose}>
                  Batal
                </Button>
                <Button
                  color="primary"
                  onPress={() => {
                    // Handle save attendance
                    onClose();
                  }}
                >
                  Simpan
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </div>
  );
};

export default AttendanceManager;
