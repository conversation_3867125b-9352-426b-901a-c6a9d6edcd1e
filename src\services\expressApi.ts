// Enhanced Express API Service for Guru Digital Pelangi
// Main entry point that re-exports all modular services
// This file maintains backward compatibility while using the new modular structure

// Re-export everything from the modular services
export * from './types';
export * from './apiClient';
export * from './authService';
export * from './classService';
export * from './studentService';
export * from './attendanceService';
export * from './gradeService';
export * from './gamificationService';
export * from './assignmentService';
export * from './schoolService';
export * from './dashboardService';

// Import services for default export (backward compatibility)
import { authService } from './authService';
import { classService } from './classService';
import { studentService } from './studentService';
import { attendanceService } from './attendanceService';
import { gradeService } from './gradeService';
import { gamificationService } from './gamificationService';
import { assignmentService } from './assignmentService';
import { schoolService } from './schoolService';
import { dashboardService } from './dashboardService';

// Default export for backward compatibility
const expressApi = {
  authService,
  classService,
  studentService,
  attendanceService,
  gradeService,
  gamificationService,
  assignmentService,
  schoolService,
  dashboardService
};

export default expressApi;
