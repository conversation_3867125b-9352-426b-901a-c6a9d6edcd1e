// Enhanced Express API Service for Guru Digital Pelangi
// Handles all API communications with Express backend
import axios from 'axios';

// Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  nip?: string;
  role: 'ADMIN' | 'GURU';
  status: 'ACTIVE' | 'INACTIVE';
}

export interface Class {
  id: string;
  name: string;
  subject?: string;
  start_time?: string;
  end_time?: string;
  description?: string;
  studentCount?: number;
  gradeLevel?: string;
  academicYear?: string;
  teacherId?: string;
  schoolId?: string;
  teacher?: {
    firstName: string;
    lastName: string;
  };
  school?: {
    name: string;
  };
}

export interface Student {
  id: string;
  studentId: string; // NISN
  fullName: string;
  email?: string;
  classId?: string;
  dateOfBirth?: string;
  gender?: 'L' | 'P';
  address?: string;
  phone?: string;
  parentName?: string;
  parentPhone?: string;
  status: 'ACTIVE' | 'INACTIVE' | 'GRADUATED';
  class?: {
    name: string;
    gradeLevel: string;
  };
  studentXp?: {
    totalXp: number;
    level: number;
    levelName: string;
  };
}

export interface Attendance {
  id: string;
  studentId: string;
  classId: string;
  date: string;
  status: 'PRESENT' | 'ABSENT' | 'LATE' | 'EXCUSED';
  reason?: 'ALPA' | 'IZIN' | 'SAKIT';
  timeIn?: string;
  notes?: string;
  student?: {
    firstName: string;
    lastName: string;
    studentId: string;
  };
  class?: {
    name: string;
  };
}

export interface Grade {
  id: string;
  studentId: string;
  subjectId: string;
  classId: string;
  gradeType: 'TUGAS_HARIAN' | 'QUIZ' | 'ULANGAN_HARIAN' | 'PTS' | 'PAS' | 'PRAKTIK' | 'SIKAP' | 'KETERAMPILAN';
  score: number;
  maxScore: number;
  description?: string;
  date: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  student?: {
    id: string;
    studentId: string;
    fullName: string;
  };
  subject?: {
    id: string;
    name: string;
  };
  class?: {
    id: string;
    name: string;
  };
  createdByUser?: {
    id: string;
    firstName: string;
    lastName: string;
  };
}

export interface StudentXp {
  id: string;
  studentId: string;
  totalXp: number;
  level: number;
  levelName: string;
  attendanceStreak: number;
  assignmentStreak: number;
  lastAttendance?: string;
  lastAssignment?: string;
  updatedAt: string;
  student?: {
    id: string;
    fullName: string;
    studentId: string;
  };
}

export interface Achievement {
  id: string;
  studentId: string;
  type: string;
  title: string;
  description?: string;
  xpReward: number;
  earnedAt: string;
  metadata?: any;
}

export interface GamificationSettings {
  id: string;
  name: string;
  description?: string;
  xpPerGrade: number;
  xpAttendanceBonus: number;
  xpAbsentPenalty: number;
  levelThresholds: Array<{
    level: number;
    name: string;
    xp: number;
  }>;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface School {
  id: string;
  name: string;
  address?: string;
  phone?: string;
  email?: string;
}

// API client configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Helper function for auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('auth_token');
  return {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  };
};

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor untuk menambahkan token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    console.log('🔧 ExpressAPI: Request interceptor - Token:', token ? token.substring(0, 20) + '...' : 'No token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      console.log('🔧 ExpressAPI: Authorization header set');
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor untuk handle errors
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired atau invalid
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_data');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Demo mode flag
const DEMO_MODE = import.meta.env.VITE_DEMO_MODE === 'true';
console.log('🔧 ExpressAPI: Demo mode:', DEMO_MODE, 'ENV:', import.meta.env.VITE_DEMO_MODE);

// Authentication service
export const authService = {
  async login(identifier: string, password: string): Promise<ApiResponse<{ user: User; token: string }>> {
    try {
      if (DEMO_MODE) {
        // Demo credentials
        const demoCredentials = [
          {
            identifier: '<EMAIL>',
            password: 'admin123',
            user: {
              id: 'admin-1',
              firstName: 'Admin',
              lastName: 'System',
              email: '<EMAIL>',
              role: 'ADMIN' as const,
              status: 'ACTIVE' as const,
            },
            token: 'demo-admin-token'
          },
          {
            identifier: '<EMAIL>',
            password: 'guru123',
            user: {
              id: 'guru-1',
              firstName: 'Budi',
              lastName: 'Santoso',
              email: '<EMAIL>',
              nip: '198501012010011001',
              role: 'GURU' as const,
              status: 'ACTIVE' as const,
            },
            token: 'demo-guru-token'
          },
          {
            identifier: '1234567890',
            password: '1234567890',
            user: {
              id: 'siswa-1',
              firstName: 'Ahmad',
              lastName: 'Wijaya',
              email: '<EMAIL>',
              role: 'SISWA' as any,
              status: 'ACTIVE' as const,
            },
            token: 'demo-siswa-token'
          }
        ];

        const credential = demoCredentials.find(
          cred => cred.identifier === identifier && cred.password === password
        );

        if (!credential) {
          return {
            success: false,
            error: 'NIP/NISN/Email atau password salah'
          };
        }

        return {
          success: true,
          data: {
            user: credential.user as User,
            token: credential.token
          },
          message: 'Login berhasil (Demo Mode)'
        };
      }

      // Real API call
      const response = await apiClient.post('/auth/login', {
        identifier,
        password
      });

      console.log('🔧 ExpressAPI: Login response data:', response.data);

      // Store token
      if (response.data.data?.token) {
        localStorage.setItem('auth_token', response.data.data.token);
        console.log('🔧 ExpressAPI: Token stored:', response.data.data.token.substring(0, 20) + '...');
      }

      return {
        success: true,
        data: response.data.data,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Login gagal'
      };
    }
  },

  async getProfile(): Promise<ApiResponse<User>> {
    try {
      if (DEMO_MODE) {
        const userData = localStorage.getItem('user_data');
        if (userData) {
          return {
            success: true,
            data: JSON.parse(userData)
          };
        }
        return {
          success: false,
          error: 'User data not found'
        };
      }

      const response = await apiClient.get('/auth/profile');
      return {
        success: true,
        data: response.data.data.user
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal mengambil profile'
      };
    }
  },

  async logout(): Promise<void> {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_data');
  }
};

// Classes service
export const classService = {
  async getClasses(params?: { page?: number; limit?: number; search?: string }): Promise<ApiResponse<Class[]>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: [
            {
              id: 'class-1',
              name: 'Matematika Kelas 9C',
              subject: 'Matematika',
              start_time: '07:00',
              end_time: '08:30',
              description: 'Kelas matematika untuk siswa kelas 9C dengan fokus pada aljabar dan geometri',
              studentCount: 25
            },
            {
              id: 'class-2',
              name: 'Fisika Kelas 11A',
              subject: 'Fisika',
              start_time: '08:30',
              end_time: '10:00',
              description: 'Kelas fisika untuk siswa kelas 11A dengan praktikum laboratorium',
              studentCount: 23
            },
            {
              id: 'class-3',
              name: 'Bahasa Indonesia Kelas 7A',
              subject: 'Bahasa Indonesia',
              start_time: '10:15',
              end_time: '11:45',
              description: 'Kelas bahasa Indonesia dengan fokus pada kemampuan menulis dan berbicara',
              studentCount: 28
            }
          ]
        };
      }

      const response = await apiClient.get('/classes', { params });
      return {
        success: true,
        data: response.data.data.classes,
        pagination: response.data.data.pagination
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal mengambil data kelas'
      };
    }
  },

  async createClass(data: Omit<Class, 'id' | 'studentCount'>): Promise<ApiResponse<Class>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: { id: 'new-class', studentCount: 0, ...data },
          message: 'Kelas berhasil dibuat (Demo Mode)'
        };
      }

      const response = await apiClient.post('/classes', data);
      return {
        success: true,
        data: response.data.data.class,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal membuat kelas'
      };
    }
  },

  async updateClass(id: string, data: Partial<Class>): Promise<ApiResponse<Class>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: { id, ...data } as Class,
          message: 'Kelas berhasil diupdate (Demo Mode)'
        };
      }

      const response = await apiClient.put(`/classes/${id}`, data);
      return {
        success: true,
        data: response.data.data.class,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal update kelas'
      };
    }
  },

  async deleteClass(id: string): Promise<ApiResponse<void>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          message: 'Kelas berhasil dihapus (Demo Mode)'
        };
      }

      const response = await apiClient.delete(`/classes/${id}`);
      return {
        success: true,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal menghapus kelas'
      };
    }
  }
};

// Students service
export const studentService = {
  async getStudents(params?: { page?: number; limit?: number; search?: string; classId?: string }): Promise<ApiResponse<Student[]>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: [
            {
              id: 'student-1',
              studentId: '1234567890',
              firstName: 'Ahmad',
              lastName: 'Wijaya',
              email: '<EMAIL>',
              classId: 'class-10a',
              gender: 'L',
              status: 'ACTIVE',
              class: {
                name: '10A',
                gradeLevel: '10'
              },
              studentXp: {
                totalXp: 150,
                level: 2,
                levelName: 'Pelajar'
              }
            },
            {
              id: 'student-2',
              studentId: '1234567891',
              firstName: 'Sari',
              lastName: 'Dewi',
              email: '<EMAIL>',
              classId: 'class-10a',
              gender: 'P',
              status: 'ACTIVE',
              class: {
                name: '10A',
                gradeLevel: '10'
              },
              studentXp: {
                totalXp: 200,
                level: 3,
                levelName: 'Cendekiawan'
              }
            }
          ]
        };
      }

      const response = await apiClient.get('/students', { params });
      return {
        success: true,
        data: response.data.data.students,
        pagination: response.data.data.pagination
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal mengambil data siswa'
      };
    }
  },

  async createStudent(data: Omit<Student, 'id' | 'studentXp'>): Promise<ApiResponse<Student>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: { id: 'new-student', ...data } as Student,
          message: 'Siswa berhasil dibuat (Demo Mode)'
        };
      }

      const response = await apiClient.post('/students', data);
      return {
        success: true,
        data: response.data.data.student,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal membuat siswa'
      };
    }
  },

  async updateStudent(id: string, data: Partial<Student>): Promise<ApiResponse<Student>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: { id, ...data } as Student,
          message: 'Siswa berhasil diupdate (Demo Mode)'
        };
      }

      const response = await apiClient.put(`/students/${id}`, data);
      return {
        success: true,
        data: response.data.data.student,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal update siswa'
      };
    }
  },

  async deleteStudent(id: string): Promise<ApiResponse<void>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          message: 'Siswa berhasil dihapus (Demo Mode)'
        };
      }

      const response = await apiClient.delete(`/students/${id}`);
      return {
        success: true,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal menghapus siswa'
      };
    }
  },

  // Bulk import students
  bulkImportStudents: async (students: any[]) => {
    try {
      const response = await fetch(`${API_BASE_URL}/students/bulk`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify({ students }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error bulk importing students:', error);
      return { success: false, error: 'Gagal melakukan bulk import siswa' };
    }
  }
};

// Attendance service
export const attendanceService = {
  async getAttendance(params?: {
    page?: number;
    limit?: number;
    classId?: string;
    studentId?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<ApiResponse<Attendance[]>> {
    try {
      if (DEMO_MODE) {
        const today = new Date().toISOString().split('T')[0];
        return {
          success: true,
          data: [
            {
              id: 'attendance-1',
              studentId: 'student-1',
              classId: 'class-10a',
              date: today,
              status: 'PRESENT',
              timeIn: '07:30',
              student: {
                firstName: 'Ahmad',
                lastName: 'Wijaya',
                studentId: '1234567890'
              },
              class: {
                name: '10A'
              }
            },
            {
              id: 'attendance-2',
              studentId: 'student-2',
              classId: 'class-10a',
              date: today,
              status: 'LATE',
              timeIn: '07:45',
              student: {
                firstName: 'Sari',
                lastName: 'Dewi',
                studentId: '1234567891'
              },
              class: {
                name: '10A'
              }
            }
          ]
        };
      }

      const response = await apiClient.get('/attendance', { params });
      return {
        success: true,
        data: response.data.data.attendance,
        pagination: response.data.data.pagination
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal mengambil data presensi'
      };
    }
  },

  async createAttendance(data: Omit<Attendance, 'id' | 'student' | 'class'>): Promise<ApiResponse<Attendance>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: { id: 'new-attendance', ...data } as Attendance,
          message: 'Presensi berhasil dibuat (Demo Mode)'
        };
      }

      const response = await apiClient.post('/attendance', data);
      return {
        success: true,
        data: response.data.data.attendance,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal membuat presensi'
      };
    }
  },

  async bulkCreateAttendance(attendanceRecords: Omit<Attendance, 'id' | 'student' | 'class'>[]): Promise<ApiResponse<any>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: { successful: attendanceRecords.length, failed: 0 },
          message: `${attendanceRecords.length} presensi berhasil dibuat (Demo Mode)`
        };
      }

      const response = await apiClient.post('/attendance/bulk', { attendanceRecords });
      return {
        success: true,
        data: response.data.data,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal membuat presensi bulk'
      };
    }
  },

  async updateAttendance(id: string, data: Partial<Attendance>): Promise<ApiResponse<Attendance>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: { id, ...data } as Attendance,
          message: 'Presensi berhasil diupdate (Demo Mode)'
        };
      }

      const response = await apiClient.put(`/attendance/${id}`, data);
      return {
        success: true,
        data: response.data.data.attendance,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal update presensi'
      };
    }
  },

  async deleteAttendance(id: string): Promise<ApiResponse<void>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          message: 'Presensi berhasil dihapus (Demo Mode)'
        };
      }

      const response = await apiClient.delete(`/attendance/${id}`);
      return {
        success: true,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal menghapus presensi'
      };
    }
  }
};

// Schools service
export const schoolService = {
  async getSchools(params?: { page?: number; limit?: number; search?: string }): Promise<ApiResponse<School[]>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: [
            {
              id: 'school-1',
              name: 'SMA Digital Pelangi',
              address: 'Jl. Pendidikan No. 123, Jakarta Selatan',
              phone: '021-12345678',
              email: '<EMAIL>'
            }
          ]
        };
      }

      const response = await apiClient.get('/schools', { params });
      return {
        success: true,
        data: response.data.data.schools,
        pagination: response.data.data.pagination
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal mengambil data sekolah'
      };
    }
  },

  async createSchool(data: Omit<School, 'id'>): Promise<ApiResponse<School>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: { id: 'new-school', ...data },
          message: 'Sekolah berhasil dibuat (Demo Mode)'
        };
      }

      const response = await apiClient.post('/schools', data);
      return {
        success: true,
        data: response.data.data.school,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal membuat sekolah'
      };
    }
  },

  async updateSchool(id: string, data: Partial<School>): Promise<ApiResponse<School>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: { id, ...data } as School,
          message: 'Sekolah berhasil diupdate (Demo Mode)'
        };
      }

      const response = await apiClient.put(`/schools/${id}`, data);
      return {
        success: true,
        data: response.data.data.school,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal update sekolah'
      };
    }
  },

  async deleteSchool(id: string): Promise<ApiResponse<void>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          message: 'Sekolah berhasil dihapus (Demo Mode)'
        };
      }

      const response = await apiClient.delete(`/schools/${id}`);
      return {
        success: true,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal menghapus sekolah'
      };
    }
  }
};

// Grades service
export const gradeService = {
  async getGrades(params?: {
    page?: number;
    limit?: number;
    classId?: string;
    subjectId?: string;
    studentId?: string;
    gradeType?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<ApiResponse<Grade[]>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: [
            {
              id: 'grade-1',
              studentId: 'student-1',
              subjectId: 'subject-1',
              classId: 'class-10a',
              gradeType: 'QUIZ',
              score: 85,
              maxScore: 100,
              description: 'Quiz Matematika Bab 1',
              date: '2025-06-15',
              createdBy: 'guru-1',
              createdAt: '2025-06-15T10:00:00Z',
              updatedAt: '2025-06-15T10:00:00Z',
              student: {
                id: 'student-1',
                studentId: '1234567890',
                fullName: 'Ahmad Wijaya'
              },
              subject: {
                id: 'subject-1',
                name: 'Matematika'
              },
              class: {
                id: 'class-10a',
                name: '10A'
              }
            }
          ]
        };
      }

      const response = await apiClient.get('/grades', { params });
      return {
        success: true,
        data: response.data.data.grades,
        pagination: response.data.data.pagination
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal mengambil data nilai'
      };
    }
  },

  async createGrade(data: Omit<Grade, 'id' | 'createdAt' | 'updatedAt' | 'student' | 'subject' | 'class' | 'createdByUser'>): Promise<ApiResponse<Grade>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: { id: 'new-grade', createdAt: new Date().toISOString(), updatedAt: new Date().toISOString(), ...data } as Grade,
          message: 'Nilai berhasil dibuat (Demo Mode)'
        };
      }

      const response = await apiClient.post('/grades', data);
      return {
        success: true,
        data: response.data.data.grade,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal membuat nilai'
      };
    }
  },

  async bulkCreateGrades(grades: Omit<Grade, 'id' | 'createdAt' | 'updatedAt' | 'student' | 'subject' | 'class' | 'createdByUser'>[]): Promise<ApiResponse<any>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: { successful: grades.length, failed: 0 },
          message: `${grades.length} nilai berhasil dibuat (Demo Mode)`
        };
      }

      const response = await apiClient.post('/grades/bulk', { grades });
      return {
        success: true,
        data: response.data.data,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal membuat nilai bulk'
      };
    }
  },

  async getClassGradeStats(classId: string, params?: { subjectId?: string; gradeType?: string }): Promise<ApiResponse<any>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: {
            totalGrades: 25,
            average: 82.5,
            highest: 95,
            lowest: 65,
            distribution: {
              'A (90-100)': 8,
              'B (80-89)': 10,
              'C (70-79)': 5,
              'D (60-69)': 2,
              'E (<60)': 0
            }
          }
        };
      }

      const response = await apiClient.get(`/grades/stats/class/${classId}`, { params });
      return {
        success: true,
        data: response.data.data
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal mengambil statistik nilai'
      };
    }
  }
};

// Gamification service
export const gamificationService = {
  async getStudentXp(studentId: string): Promise<ApiResponse<StudentXp>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: {
            id: 'xp-1',
            studentId,
            totalXp: 250,
            level: 3,
            levelName: 'Rajin',
            attendanceStreak: 15,
            assignmentStreak: 8,
            lastAttendance: '2025-06-18',
            lastAssignment: '2025-06-17',
            updatedAt: '2025-06-18T10:00:00Z',
            student: {
              id: studentId,
              fullName: 'Ahmad Wijaya',
              studentId: '1234567890'
            }
          }
        };
      }

      const response = await apiClient.get(`/gamification/student/${studentId}`);
      return {
        success: true,
        data: response.data.data.studentXp
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal mengambil data XP siswa'
      };
    }
  },

  async getClassLeaderboard(classId: string, params?: { limit?: number }): Promise<ApiResponse<any[]>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: [
            {
              rank: 1,
              student: { id: 'student-1', fullName: 'Ahmad Wijaya', studentId: '1234567890' },
              totalXp: 350,
              level: 4,
              levelName: 'Berprestasi',
              attendanceStreak: 20,
              assignmentStreak: 12
            },
            {
              rank: 2,
              student: { id: 'student-2', fullName: 'Sari Dewi', studentId: '1234567891' },
              totalXp: 280,
              level: 3,
              levelName: 'Rajin',
              attendanceStreak: 18,
              assignmentStreak: 10
            }
          ]
        };
      }

      const response = await apiClient.get(`/gamification/leaderboard/${classId}`, { params });
      return {
        success: true,
        data: response.data.data.leaderboard
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal mengambil leaderboard'
      };
    }
  },

  async getStudentAchievements(studentId: string): Promise<ApiResponse<Achievement[]>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: [
            {
              id: 'achievement-1',
              studentId,
              type: 'PERFECT_SCORE',
              title: 'Nilai Sempurna!',
              description: 'Mendapat nilai 100 untuk pertama kali',
              xpReward: 50,
              earnedAt: '2025-06-15T10:00:00Z',
              metadata: { score: 100, maxScore: 100 }
            }
          ]
        };
      }

      const response = await apiClient.get(`/gamification/achievements/${studentId}`);
      return {
        success: true,
        data: response.data.data.achievements
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal mengambil pencapaian siswa'
      };
    }
  },

  // Get class leaderboard (alternative implementation)
  getClassLeaderboardAlt: async (classId: string, params?: { limit?: number }) => {
    try {
      const queryParams = new URLSearchParams();
      if (params?.limit) queryParams.append('limit', params.limit.toString());

      const response = await fetch(`${API_BASE_URL}/gamification/leaderboard/${classId}?${queryParams}`, {
        headers: getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching leaderboard:', error);
      return { success: false, error: 'Gagal mengambil leaderboard', data: [] };
    }
  }
};

// Assignment Service
export const assignmentService = {
  // Get assignments for current teacher
  getAssignments: async (params?: {
    classId?: string;
    status?: string;
    type?: string;
    search?: string;
  }) => {
    try {
      const queryParams = new URLSearchParams();
      if (params?.classId) queryParams.append('classId', params.classId);
      if (params?.status) queryParams.append('status', params.status);
      if (params?.type) queryParams.append('type', params.type);
      if (params?.search) queryParams.append('search', params.search);

      const response = await fetch(`${API_BASE_URL}/assignments?${queryParams}`, {
        headers: getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching assignments:', error);
      return { success: false, error: 'Gagal mengambil data tugas' };
    }
  },

  // Get assignment statistics
  getAssignmentStats: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/assignments/stats`, {
        headers: getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching assignment stats:', error);
      return { success: false, error: 'Gagal mengambil statistik tugas' };
    }
  },

  // Create new assignment
  createAssignment: async (assignmentData: {
    title: string;
    description?: string;
    instructions?: string;
    points?: number;
    deadline: string;
    type?: string;
    classId: string;
  }) => {
    try {
      const response = await fetch(`${API_BASE_URL}/assignments`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(assignmentData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating assignment:', error);
      return { success: false, error: 'Gagal membuat tugas' };
    }
  },

  // Update assignment
  updateAssignment: async (id: string, assignmentData: any) => {
    try {
      const response = await fetch(`${API_BASE_URL}/assignments/${id}`, {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify(assignmentData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error updating assignment:', error);
      return { success: false, error: 'Gagal mengupdate tugas' };
    }
  },

  // Delete assignment
  deleteAssignment: async (id: string) => {
    try {
      const response = await fetch(`${API_BASE_URL}/assignments/${id}`, {
        method: 'DELETE',
        headers: getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error deleting assignment:', error);
      return { success: false, error: 'Gagal menghapus tugas' };
    }
  },
};

// Badge Service
export const badgeService = {
  // Get all badges
  getBadges: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/badges`, {
        headers: getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching badges:', error);
      return { success: false, error: 'Gagal mengambil data badge', data: [] };
    }
  },

  // Create new badge
  createBadge: async (badgeData: {
    name: string;
    description: string;
    xpReward: number;
    icon: string;
  }) => {
    try {
      const response = await fetch(`${API_BASE_URL}/badges`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(badgeData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating badge:', error);
      return { success: false, error: 'Gagal membuat badge' };
    }
  },

  // Update badge
  updateBadge: async (id: string, badgeData: any) => {
    try {
      const response = await fetch(`${API_BASE_URL}/badges/${id}`, {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify(badgeData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error updating badge:', error);
      return { success: false, error: 'Gagal mengupdate badge' };
    }
  },

  // Delete badge
  deleteBadge: async (id: string) => {
    try {
      const response = await fetch(`${API_BASE_URL}/badges/${id}`, {
        method: 'DELETE',
        headers: getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error deleting badge:', error);
      return { success: false, error: 'Gagal menghapus badge' };
    }
  },
};

// Level Service
export const levelService = {
  // Get all levels
  getLevels: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/levels`, {
        headers: getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching levels:', error);
      return { success: false, error: 'Gagal mengambil data level', data: [] };
    }
  },

  // Create new level
  createLevel: async (levelData: {
    level: number;
    name: string;
    xpRequired: number;
    benefits: string;
  }) => {
    try {
      const response = await fetch(`${API_BASE_URL}/levels`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(levelData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating level:', error);
      return { success: false, error: 'Gagal membuat level' };
    }
  },

  // Update level
  updateLevel: async (id: string, levelData: any) => {
    try {
      const response = await fetch(`${API_BASE_URL}/levels/${id}`, {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify(levelData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error updating level:', error);
      return { success: false, error: 'Gagal mengupdate level' };
    }
  },

  // Delete level
  deleteLevel: async (id: string) => {
    try {
      const response = await fetch(`${API_BASE_URL}/levels/${id}`, {
        method: 'DELETE',
        headers: getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error deleting level:', error);
      return { success: false, error: 'Gagal menghapus level' };
    }
  },
};

// Challenge Service
export const challengeService = {
  // Get all challenges
  getChallenges: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/challenges`, {
        headers: getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching challenges:', error);
      return { success: false, error: 'Gagal mengambil data challenge', data: [] };
    }
  },

  // Create new challenge
  createChallenge: async (challengeData: {
    title: string;
    description: string;
    duration: number;
    targetType: string;
    xpReward: number;
  }) => {
    try {
      const response = await fetch(`${API_BASE_URL}/challenges`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(challengeData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating challenge:', error);
      return { success: false, error: 'Gagal membuat challenge' };
    }
  },

  // Update challenge
  updateChallenge: async (id: string, challengeData: any) => {
    try {
      const response = await fetch(`${API_BASE_URL}/challenges/${id}`, {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify(challengeData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error updating challenge:', error);
      return { success: false, error: 'Gagal mengupdate challenge' };
    }
  },

  // Delete challenge
  deleteChallenge: async (id: string) => {
    try {
      const response = await fetch(`${API_BASE_URL}/challenges/${id}`, {
        method: 'DELETE',
        headers: getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error deleting challenge:', error);
      return { success: false, error: 'Gagal menghapus challenge' };
    }
  },
};

// All services are already exported individually above
