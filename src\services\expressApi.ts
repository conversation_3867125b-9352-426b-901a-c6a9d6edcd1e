// Enhanced Express API Service for Guru Digital Pelangi
// Handles all API communications with Express backend
import axios from 'axios';

// Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  nip?: string;
  role: 'ADMIN' | 'GURU';
  status: 'ACTIVE' | 'INACTIVE';
}

export interface Class {
  id: string;
  name: string;
  gradeLevel: string;
  academicYear: string;
  teacherId?: string;
  schoolId?: string;
  studentCount?: number;
  teacher?: {
    firstName: string;
    lastName: string;
  };
  school?: {
    name: string;
  };
}

export interface Student {
  id: string;
  studentId: string; // NISN
  firstName: string;
  lastName: string;
  email?: string;
  classId?: string;
  dateOfBirth?: string;
  gender?: 'L' | 'P';
  address?: string;
  phone?: string;
  parentName?: string;
  parentPhone?: string;
  status: 'ACTIVE' | 'INACTIVE' | 'GRADUATED';
  class?: {
    name: string;
    gradeLevel: string;
  };
  studentXp?: {
    totalXp: number;
    level: number;
    levelName: string;
  };
}

export interface Attendance {
  id: string;
  studentId: string;
  classId: string;
  date: string;
  status: 'PRESENT' | 'ABSENT' | 'LATE' | 'EXCUSED';
  timeIn?: string;
  notes?: string;
  student?: {
    firstName: string;
    lastName: string;
    studentId: string;
  };
  class?: {
    name: string;
  };
}

export interface School {
  id: string;
  name: string;
  address?: string;
  phone?: string;
  email?: string;
}

// API client configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor untuk menambahkan token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    console.log('🔧 ExpressAPI: Request interceptor - Token:', token ? token.substring(0, 20) + '...' : 'No token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      console.log('🔧 ExpressAPI: Authorization header set');
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor untuk handle errors
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired atau invalid
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_data');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Demo mode flag
const DEMO_MODE = import.meta.env.VITE_DEMO_MODE === 'true';
console.log('🔧 ExpressAPI: Demo mode:', DEMO_MODE, 'ENV:', import.meta.env.VITE_DEMO_MODE);

// Authentication service
export const authService = {
  async login(identifier: string, password: string): Promise<ApiResponse<{ user: User; token: string }>> {
    try {
      if (DEMO_MODE) {
        // Demo credentials
        const demoCredentials = [
          {
            identifier: '<EMAIL>',
            password: 'admin123',
            user: {
              id: 'admin-1',
              firstName: 'Admin',
              lastName: 'System',
              email: '<EMAIL>',
              role: 'ADMIN' as const,
              status: 'ACTIVE' as const,
            },
            token: 'demo-admin-token'
          },
          {
            identifier: '<EMAIL>',
            password: 'guru123',
            user: {
              id: 'guru-1',
              firstName: 'Budi',
              lastName: 'Santoso',
              email: '<EMAIL>',
              nip: '198501012010011001',
              role: 'GURU' as const,
              status: 'ACTIVE' as const,
            },
            token: 'demo-guru-token'
          },
          {
            identifier: '1234567890',
            password: '1234567890',
            user: {
              id: 'siswa-1',
              firstName: 'Ahmad',
              lastName: 'Wijaya',
              email: '<EMAIL>',
              role: 'SISWA' as any,
              status: 'ACTIVE' as const,
            },
            token: 'demo-siswa-token'
          }
        ];

        const credential = demoCredentials.find(
          cred => cred.identifier === identifier && cred.password === password
        );

        if (!credential) {
          return {
            success: false,
            error: 'NIP/NISN/Email atau password salah'
          };
        }

        return {
          success: true,
          data: {
            user: credential.user as User,
            token: credential.token
          },
          message: 'Login berhasil (Demo Mode)'
        };
      }

      // Real API call
      const response = await apiClient.post('/auth/login', {
        identifier,
        password
      });

      console.log('🔧 ExpressAPI: Login response data:', response.data);

      // Store token
      if (response.data.data?.token) {
        localStorage.setItem('auth_token', response.data.data.token);
        console.log('🔧 ExpressAPI: Token stored:', response.data.data.token.substring(0, 20) + '...');
      }

      return {
        success: true,
        data: response.data.data,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Login gagal'
      };
    }
  },

  async getProfile(): Promise<ApiResponse<User>> {
    try {
      if (DEMO_MODE) {
        const userData = localStorage.getItem('user_data');
        if (userData) {
          return {
            success: true,
            data: JSON.parse(userData)
          };
        }
        return {
          success: false,
          error: 'User data not found'
        };
      }

      const response = await apiClient.get('/auth/profile');
      return {
        success: true,
        data: response.data.data.user
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal mengambil profile'
      };
    }
  },

  async logout(): Promise<void> {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_data');
  }
};

// Classes service
export const classService = {
  async getClasses(params?: { page?: number; limit?: number; search?: string }): Promise<ApiResponse<Class[]>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: [
            {
              id: 'class-10a',
              name: '10A',
              gradeLevel: '10',
              academicYear: '2024/2025',
              studentCount: 25,
              teacher: {
                firstName: 'Budi',
                lastName: 'Santoso'
              },
              school: {
                name: 'SMA Digital Pelangi'
              }
            },
            {
              id: 'class-10b',
              name: '10B',
              gradeLevel: '10',
              academicYear: '2024/2025',
              studentCount: 23,
              teacher: {
                firstName: 'Siti',
                lastName: 'Rahayu'
              },
              school: {
                name: 'SMA Digital Pelangi'
              }
            }
          ]
        };
      }

      const response = await apiClient.get('/classes', { params });
      return {
        success: true,
        data: response.data.data.classes,
        pagination: response.data.data.pagination
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal mengambil data kelas'
      };
    }
  },

  async createClass(data: Omit<Class, 'id' | 'studentCount'>): Promise<ApiResponse<Class>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: { id: 'new-class', studentCount: 0, ...data },
          message: 'Kelas berhasil dibuat (Demo Mode)'
        };
      }

      const response = await apiClient.post('/classes', data);
      return {
        success: true,
        data: response.data.data.class,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal membuat kelas'
      };
    }
  },

  async updateClass(id: string, data: Partial<Class>): Promise<ApiResponse<Class>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: { id, ...data } as Class,
          message: 'Kelas berhasil diupdate (Demo Mode)'
        };
      }

      const response = await apiClient.put(`/classes/${id}`, data);
      return {
        success: true,
        data: response.data.data.class,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal update kelas'
      };
    }
  },

  async deleteClass(id: string): Promise<ApiResponse<void>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          message: 'Kelas berhasil dihapus (Demo Mode)'
        };
      }

      const response = await apiClient.delete(`/classes/${id}`);
      return {
        success: true,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal menghapus kelas'
      };
    }
  }
};

// Students service
export const studentService = {
  async getStudents(params?: { page?: number; limit?: number; search?: string; classId?: string }): Promise<ApiResponse<Student[]>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: [
            {
              id: 'student-1',
              studentId: '1234567890',
              firstName: 'Ahmad',
              lastName: 'Wijaya',
              email: '<EMAIL>',
              classId: 'class-10a',
              gender: 'L',
              status: 'ACTIVE',
              class: {
                name: '10A',
                gradeLevel: '10'
              },
              studentXp: {
                totalXp: 150,
                level: 2,
                levelName: 'Pelajar'
              }
            },
            {
              id: 'student-2',
              studentId: '1234567891',
              firstName: 'Sari',
              lastName: 'Dewi',
              email: '<EMAIL>',
              classId: 'class-10a',
              gender: 'P',
              status: 'ACTIVE',
              class: {
                name: '10A',
                gradeLevel: '10'
              },
              studentXp: {
                totalXp: 200,
                level: 3,
                levelName: 'Cendekiawan'
              }
            }
          ]
        };
      }

      const response = await apiClient.get('/students', { params });
      return {
        success: true,
        data: response.data.data.students,
        pagination: response.data.data.pagination
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal mengambil data siswa'
      };
    }
  },

  async createStudent(data: Omit<Student, 'id' | 'studentXp'>): Promise<ApiResponse<Student>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: { id: 'new-student', ...data } as Student,
          message: 'Siswa berhasil dibuat (Demo Mode)'
        };
      }

      const response = await apiClient.post('/students', data);
      return {
        success: true,
        data: response.data.data.student,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal membuat siswa'
      };
    }
  },

  async updateStudent(id: string, data: Partial<Student>): Promise<ApiResponse<Student>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: { id, ...data } as Student,
          message: 'Siswa berhasil diupdate (Demo Mode)'
        };
      }

      const response = await apiClient.put(`/students/${id}`, data);
      return {
        success: true,
        data: response.data.data.student,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal update siswa'
      };
    }
  },

  async deleteStudent(id: string): Promise<ApiResponse<void>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          message: 'Siswa berhasil dihapus (Demo Mode)'
        };
      }

      const response = await apiClient.delete(`/students/${id}`);
      return {
        success: true,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal menghapus siswa'
      };
    }
  }
};

// Attendance service
export const attendanceService = {
  async getAttendance(params?: {
    page?: number;
    limit?: number;
    classId?: string;
    studentId?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<ApiResponse<Attendance[]>> {
    try {
      if (DEMO_MODE) {
        const today = new Date().toISOString().split('T')[0];
        return {
          success: true,
          data: [
            {
              id: 'attendance-1',
              studentId: 'student-1',
              classId: 'class-10a',
              date: today,
              status: 'PRESENT',
              timeIn: '07:30',
              student: {
                firstName: 'Ahmad',
                lastName: 'Wijaya',
                studentId: '1234567890'
              },
              class: {
                name: '10A'
              }
            },
            {
              id: 'attendance-2',
              studentId: 'student-2',
              classId: 'class-10a',
              date: today,
              status: 'LATE',
              timeIn: '07:45',
              student: {
                firstName: 'Sari',
                lastName: 'Dewi',
                studentId: '1234567891'
              },
              class: {
                name: '10A'
              }
            }
          ]
        };
      }

      const response = await apiClient.get('/attendance', { params });
      return {
        success: true,
        data: response.data.data.attendance,
        pagination: response.data.data.pagination
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal mengambil data presensi'
      };
    }
  },

  async createAttendance(data: Omit<Attendance, 'id' | 'student' | 'class'>): Promise<ApiResponse<Attendance>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: { id: 'new-attendance', ...data } as Attendance,
          message: 'Presensi berhasil dibuat (Demo Mode)'
        };
      }

      const response = await apiClient.post('/attendance', data);
      return {
        success: true,
        data: response.data.data.attendance,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal membuat presensi'
      };
    }
  },

  async bulkCreateAttendance(attendanceRecords: Omit<Attendance, 'id' | 'student' | 'class'>[]): Promise<ApiResponse<any>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: { successful: attendanceRecords.length, failed: 0 },
          message: `${attendanceRecords.length} presensi berhasil dibuat (Demo Mode)`
        };
      }

      const response = await apiClient.post('/attendance/bulk', { attendanceRecords });
      return {
        success: true,
        data: response.data.data,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal membuat presensi bulk'
      };
    }
  },

  async updateAttendance(id: string, data: Partial<Attendance>): Promise<ApiResponse<Attendance>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: { id, ...data } as Attendance,
          message: 'Presensi berhasil diupdate (Demo Mode)'
        };
      }

      const response = await apiClient.put(`/attendance/${id}`, data);
      return {
        success: true,
        data: response.data.data.attendance,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal update presensi'
      };
    }
  },

  async deleteAttendance(id: string): Promise<ApiResponse<void>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          message: 'Presensi berhasil dihapus (Demo Mode)'
        };
      }

      const response = await apiClient.delete(`/attendance/${id}`);
      return {
        success: true,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal menghapus presensi'
      };
    }
  }
};

// Schools service
export const schoolService = {
  async getSchools(params?: { page?: number; limit?: number; search?: string }): Promise<ApiResponse<School[]>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: [
            {
              id: 'school-1',
              name: 'SMA Digital Pelangi',
              address: 'Jl. Pendidikan No. 123, Jakarta Selatan',
              phone: '021-12345678',
              email: '<EMAIL>'
            }
          ]
        };
      }

      const response = await apiClient.get('/schools', { params });
      return {
        success: true,
        data: response.data.data.schools,
        pagination: response.data.data.pagination
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal mengambil data sekolah'
      };
    }
  },

  async createSchool(data: Omit<School, 'id'>): Promise<ApiResponse<School>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: { id: 'new-school', ...data },
          message: 'Sekolah berhasil dibuat (Demo Mode)'
        };
      }

      const response = await apiClient.post('/schools', data);
      return {
        success: true,
        data: response.data.data.school,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal membuat sekolah'
      };
    }
  },

  async updateSchool(id: string, data: Partial<School>): Promise<ApiResponse<School>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: { id, ...data } as School,
          message: 'Sekolah berhasil diupdate (Demo Mode)'
        };
      }

      const response = await apiClient.put(`/schools/${id}`, data);
      return {
        success: true,
        data: response.data.data.school,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal update sekolah'
      };
    }
  },

  async deleteSchool(id: string): Promise<ApiResponse<void>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          message: 'Sekolah berhasil dihapus (Demo Mode)'
        };
      }

      const response = await apiClient.delete(`/schools/${id}`);
      return {
        success: true,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal menghapus sekolah'
      };
    }
  }
};
