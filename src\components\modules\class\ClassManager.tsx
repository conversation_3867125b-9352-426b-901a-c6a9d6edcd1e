// Enhanced Class Management Component for Guru Digital Pelangi
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Card, 
  CardBody, 
  CardHeader, 
  Button, 
  Input, 
  Table, 
  TableHeader, 
  TableColumn, 
  TableBody, 
  TableRow, 
  TableCell,
  Chip,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Select,
  SelectItem,
  Tabs,
  Tab
} from '@heroui/react';
import { Plus, Search, Edit, Trash2, Users, BookOpen, Calendar, GraduationCap } from 'lucide-react';
import EmptyState from '../../common/EmptyState';
import { classService, schoolService } from '../../../services/expressApi';
import { Class } from '../../../services/expressApi';
import { useAuthStore } from '../../../stores/authStore';
import AttendanceManager from '../attendance/AttendanceManager';

interface Subject {
  id: string;
  name: string;
  code: string;
}

interface Teacher {
  id: string;
  firstName: string;
  lastName: string;
  fullName: string;
  email: string;
}

const ClassManager: React.FC = () => {
  const { user } = useAuthStore();
  const [classes, setClasses] = useState<Class[]>([]);
  const [filteredClasses, setFilteredClasses] = useState<Class[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedClass, setSelectedClass] = useState<Class | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [loadingSubjects, setLoadingSubjects] = useState(false);
  const [loadingTeachers, setLoadingTeachers] = useState(false);
  const [activeTab, setActiveTab] = useState('classes');
  const { isOpen, onOpen, onClose } = useDisclosure();

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    subjectId: '',
    teacherIds: [] as string[],
    description: ''
  });

  useEffect(() => {
    loadClasses();
    loadSubjects();
    if (user?.role === 'ADMIN') {
      loadTeachers();
    }
  }, [user]);

  useEffect(() => {
    filterClasses();
  }, [searchTerm, classes]);

  const loadClasses = async () => {
    setIsLoading(true);
    const response = await classService.getClasses();
    if (response.success && response.data) {
      setClasses(response.data);
    }
    setIsLoading(false);
  };

  const loadSubjects = async () => {
    try {
      setLoadingSubjects(true);
      console.log('🔧 Loading subjects...');
      const response = await fetch('http://localhost:5000/api/subjects', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });
      if (response.ok) {
        const result = await response.json();
        console.log('🔧 Subjects loaded:', result.data);
        setSubjects(result.data);
      } else {
        console.error('🔧 Failed to load subjects:', response.status);
      }
    } catch (error) {
      console.error('Error loading subjects:', error);
    } finally {
      setLoadingSubjects(false);
    }
  };

  const loadTeachers = async () => {
    try {
      setLoadingTeachers(true);
      console.log('🔧 Loading teachers...');
      const response = await fetch('http://localhost:5000/api/teachers', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });
      if (response.ok) {
        const result = await response.json();
        console.log('🔧 Teachers loaded:', result.data);
        setTeachers(result.data);
      } else {
        console.error('🔧 Failed to load teachers:', response.status);
      }
    } catch (error) {
      console.error('Error loading teachers:', error);
    } finally {
      setLoadingTeachers(false);
    }
  };

  const filterClasses = () => {
    if (!searchTerm) {
      setFilteredClasses(classes);
    } else {
      const filtered = classes.filter(cls =>
        cls.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cls.subject?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cls.gradeLevel?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cls.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredClasses(filtered);
    }
  };

  const handleCreate = () => {
    setSelectedClass(null);
    setIsEditing(false);
    setFormData({
      name: '',
      subjectId: '',
      teacherIds: user?.role === 'GURU' ? [user.id] : [],
      description: ''
    });
    // Reload data saat modal dibuka
    loadSubjects();
    if (user?.role === 'ADMIN') {
      loadTeachers();
    }
    onOpen();
  };

  const handleEdit = (cls: Class) => {
    setSelectedClass(cls);
    setIsEditing(true);
    setFormData({
      name: cls.name,
      subjectId: cls.subjectId || '',
      teacherIds: cls.teachers?.map(t => t.id) || [],
      description: cls.description || ''
    });
    onOpen();
  };

  const handleSave = async () => {
    try {
      console.log('Saving class data:', formData);

      if (isEditing && selectedClass) {
        const response = await classService.updateClass(selectedClass.id, formData);
        console.log('Update response:', response);
        if (response.success) {
          loadClasses();
          onClose();
          alert('Kelas berhasil diupdate!');
        } else {
          alert('Error: ' + (response.error || 'Gagal update kelas'));
        }
      } else {
        const response = await classService.createClass(formData);
        console.log('Create response:', response);
        if (response.success) {
          loadClasses();
          onClose();
          alert('Kelas berhasil dibuat!');
        } else {
          alert('Error: ' + (response.error || 'Gagal membuat kelas'));
        }
      }
    } catch (error) {
      console.error('Save error:', error);
      alert('Terjadi error saat menyimpan data');
    }
  };

  const handleDelete = async (id: string) => {
    if (confirm('Apakah Anda yakin ingin menghapus kelas ini?')) {
      try {
        const response = await classService.deleteClass(id);
        console.log('Delete response:', response);
        if (response.success) {
          loadClasses();
          alert('Kelas berhasil dihapus!');
        } else {
          alert('Error: ' + (response.error || 'Gagal menghapus kelas'));
        }
      } catch (error) {
        console.error('Delete error:', error);
        alert('Terjadi error saat menghapus data');
      }
    }
  };

  const gradeOptions = [
    { key: '7', label: 'Kelas 7' },
    { key: '8', label: 'Kelas 8' },
    { key: '9', label: 'Kelas 9' },
    { key: '10', label: 'Kelas 10' },
    { key: '11', label: 'Kelas 11' },
    { key: '12', label: 'Kelas 12' }
  ];

  // Array warna gradient yang beragam untuk header kelas
  const gradientColors = [
    'from-cyan-400 to-blue-500',      // Biru cyan
    'from-purple-400 to-pink-500',    // Ungu pink
    'from-green-400 to-teal-500',     // Hijau teal
    'from-orange-400 to-red-500',     // Orange merah
    'from-indigo-400 to-purple-500',  // Indigo ungu
    'from-pink-400 to-rose-500',      // Pink rose
    'from-teal-400 to-cyan-500',      // Teal cyan
    'from-amber-400 to-orange-500',   // Amber orange
    'from-emerald-400 to-green-500',  // Emerald hijau
    'from-violet-400 to-purple-500',  // Violet ungu
    'from-sky-400 to-blue-500',       // Sky biru
    'from-lime-400 to-green-500',     // Lime hijau
    'from-fuchsia-400 to-pink-500',   // Fuchsia pink
    'from-rose-400 to-red-500',       // Rose merah
    'from-blue-400 to-indigo-500',    // Biru indigo
  ];

  // Function untuk mendapatkan warna berdasarkan index
  const getGradientColor = (index: number) => {
    return gradientColors[index % gradientColors.length];
  };

  if (activeTab === 'attendance') {
    return <AttendanceManager />;
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header with Tabs */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <Card>
          <CardHeader className="flex flex-col gap-4">
            <div className="flex flex-row items-center justify-between w-full">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <BookOpen className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold">Manajemen Kelas</h1>
                  <p className="text-gray-600">Kelola data kelas dan presensi siswa</p>
                </div>
              </div>
              {activeTab === 'classes' && (
                <Button
                  color="primary"
                  startContent={<Plus className="w-4 h-4" />}
                  onPress={handleCreate}
                >
                  Tambah Kelas
                </Button>
              )}
            </div>
            
            <Tabs 
              selectedKey={activeTab} 
              onSelectionChange={(key) => setActiveTab(key as string)}
              className="w-full"
            >
              <Tab 
                key="classes" 
                title={
                  <div className="flex items-center gap-2">
                    <GraduationCap className="w-4 h-4" />
                    <span>Data Kelas</span>
                  </div>
                }
              />
              <Tab 
                key="attendance" 
                title={
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    <span>Presensi</span>
                  </div>
                }
              />
            </Tabs>
          </CardHeader>
        </Card>
      </motion.div>

      {/* Search and Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card>
          <CardBody>
            <div className="flex gap-4">
              <Input
                placeholder="Cari kelas..."
                startContent={<Search className="w-4 h-4 text-gray-400" />}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="flex-1"
              />
              <Chip color="primary" variant="flat">
                {filteredClasses.length} Kelas
              </Chip>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Classes Table */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Daftar Kelas</h3>
          </CardHeader>
          <CardBody>
            {filteredClasses.length === 0 ? (
              <EmptyState
                icon={GraduationCap}
                title="Belum ada kelas"
                description="Mulai dengan membuat kelas pertama Anda untuk mengelola siswa dan pembelajaran"
                actionLabel="Buat Kelas Pertama"
                onAction={handleCreate}
                actionColor="primary"
              />
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredClasses.map((cls, index) => (
                <Card key={cls.id} className="hover:shadow-lg transition-all duration-300 bg-white border border-gray-200">
                  {/* Header Card - Dynamic Gradient */}
                  <CardHeader className={`bg-gradient-to-r ${getGradientColor(index)} text-white p-6 relative overflow-hidden`}>
                    <div className="flex justify-between items-start w-full">
                      <div>
                        <h3 className="text-3xl font-bold mb-1">{cls.name}</h3>
                        <p className="text-white/90 text-sm font-medium">{cls.subject?.name || 'Mata Pelajaran'}</p>
                      </div>
                      <div className="flex gap-1">
                        <div className="w-4 h-4 bg-white/30 rounded"></div>
                        <div className="w-4 h-4 bg-white/30 rounded"></div>
                      </div>
                    </div>
                  </CardHeader>

                  {/* Content */}
                  <CardBody className="p-6 bg-gray-50">
                    {/* Class Info */}
                    <div className="mb-6">
                      <h4 className="text-lg font-semibold text-gray-800">
                        {cls.subject?.name || 'Mata Pelajaran'}
                      </h4>
                    </div>

                    {/* Stats Grid */}
                    <div className="grid grid-cols-3 gap-6 mb-8">
                      <div className="text-center">
                        <div className="w-12 h-12 mx-auto mb-3 flex items-center justify-center">
                          <Users className="w-8 h-8 text-gray-600" />
                        </div>
                        <p className="text-2xl font-bold text-gray-900 mb-1">{cls.studentCount || 0}</p>
                        <p className="text-gray-600 text-sm">Siswa</p>
                      </div>
                      <div className="text-center">
                        <div className="w-12 h-12 mx-auto mb-3 flex items-center justify-center">
                          <BookOpen className="w-8 h-8 text-gray-600" />
                        </div>
                        <p className="text-2xl font-bold text-gray-900 mb-1">0</p>
                        <p className="text-gray-600 text-sm">Tugas</p>
                      </div>
                      <div className="text-center">
                        <div className="w-12 h-12 mx-auto mb-3 flex items-center justify-center">
                          <GraduationCap className="w-8 h-8 text-gray-600" />
                        </div>
                        <p className="text-2xl font-bold text-gray-900 mb-1">-</p>
                        <p className="text-gray-600 text-sm">Rata-rata</p>
                      </div>
                    </div>

                    {/* Footer */}
                    <div className="flex justify-between items-center">
                      <p className="text-gray-500 text-sm">
                        {cls.description || 'Dibuat baru-baru ini'}
                      </p>
                      <div className="flex gap-2">
                        <Button
                          isIconOnly
                          size="sm"
                          variant="light"
                          className="text-gray-500 hover:text-blue-600"
                        >
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M3.9 12c0-1.71 1.39-3.1 3.1-3.1h4V7H6.99C4.24 7 2 9.24 2 12s2.24 5 4.99 5H11v-1.9H7c-1.71 0-3.1-1.39-3.1-3.1zM8 13h8v-2H8v2zm5-6h4.01C19.76 7 22 9.24 22 12s-2.24 5-4.99 5H13v1.9h4c1.71 0 3.1-1.39 3.1-3.1 0-1.71-1.39-3.1-3.1-3.1H13V7z"/>
                          </svg>
                        </Button>
                        <Button
                          isIconOnly
                          size="sm"
                          variant="light"
                          className="text-gray-500 hover:text-green-600"
                          onPress={() => handleEdit(cls)}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          isIconOnly
                          size="sm"
                          variant="light"
                          className="text-gray-500 hover:text-red-600"
                          onPress={() => handleDelete(cls.id)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardBody>
                </Card>
                ))}
              </div>
            )}
          </CardBody>
        </Card>
      </motion.div>

      {/* Create/Edit Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="2xl">
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader>
                <h3>{isEditing ? 'Edit Kelas' : 'Tambah Kelas Baru'}</h3>
              </ModalHeader>
              <ModalBody>
                <div className="space-y-4">
                  <Input
                    label="Nama Kelas"
                    placeholder="Contoh: Kelas 7A"
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    isRequired
                  />

                  <Select
                    label="Mata Pelajaran"
                    placeholder="Pilih mata pelajaran"
                    selectedKeys={formData.subjectId ? [formData.subjectId] : []}
                    onSelectionChange={(keys) => {
                      const selectedKey = Array.from(keys)[0] as string;
                      setFormData({...formData, subjectId: selectedKey});
                    }}
                    isRequired
                  >
                    {loadingSubjects ? (
                      <SelectItem key="loading" isDisabled>
                        Loading mata pelajaran...
                      </SelectItem>
                    ) : subjects.length === 0 ? (
                      <SelectItem key="empty" isDisabled>
                        Tidak ada mata pelajaran
                      </SelectItem>
                    ) : (
                      subjects.map((subject) => (
                        <SelectItem key={subject.id}>
                          {subject.name} ({subject.code})
                        </SelectItem>
                      ))
                    )}
                  </Select>

                  {user?.role === 'ADMIN' && (
                    <Select
                      label="Assign Guru"
                      placeholder="Pilih guru yang mengajar"
                      selectionMode="multiple"
                      selectedKeys={formData.teacherIds}
                      onSelectionChange={(keys) => {
                        setFormData({...formData, teacherIds: Array.from(keys) as string[]});
                      }}
                    >
                      {loadingTeachers ? (
                        <SelectItem key="loading" isDisabled>
                          Loading guru...
                        </SelectItem>
                      ) : teachers.length === 0 ? (
                        <SelectItem key="empty" isDisabled>
                          Tidak ada guru
                        </SelectItem>
                      ) : (
                        teachers.map((teacher) => (
                          <SelectItem key={teacher.id}>
                            {teacher.fullName} ({teacher.email})
                          </SelectItem>
                        ))
                      )}
                    </Select>
                  )}



                  <Input
                    label="Deskripsi"
                    placeholder="Deskripsi kelas (opsional)"
                    value={formData.description}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                  />
                </div>
              </ModalBody>
              <ModalFooter>
                <Button variant="light" onPress={onClose}>
                  Batal
                </Button>
                <Button color="primary" onPress={handleSave}>
                  {isEditing ? 'Update' : 'Simpan'}
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </div>
  );
};

export default ClassManager;
