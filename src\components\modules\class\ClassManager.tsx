// Enhanced Class Management Component for Guru Digital Pelangi
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Card, 
  CardBody, 
  CardHeader, 
  Button, 
  Input, 
  Table, 
  TableHeader, 
  TableColumn, 
  TableBody, 
  TableRow, 
  TableCell,
  Chip,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Select,
  SelectItem,
  Tabs,
  Tab
} from '@heroui/react';
import { Plus, Search, Edit, Trash2, Users, BookOpen, Calendar, GraduationCap } from 'lucide-react';
import { classService, schoolService } from '../../../services/expressApi';
import { Class } from '../../../services/expressApi';
import AttendanceManager from '../attendance/AttendanceManager';

const ClassManager: React.FC = () => {
  const [classes, setClasses] = useState<Class[]>([]);
  const [filteredClasses, setFilteredClasses] = useState<Class[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedClass, setSelectedClass] = useState<Class | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [schools, setSchools] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState('classes');
  const { isOpen, onOpen, onClose } = useDisclosure();

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    subject: '',
    start_time: '',
    end_time: '',
    description: ''
  });

  useEffect(() => {
    loadClasses();
    loadSchools();
  }, []);

  useEffect(() => {
    filterClasses();
  }, [searchTerm, classes]);

  const loadClasses = async () => {
    setIsLoading(true);
    const response = await classService.getClasses();
    if (response.success && response.data) {
      setClasses(response.data);
    }
    setIsLoading(false);
  };

  const loadSchools = async () => {
    const response = await schoolService.getSchools();
    if (response.success && response.data) {
      setSchools(response.data);
    }
  };

  const filterClasses = () => {
    if (!searchTerm) {
      setFilteredClasses(classes);
    } else {
      const filtered = classes.filter(cls =>
        cls.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cls.grade_level.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredClasses(filtered);
    }
  };

  const handleCreate = () => {
    setSelectedClass(null);
    setIsEditing(false);
    setFormData({
      name: '',
      subject: '',
      start_time: '',
      end_time: '',
      description: ''
    });
    onOpen();
  };

  const handleEdit = (cls: Class) => {
    setSelectedClass(cls);
    setIsEditing(true);
    setFormData({
      name: cls.name,
      subject: cls.subject || '',
      start_time: cls.start_time || '',
      end_time: cls.end_time || '',
      description: cls.description || ''
    });
    onOpen();
  };

  const handleSave = async () => {
    try {
      console.log('Saving class data:', formData);

      if (isEditing && selectedClass) {
        const response = await classService.updateClass(selectedClass.id, formData);
        console.log('Update response:', response);
        if (response.success) {
          loadClasses();
          onClose();
          alert('Kelas berhasil diupdate!');
        } else {
          alert('Error: ' + (response.error || 'Gagal update kelas'));
        }
      } else {
        const response = await classService.createClass(formData);
        console.log('Create response:', response);
        if (response.success) {
          loadClasses();
          onClose();
          alert('Kelas berhasil dibuat!');
        } else {
          alert('Error: ' + (response.error || 'Gagal membuat kelas'));
        }
      }
    } catch (error) {
      console.error('Save error:', error);
      alert('Terjadi error saat menyimpan data');
    }
  };

  const handleDelete = async (id: string) => {
    if (confirm('Apakah Anda yakin ingin menghapus kelas ini?')) {
      try {
        const response = await classService.deleteClass(id);
        console.log('Delete response:', response);
        if (response.success) {
          loadClasses();
          alert('Kelas berhasil dihapus!');
        } else {
          alert('Error: ' + (response.error || 'Gagal menghapus kelas'));
        }
      } catch (error) {
        console.error('Delete error:', error);
        alert('Terjadi error saat menghapus data');
      }
    }
  };

  const gradeOptions = [
    { key: '7', label: 'Kelas 7' },
    { key: '8', label: 'Kelas 8' },
    { key: '9', label: 'Kelas 9' },
    { key: '10', label: 'Kelas 10' },
    { key: '11', label: 'Kelas 11' },
    { key: '12', label: 'Kelas 12' }
  ];

  if (activeTab === 'attendance') {
    return <AttendanceManager />;
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header with Tabs */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <Card>
          <CardHeader className="flex flex-col gap-4">
            <div className="flex flex-row items-center justify-between w-full">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <BookOpen className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold">Manajemen Kelas</h1>
                  <p className="text-gray-600">Kelola data kelas dan presensi siswa</p>
                </div>
              </div>
              {activeTab === 'classes' && (
                <Button
                  color="primary"
                  startContent={<Plus className="w-4 h-4" />}
                  onPress={handleCreate}
                >
                  Tambah Kelas
                </Button>
              )}
            </div>
            
            <Tabs 
              selectedKey={activeTab} 
              onSelectionChange={(key) => setActiveTab(key as string)}
              className="w-full"
            >
              <Tab 
                key="classes" 
                title={
                  <div className="flex items-center gap-2">
                    <GraduationCap className="w-4 h-4" />
                    <span>Data Kelas</span>
                  </div>
                }
              />
              <Tab 
                key="attendance" 
                title={
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    <span>Presensi</span>
                  </div>
                }
              />
            </Tabs>
          </CardHeader>
        </Card>
      </motion.div>

      {/* Search and Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card>
          <CardBody>
            <div className="flex gap-4">
              <Input
                placeholder="Cari kelas..."
                startContent={<Search className="w-4 h-4 text-gray-400" />}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="flex-1"
              />
              <Chip color="primary" variant="flat">
                {filteredClasses.length} Kelas
              </Chip>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Classes Table */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Daftar Kelas</h3>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredClasses.map((cls) => (
                <Card key={cls.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg">
                          <BookOpen className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <h3 className="font-bold text-lg">{cls.name}</h3>
                          <p className="text-sm text-gray-600">{cls.subject || 'Mata Pelajaran'}</p>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardBody className="pt-0">
                    <div className="space-y-3">
                      <div className="flex items-center gap-2 text-sm">
                        <Calendar className="w-4 h-4 text-gray-400" />
                        <span>{cls.start_time || '07:00'} - {cls.end_time || '08:30'}</span>
                      </div>

                      <div className="flex items-center gap-2 text-sm">
                        <Users className="w-4 h-4 text-gray-400" />
                        <span>{cls.student_count || 0} Siswa</span>
                      </div>

                      {cls.description && (
                        <p className="text-sm text-gray-600 line-clamp-2">{cls.description}</p>
                      )}
                    </div>
                  </CardBody>
                  <CardBody className="pt-0">
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="light"
                        startContent={<Edit className="w-4 h-4" />}
                        onPress={() => handleEdit(cls)}
                        className="flex-1"
                      >
                        Edit
                      </Button>
                      <Button
                        size="sm"
                        color="danger"
                        variant="light"
                        startContent={<Trash2 className="w-4 h-4" />}
                        onPress={() => handleDelete(cls.id)}
                        className="flex-1"
                      >
                        Hapus
                      </Button>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Create/Edit Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="2xl">
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader>
                <h3>{isEditing ? 'Edit Kelas' : 'Tambah Kelas Baru'}</h3>
              </ModalHeader>
              <ModalBody>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Nama Kelas"
                    placeholder="Contoh: Matematika Kelas 7A"
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    isRequired
                    className="md:col-span-2"
                  />

                  <Input
                    label="Mata Pelajaran"
                    placeholder="Contoh: Matematika, Bahasa Indonesia"
                    value={formData.subject}
                    onChange={(e) => setFormData({...formData, subject: e.target.value})}
                    isRequired
                    className="md:col-span-2"
                  />

                  <Input
                    label="Jam Mulai"
                    type="time"
                    value={formData.start_time}
                    onChange={(e) => setFormData({...formData, start_time: e.target.value})}
                    isRequired
                  />

                  <Input
                    label="Jam Selesai"
                    type="time"
                    value={formData.end_time}
                    onChange={(e) => setFormData({...formData, end_time: e.target.value})}
                    isRequired
                  />

                  <Input
                    label="Deskripsi"
                    placeholder="Deskripsi kelas (opsional)"
                    value={formData.description}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    className="md:col-span-2"
                  />
                </div>
              </ModalBody>
              <ModalFooter>
                <Button variant="light" onPress={onClose}>
                  Batal
                </Button>
                <Button color="primary" onPress={handleSave}>
                  {isEditing ? 'Update' : 'Simpan'}
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </div>
  );
};

export default ClassManager;
