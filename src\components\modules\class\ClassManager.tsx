// Enhanced Class Management Component for Guru Digital Pelangi
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Card, 
  CardBody, 
  CardHeader, 
  Button, 
  Input, 
  Table, 
  TableHeader, 
  TableColumn, 
  TableBody, 
  TableRow, 
  TableCell,
  Chip,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Select,
  SelectItem,
  Tabs,
  Tab
} from '@heroui/react';
import { Plus, Search, Edit, Trash2, Users, BookOpen, Calendar, GraduationCap } from 'lucide-react';
import { classService, schoolService } from '../../../services/directus';
import { Class } from '../../../types/api';
import AttendanceManager from '../attendance/AttendanceManager';

const ClassManager: React.FC = () => {
  const [classes, setClasses] = useState<Class[]>([]);
  const [filteredClasses, setFilteredClasses] = useState<Class[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedClass, setSelectedClass] = useState<Class | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [schools, setSchools] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState('classes');
  const { isOpen, onOpen, onClose } = useDisclosure();

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    grade_level: '',
    academic_year: '2024/2025',
    teacher_id: '',
    school_id: ''
  });

  useEffect(() => {
    loadClasses();
    loadSchools();
  }, []);

  useEffect(() => {
    filterClasses();
  }, [searchTerm, classes]);

  const loadClasses = async () => {
    setIsLoading(true);
    const response = await classService.getClasses();
    if (response.success && response.data) {
      setClasses(response.data);
    }
    setIsLoading(false);
  };

  const loadSchools = async () => {
    const response = await schoolService.getSchools();
    if (response.success && response.data) {
      setSchools(response.data);
    }
  };

  const filterClasses = () => {
    if (!searchTerm) {
      setFilteredClasses(classes);
    } else {
      const filtered = classes.filter(cls =>
        cls.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cls.grade_level.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredClasses(filtered);
    }
  };

  const handleCreate = () => {
    setSelectedClass(null);
    setIsEditing(false);
    setFormData({
      name: '',
      grade_level: '',
      academic_year: '2024/2025',
      teacher_id: '',
      school_id: schools.length > 0 ? schools[0].id : ''
    });
    onOpen();
  };

  const handleEdit = (cls: Class) => {
    setSelectedClass(cls);
    setIsEditing(true);
    setFormData({
      name: cls.name,
      grade_level: cls.grade_level,
      academic_year: cls.academic_year,
      teacher_id: cls.teacher_id || '',
      school_id: cls.school_id || ''
    });
    onOpen();
  };

  const handleSave = async () => {
    if (isEditing && selectedClass) {
      const response = await classService.updateClass(selectedClass.id, formData);
      if (response.success) {
        loadClasses();
        onClose();
      }
    } else {
      const response = await classService.createClass(formData);
      if (response.success) {
        loadClasses();
        onClose();
      }
    }
  };

  const handleDelete = async (id: string) => {
    if (confirm('Apakah Anda yakin ingin menghapus kelas ini?')) {
      const response = await classService.deleteClass(id);
      if (response.success) {
        loadClasses();
      }
    }
  };

  const gradeOptions = [
    { key: '7', label: 'Kelas 7' },
    { key: '8', label: 'Kelas 8' },
    { key: '9', label: 'Kelas 9' },
    { key: '10', label: 'Kelas 10' },
    { key: '11', label: 'Kelas 11' },
    { key: '12', label: 'Kelas 12' }
  ];

  if (activeTab === 'attendance') {
    return <AttendanceManager />;
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header with Tabs */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <Card>
          <CardHeader className="flex flex-col gap-4">
            <div className="flex flex-row items-center justify-between w-full">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <BookOpen className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold">Manajemen Kelas</h1>
                  <p className="text-gray-600">Kelola data kelas dan presensi siswa</p>
                </div>
              </div>
              {activeTab === 'classes' && (
                <Button
                  color="primary"
                  startContent={<Plus className="w-4 h-4" />}
                  onPress={handleCreate}
                >
                  Tambah Kelas
                </Button>
              )}
            </div>
            
            <Tabs 
              selectedKey={activeTab} 
              onSelectionChange={(key) => setActiveTab(key as string)}
              className="w-full"
            >
              <Tab 
                key="classes" 
                title={
                  <div className="flex items-center gap-2">
                    <GraduationCap className="w-4 h-4" />
                    <span>Data Kelas</span>
                  </div>
                }
              />
              <Tab 
                key="attendance" 
                title={
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    <span>Presensi</span>
                  </div>
                }
              />
            </Tabs>
          </CardHeader>
        </Card>
      </motion.div>

      {/* Search and Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card>
          <CardBody>
            <div className="flex gap-4">
              <Input
                placeholder="Cari kelas..."
                startContent={<Search className="w-4 h-4 text-gray-400" />}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="flex-1"
              />
              <Chip color="primary" variant="flat">
                {filteredClasses.length} Kelas
              </Chip>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Classes Table */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Daftar Kelas</h3>
          </CardHeader>
          <CardBody>
            <Table aria-label="Classes table">
              <TableHeader>
                <TableColumn>NAMA KELAS</TableColumn>
                <TableColumn>TINGKAT</TableColumn>
                <TableColumn>TAHUN AJARAN</TableColumn>
                <TableColumn>JUMLAH SISWA</TableColumn>
                <TableColumn>WALI KELAS</TableColumn>
                <TableColumn>AKSI</TableColumn>
              </TableHeader>
              <TableBody>
                {filteredClasses.map((cls) => (
                  <TableRow key={cls.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-blue-100 rounded-lg">
                          <BookOpen className="w-4 h-4 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-medium">{cls.name}</p>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Chip color="primary" variant="flat">
                        Kelas {cls.grade_level}
                      </Chip>
                    </TableCell>
                    <TableCell>{cls.academic_year}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Users className="w-4 h-4 text-gray-400" />
                        <span>{cls.student_count || 0} siswa</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {cls.teacher?.first_name ? 
                        `${cls.teacher.first_name} ${cls.teacher.last_name}` : 
                        'Belum ditentukan'
                      }
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="light"
                          startContent={<Edit className="w-4 h-4" />}
                          onPress={() => handleEdit(cls)}
                        >
                          Edit
                        </Button>
                        <Button
                          size="sm"
                          color="danger"
                          variant="light"
                          startContent={<Trash2 className="w-4 h-4" />}
                          onPress={() => handleDelete(cls.id)}
                        >
                          Hapus
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardBody>
        </Card>
      </motion.div>

      {/* Create/Edit Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="2xl">
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader>
                <h3>{isEditing ? 'Edit Kelas' : 'Tambah Kelas Baru'}</h3>
              </ModalHeader>
              <ModalBody>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Nama Kelas"
                    placeholder="Contoh: 7A, 8B"
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    isRequired
                  />
                  
                  <Select
                    label="Tingkat Kelas"
                    placeholder="Pilih tingkat"
                    selectedKeys={formData.grade_level ? [formData.grade_level] : []}
                    onSelectionChange={(keys) => {
                      const level = Array.from(keys)[0] as string;
                      setFormData({...formData, grade_level: level});
                    }}
                    isRequired
                  >
                    {gradeOptions.map((option) => (
                      <SelectItem key={option.key} value={option.key}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </Select>

                  <Input
                    label="Tahun Ajaran"
                    placeholder="2024/2025"
                    value={formData.academic_year}
                    onChange={(e) => setFormData({...formData, academic_year: e.target.value})}
                    isRequired
                  />

                  <Select
                    label="Sekolah"
                    placeholder="Pilih sekolah"
                    selectedKeys={formData.school_id ? [formData.school_id] : []}
                    onSelectionChange={(keys) => {
                      const schoolId = Array.from(keys)[0] as string;
                      setFormData({...formData, school_id: schoolId});
                    }}
                  >
                    {schools.map((school) => (
                      <SelectItem key={school.id} value={school.id}>
                        {school.name}
                      </SelectItem>
                    ))}
                  </Select>
                </div>
              </ModalBody>
              <ModalFooter>
                <Button variant="light" onPress={onClose}>
                  Batal
                </Button>
                <Button color="primary" onPress={handleSave}>
                  {isEditing ? 'Update' : 'Simpan'}
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </div>
  );
};

export default ClassManager;
