version: '3'
services:
  postgres:
    image: postgres:14
    environment:
      POSTGRES_USER: directus
      POSTGRES_PASSWORD: directus
      POSTGRES_DB: directus
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - directus-net

  directus:
    image: directus/directus:latest
    ports:
      - 8055:8055
    environment:
      KEY: 'secret-key'
      SECRET: 'secret-secret'
      ADMIN_EMAIL: '<EMAIL>'
      ADMIN_PASSWORD: 'admin123'
      DB_CLIENT: 'pg'
      DB_HOST: 'postgres'
      DB_PORT: '5432'
      DB_DATABASE: 'directus'
      DB_USER: 'directus'
      DB_PASSWORD: 'directus'
    depends_on:
      - postgres
    networks:
      - directus-net

volumes:
  postgres_data:

networks:
  directus-net:
    driver: bridge