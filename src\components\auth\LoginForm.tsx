// Updated: Login form with Directus authentication
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button, Input, Card, CardBody, CardHeader, Select, SelectItem } from '@heroui/react';
import { Eye, EyeOff, User, Lock, GraduationCap } from 'lucide-react';
import { useAuthStore } from '../../stores/authStore';
import { motion } from 'framer-motion';

const loginSchema = z.object({
  email: z.string().email('Email tidak valid'),
  password: z.string().min(6, 'Password minimal 6 karakter'),
  role: z.enum(['admin', 'guru', 'siswa'], {
    required_error: 'Pilih role terlebih dahulu'
  })
});

type LoginFormData = z.infer<typeof loginSchema>;

interface LoginFormProps {
  onSuccess?: () => void;
}

export const LoginForm: React.FC<LoginFormProps> = ({ onSuccess }) => {
  const [showPassword, setShowPassword] = useState(false);
  const { login, isLoading, error, clearError } = useAuthStore();
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema)
  });

  const selectedRole = watch('role');

  const onSubmit = async (data: LoginFormData) => {
    clearError();
    const success = await login(data.email, data.password);
    
    if (success) {
      onSuccess?.();
    }
  };

  const roleOptions = [
    { key: 'admin', label: 'Administrator', icon: '👨‍💼' },
    { key: 'guru', label: 'Guru', icon: '👨‍🏫' },
    { key: 'siswa', label: 'Siswa', icon: '👨‍🎓' }
  ];

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <Card className="shadow-2xl border-0">
          <CardHeader className="text-center pb-2">
            <div className="flex flex-col items-center space-y-2">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
                <GraduationCap className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-2xl font-bold text-gray-900">
                Guru Digital Pelangi
              </h1>
              <p className="text-gray-600 text-sm">
                Sistem Administrasi Guru Modern
              </p>
            </div>
          </CardHeader>
          
          <CardBody className="pt-2">
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              {/* Role Selection */}
              <div>
                <Select
                  label="Pilih Role"
                  placeholder="Pilih role Anda"
                  selectedKeys={selectedRole ? [selectedRole] : []}
                  onSelectionChange={(keys) => {
                    const role = Array.from(keys)[0] as string;
                    setValue('role', role as any);
                  }}
                  startContent={<User className="w-4 h-4" />}
                  isInvalid={!!errors.role}
                  errorMessage={errors.role?.message}
                  variant="bordered"
                >
                  {roleOptions.map((role) => (
                    <SelectItem key={role.key} value={role.key}>
                      <div className="flex items-center gap-2">
                        <span>{role.icon}</span>
                        <span>{role.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </Select>
              </div>

              {/* Email Input */}
              <div>
                <Input
                  {...register('email')}
                  type="email"
                  label="Email"
                  placeholder="Masukkan email Anda"
                  startContent={<User className="w-4 h-4 text-gray-400" />}
                  isInvalid={!!errors.email}
                  errorMessage={errors.email?.message}
                  variant="bordered"
                />
              </div>

              {/* Password Input */}
              <div>
                <Input
                  {...register('password')}
                  type={showPassword ? 'text' : 'password'}
                  label="Password"
                  placeholder="Masukkan password Anda"
                  startContent={<Lock className="w-4 h-4 text-gray-400" />}
                  endContent={
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="focus:outline-none"
                    >
                      {showPassword ? (
                        <EyeOff className="w-4 h-4 text-gray-400" />
                      ) : (
                        <Eye className="w-4 h-4 text-gray-400" />
                      )}
                    </button>
                  }
                  isInvalid={!!errors.password}
                  errorMessage={errors.password?.message}
                  variant="bordered"
                />
              </div>

              {/* Error Message */}
              {error && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  className="bg-red-50 border border-red-200 rounded-lg p-3"
                >
                  <p className="text-red-600 text-sm">{error}</p>
                </motion.div>
              )}

              {/* Login Button */}
              <Button
                type="submit"
                color="primary"
                size="lg"
                className="w-full font-semibold"
                isLoading={isLoading}
                disabled={isLoading}
              >
                {isLoading ? 'Masuk...' : 'Masuk'}
              </Button>
            </form>

            {/* Demo Credentials */}
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <p className="text-xs text-gray-600 mb-2 font-semibold">Demo Credentials:</p>
              <div className="space-y-1 text-xs text-gray-500">
                <p><strong>Admin:</strong> <EMAIL> / admin123</p>
                <p><strong>Guru:</strong> <EMAIL> / guru123</p>
                <p><strong>Siswa:</strong> <EMAIL> / siswa123</p>
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>
    </div>
  );
};
