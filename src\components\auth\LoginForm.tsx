// Updated: Login form with Directus authentication
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button, Input, Card, CardBody, CardHeader, Select, SelectItem } from '@heroui/react';
import { Eye, EyeOff, User, Lock, GraduationCap } from 'lucide-react';
import { useAuthStore } from '../../stores/authStore';
import { motion } from 'framer-motion';

const loginSchema = z.object({
  identifier: z.string().min(1, 'NIP/NISN tidak boleh kosong'),
  password: z.string().min(6, 'Password minimal 6 karakter')
});

type LoginFormData = z.infer<typeof loginSchema>;

interface LoginFormProps {
  onSuccess?: () => void;
}

export const LoginForm: React.FC<LoginFormProps> = ({ onSuccess }) => {
  const [showPassword, setShowPassword] = useState(false);
  const { login, isLoading, error, clearError } = useAuthStore();

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema)
  });

  const identifier = watch('identifier');

  // Auto-detect user type based on identifier
  const getUserType = (identifier: string) => {
    if (!identifier) return null;
    if (/^\d{10}$/.test(identifier)) return 'siswa'; // NISN (10 digits)
    if (/^\d{18}$/.test(identifier)) return 'guru'; // NIP (18 digits)
    if (identifier.includes('@')) return 'admin'; // Email for admin
    return null;
  };

  const userType = getUserType(identifier);

  const onSubmit = async (data: LoginFormData) => {
    clearError();
    const success = await login(data.identifier, data.password);

    if (success) {
      onSuccess?.();
    }
  };

  const roleOptions = [
    { key: 'admin', label: 'Administrator', icon: '👨‍💼' },
    { key: 'guru', label: 'Guru', icon: '👨‍🏫' },
    { key: 'siswa', label: 'Siswa', icon: '👨‍🎓' }
  ];

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <Card className="shadow-2xl border-0">
          <CardHeader className="text-center pb-2">
            <div className="flex flex-col items-center space-y-2">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
                <GraduationCap className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-2xl font-bold text-gray-900">
                Guru Digital Pelangi
              </h1>
              <p className="text-gray-600 text-sm">
                Sistem Administrasi Guru Modern
              </p>
            </div>
          </CardHeader>
          
          <CardBody className="pt-2">
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              {/* User Type Indicator */}
              {userType && (
                <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg">
                  <span className="text-2xl">
                    {userType === 'siswa' ? '👨‍🎓' : userType === 'guru' ? '👨‍🏫' : '👨‍💼'}
                  </span>
                  <div>
                    <p className="text-sm font-medium text-blue-900">
                      Login sebagai {userType === 'siswa' ? 'Siswa' : userType === 'guru' ? 'Guru' : 'Admin'}
                    </p>
                    <p className="text-xs text-blue-600">
                      {userType === 'siswa' ? 'NISN terdeteksi' : userType === 'guru' ? 'NIP terdeteksi' : 'Email admin terdeteksi'}
                    </p>
                  </div>
                </div>
              )}

              {/* NIP/NISN Input */}
              <div>
                <Input
                  {...register('identifier')}
                  type="text"
                  label="NIP/NISN/Email"
                  placeholder="Masukkan NIP (18 digit), NISN (10 digit), atau Email"
                  startContent={<User className="w-4 h-4 text-gray-400" />}
                  isInvalid={!!errors.identifier}
                  errorMessage={errors.identifier?.message}
                  variant="bordered"
                  description={
                    <div className="text-xs text-gray-500 mt-1">
                      <p>• Guru/Admin: NIP (18 digit) atau Email</p>
                      <p>• Siswa: NISN (10 digit)</p>
                    </div>
                  }
                />
              </div>

              {/* Password Input */}
              <div>
                <Input
                  {...register('password')}
                  type={showPassword ? 'text' : 'password'}
                  label="Password"
                  placeholder="Masukkan password Anda"
                  startContent={<Lock className="w-4 h-4 text-gray-400" />}
                  endContent={
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="focus:outline-none"
                    >
                      {showPassword ? (
                        <EyeOff className="w-4 h-4 text-gray-400" />
                      ) : (
                        <Eye className="w-4 h-4 text-gray-400" />
                      )}
                    </button>
                  }
                  isInvalid={!!errors.password}
                  errorMessage={errors.password?.message}
                  variant="bordered"
                />
              </div>

              {/* Error Message */}
              {error && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  className="bg-red-50 border border-red-200 rounded-lg p-3"
                >
                  <p className="text-red-600 text-sm">{error}</p>
                </motion.div>
              )}

              {/* Login Button */}
              <Button
                type="submit"
                color="primary"
                size="lg"
                className="w-full font-semibold"
                isLoading={isLoading}
                disabled={isLoading}
              >
                {isLoading ? 'Masuk...' : 'Masuk'}
              </Button>
            </form>

            {/* Demo Credentials */}
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <p className="text-xs text-gray-600 mb-2 font-semibold">Demo Credentials:</p>
              <div className="space-y-1 text-xs text-gray-500">
                <p><strong>Admin:</strong> <EMAIL> / admin123</p>
                <p><strong>Guru:</strong> 123456789012345678 / guru123</p>
                <p><strong>Siswa:</strong> 1234567890 / siswa123</p>
              </div>
              <p className="text-xs text-gray-400 mt-2">
                * NIP: 18 digit, NISN: 10 digit
              </p>
            </div>
          </CardBody>
        </Card>
      </motion.div>
    </div>
  );
};
