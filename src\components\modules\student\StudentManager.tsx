// Student Management Component for Guru Digital Pelangi
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Card, 
  CardBody, 
  CardHeader, 
  Button, 
  Input, 
  Table, 
  TableHeader, 
  TableColumn, 
  TableBody, 
  TableRow, 
  TableCell,
  Chip,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Select,
  SelectItem,
  Avatar,
  Tabs,
  Tab
} from '@heroui/react';
import { Plus, Search, Edit, Trash2, Users, UserPlus, Award, TrendingUp } from 'lucide-react';
import { studentService, classService, Student } from '../../../services/expressApi';

const StudentManager: React.FC = () => {
  const [students, setStudents] = useState<Student[]>([]);
  const [filteredStudents, setFilteredStudents] = useState<Student[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClass, setSelectedClass] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [classes, setClasses] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState('students');
  const { isOpen, onOpen, onClose } = useDisclosure();

  // Form state
  const [formData, setFormData] = useState({
    student_id: '',
    first_name: '',
    last_name: '',
    email: '',
    class_id: '',
    date_of_birth: '',
    gender: '',
    address: '',
    phone: '',
    parent_name: '',
    parent_phone: '',
    status: 'active'
  });

  useEffect(() => {
    loadStudents();
    loadClasses();
  }, []);

  useEffect(() => {
    filterStudents();
  }, [searchTerm, selectedClass, students]);

  const loadStudents = async () => {
    setIsLoading(true);
    const response = await studentService.getStudents();
    if (response.success && response.data) {
      setStudents(response.data);
    }
    setIsLoading(false);
  };

  const loadClasses = async () => {
    const response = await classService.getClasses();
    if (response.success && response.data) {
      setClasses(response.data);
    }
  };

  const filterStudents = () => {
    let filtered = students;

    if (searchTerm) {
      filtered = filtered.filter(student =>
        student.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        student.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        student.student_id.includes(searchTerm)
      );
    }

    if (selectedClass) {
      filtered = filtered.filter(student => student.class_id === selectedClass);
    }

    setFilteredStudents(filtered);
  };

  const handleCreate = () => {
    setSelectedStudent(null);
    setIsEditing(false);
    setFormData({
      student_id: '',
      first_name: '',
      last_name: '',
      email: '',
      class_id: classes.length > 0 ? classes[0].id : '',
      date_of_birth: '',
      gender: '',
      address: '',
      phone: '',
      parent_name: '',
      parent_phone: '',
      status: 'active'
    });
    onOpen();
  };

  const handleEdit = (student: Student) => {
    setSelectedStudent(student);
    setIsEditing(true);
    setFormData({
      student_id: student.studentId,
      first_name: student.firstName,
      last_name: student.lastName,
      email: student.email || '',
      class_id: student.classId || '',
      date_of_birth: student.dateOfBirth || '',
      gender: student.gender || '',
      address: student.address || '',
      phone: student.phone || '',
      parent_name: student.parentName || '',
      parent_phone: student.parentPhone || '',
      status: student.status
    });
    onOpen();
  };

  const handleSave = async () => {
    try {
      console.log('Saving student data:', formData);

      if (isEditing && selectedStudent) {
        const response = await studentService.updateStudent(selectedStudent.id, formData);
        console.log('Update response:', response);
        if (response.success) {
          loadStudents();
          onClose();
          alert('Siswa berhasil diupdate!');
        } else {
          alert('Error: ' + (response.error || 'Gagal update siswa'));
        }
      } else {
        const response = await studentService.createStudent(formData);
        console.log('Create response:', response);
        if (response.success) {
          loadStudents();
          onClose();
          alert('Siswa berhasil dibuat!');
        } else {
          alert('Error: ' + (response.error || 'Gagal membuat siswa'));
        }
      }
    } catch (error) {
      console.error('Save error:', error);
      alert('Terjadi error saat menyimpan data');
    }
  };

  const handleDelete = async (id: string) => {
    if (confirm('Apakah Anda yakin ingin menghapus siswa ini?')) {
      try {
        const response = await studentService.deleteStudent(id);
        console.log('Delete response:', response);
        if (response.success) {
          loadStudents();
          alert('Siswa berhasil dihapus!');
        } else {
          alert('Error: ' + (response.error || 'Gagal menghapus siswa'));
        }
      } catch (error) {
        console.error('Delete error:', error);
        alert('Terjadi error saat menghapus data');
      }
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'success';
      case 'INACTIVE': return 'warning';
      case 'GRADUATED': return 'primary';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'Aktif';
      case 'INACTIVE': return 'Tidak Aktif';
      case 'GRADUATED': return 'Lulus';
      default: return 'Unknown';
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Users className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold">Manajemen Siswa</h1>
                <p className="text-gray-600">Kelola data siswa dan informasi akademik</p>
              </div>
            </div>
            <Button
              color="primary"
              startContent={<Plus className="w-4 h-4" />}
              onPress={handleCreate}
            >
              Tambah Siswa
            </Button>
          </CardHeader>
        </Card>
      </motion.div>

      {/* Search and Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Input
                placeholder="Cari nama atau NISN..."
                startContent={<Search className="w-4 h-4 text-gray-400" />}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              
              <Select
                label="Filter Kelas"
                placeholder="Semua kelas"
                selectedKeys={selectedClass ? [selectedClass] : []}
                onSelectionChange={(keys) => {
                  const classId = Array.from(keys)[0] as string;
                  setSelectedClass(classId);
                }}
              >
                <SelectItem key="" value="">Semua Kelas</SelectItem>
                {classes.map((cls) => (
                  <SelectItem key={cls.id} value={cls.id}>
                    {cls.name}
                  </SelectItem>
                ))}
              </Select>

              <div className="flex items-center justify-end">
                <Chip color="primary" variant="flat">
                  {filteredStudents.length} Siswa
                </Chip>
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Students Table */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Daftar Siswa</h3>
          </CardHeader>
          <CardBody>
            <Table aria-label="Students table">
              <TableHeader>
                <TableColumn>SISWA</TableColumn>
                <TableColumn>NISN</TableColumn>
                <TableColumn>KELAS</TableColumn>
                <TableColumn>JENIS KELAMIN</TableColumn>
                <TableColumn>STATUS</TableColumn>
                <TableColumn>AKSI</TableColumn>
              </TableHeader>
              <TableBody>
                {filteredStudents.map((student) => (
                  <TableRow key={student.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar
                          name={`${student.firstName?.charAt(0) || ''}${student.lastName?.charAt(0) || ''}`}
                          className="bg-gradient-to-r from-blue-400 to-purple-400"
                        />
                        <div>
                          <p className="font-medium">
                            {student.firstName} {student.lastName}
                          </p>
                          <p className="text-sm text-gray-600">
                            {student.email || 'Email tidak tersedia'}
                          </p>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="font-mono text-sm">
                        {student.studentId}
                      </span>
                    </TableCell>
                    <TableCell>
                      {student.class?.name ? (
                        <Chip color="primary" variant="flat">
                          {student.class.name}
                        </Chip>
                      ) : (
                        <span className="text-gray-400">Belum ditentukan</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <span className="capitalize">
                        {student.gender === 'L' ? 'Laki-laki' : student.gender === 'P' ? 'Perempuan' : '-'}
                      </span>
                    </TableCell>
                    <TableCell>
                      <Chip
                        color={getStatusColor(student.status) as any}
                        variant="flat"
                      >
                        {getStatusText(student.status)}
                      </Chip>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="light"
                          startContent={<Edit className="w-4 h-4" />}
                          onPress={() => handleEdit(student)}
                        >
                          Edit
                        </Button>
                        <Button
                          size="sm"
                          color="danger"
                          variant="light"
                          startContent={<Trash2 className="w-4 h-4" />}
                          onPress={() => handleDelete(student.id)}
                        >
                          Hapus
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardBody>
        </Card>
      </motion.div>

      {/* Create/Edit Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="3xl">
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader>
                <h3>{isEditing ? 'Edit Siswa' : 'Tambah Siswa Baru'}</h3>
              </ModalHeader>
              <ModalBody>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="NISN"
                    placeholder="10 digit NISN"
                    value={formData.student_id}
                    onChange={(e) => setFormData({...formData, student_id: e.target.value})}
                    isRequired
                    maxLength={10}
                  />
                  
                  <Select
                    label="Kelas"
                    placeholder="Pilih kelas"
                    selectedKeys={formData.class_id ? [formData.class_id] : []}
                    onSelectionChange={(keys) => {
                      const classId = Array.from(keys)[0] as string;
                      setFormData({...formData, class_id: classId});
                    }}
                    isRequired
                  >
                    {classes.map((cls) => (
                      <SelectItem key={cls.id} value={cls.id}>
                        {cls.name}
                      </SelectItem>
                    ))}
                  </Select>

                  <Input
                    label="Nama Depan"
                    placeholder="Nama depan siswa"
                    value={formData.first_name}
                    onChange={(e) => setFormData({...formData, first_name: e.target.value})}
                    isRequired
                  />

                  <Input
                    label="Nama Belakang"
                    placeholder="Nama belakang siswa"
                    value={formData.last_name}
                    onChange={(e) => setFormData({...formData, last_name: e.target.value})}
                    isRequired
                  />

                  <Input
                    label="Email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={(e) => setFormData({...formData, email: e.target.value})}
                  />

                  <Select
                    label="Jenis Kelamin"
                    placeholder="Pilih jenis kelamin"
                    selectedKeys={formData.gender ? [formData.gender] : []}
                    onSelectionChange={(keys) => {
                      const gender = Array.from(keys)[0] as string;
                      setFormData({...formData, gender});
                    }}
                  >
                    <SelectItem key="L" value="L">Laki-laki</SelectItem>
                    <SelectItem key="P" value="P">Perempuan</SelectItem>
                  </Select>

                  <Input
                    label="Tanggal Lahir"
                    type="date"
                    value={formData.date_of_birth}
                    onChange={(e) => setFormData({...formData, date_of_birth: e.target.value})}
                  />

                  <Input
                    label="No. Telepon"
                    placeholder="08xxxxxxxxxx"
                    value={formData.phone}
                    onChange={(e) => setFormData({...formData, phone: e.target.value})}
                  />

                  <Input
                    label="Nama Orang Tua"
                    placeholder="Nama orang tua/wali"
                    value={formData.parent_name}
                    onChange={(e) => setFormData({...formData, parent_name: e.target.value})}
                    className="md:col-span-2"
                  />

                  <Input
                    label="No. Telepon Orang Tua"
                    placeholder="08xxxxxxxxxx"
                    value={formData.parent_phone}
                    onChange={(e) => setFormData({...formData, parent_phone: e.target.value})}
                    className="md:col-span-2"
                  />
                </div>
              </ModalBody>
              <ModalFooter>
                <Button variant="light" onPress={onClose}>
                  Batal
                </Button>
                <Button color="primary" onPress={handleSave}>
                  {isEditing ? 'Update' : 'Simpan'}
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </div>
  );
};

export default StudentManager;
