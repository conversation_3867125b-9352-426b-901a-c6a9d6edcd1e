// Student Management Component for Guru Digital Pelangi
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Card, 
  CardBody, 
  CardHeader, 
  Button, 
  Input, 
  Table, 
  TableHeader, 
  TableColumn, 
  TableBody, 
  TableRow, 
  TableCell,
  Chip,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Select,
  SelectItem,
  Avatar,
  Tabs,
  Tab
} from '@heroui/react';
import { Plus, Search, Edit, Trash2, Users, UserPlus, Award, TrendingUp, Upload } from 'lucide-react';
import EmptyState from '../../common/EmptyState';
import BulkImportModal from './BulkImportModal';
import { studentService, classService, Student } from '../../../services/expressApi';

const StudentManager: React.FC = () => {
  const [students, setStudents] = useState<Student[]>([]);
  const [filteredStudents, setFilteredStudents] = useState<Student[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClass, setSelectedClass] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [classes, setClasses] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState('students');
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isProfileOpen, onOpen: onProfileOpen, onClose: onProfileClose } = useDisclosure();
  const { isOpen: isBulkImportOpen, onOpen: onBulkImportOpen, onClose: onBulkImportClose } = useDisclosure();
  const [profileStudent, setProfileStudent] = useState<Student | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    studentId: '',
    fullName: '',
    email: '',
    classId: '',
    dateOfBirth: '',
    gender: '',
    address: '',
    phone: '',
    parentName: '',
    parentPhone: '',
    status: 'ACTIVE'
  });

  useEffect(() => {
    loadStudents();
    loadClasses();
  }, []);

  useEffect(() => {
    filterStudents();
  }, [searchTerm, selectedClass, students]);

  const loadStudents = async () => {
    setIsLoading(true);
    const response = await studentService.getStudents();
    if (response.success && response.data) {
      setStudents(response.data);
    }
    setIsLoading(false);
  };

  const loadClasses = async () => {
    const response = await classService.getClasses();
    if (response.success && response.data) {
      setClasses(response.data);
    }
  };

  const filterStudents = () => {
    let filtered = students;

    if (searchTerm) {
      filtered = filtered.filter(student =>
        student.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        student.studentId.includes(searchTerm)
      );
    }

    if (selectedClass) {
      filtered = filtered.filter(student => student.classId === selectedClass);
    }

    setFilteredStudents(filtered);
  };

  const handleCreate = () => {
    setSelectedStudent(null);
    setIsEditing(false);
    setFormData({
      studentId: '',
      fullName: '',
      email: '',
      classId: classes.length > 0 ? classes[0].id : '',
      dateOfBirth: '',
      gender: '',
      address: '',
      phone: '',
      parentName: '',
      parentPhone: '',
      status: 'ACTIVE'
    });
    onOpen();
  };

  const handleEdit = (student: Student) => {
    setSelectedStudent(student);
    setIsEditing(true);
    setFormData({
      studentId: student.studentId,
      fullName: student.fullName,
      email: student.email || '',
      classId: student.classId || '',
      dateOfBirth: student.dateOfBirth || '',
      gender: student.gender || '',
      address: student.address || '',
      phone: student.phone || '',
      parentName: student.parentName || '',
      parentPhone: student.parentPhone || '',
      status: student.status
    });
    onOpen();
  };

  const handleSave = async () => {
    try {
      console.log('Saving student data:', formData);

      if (isEditing && selectedStudent) {
        const response = await studentService.updateStudent(selectedStudent.id, formData);
        console.log('Update response:', response);
        if (response.success) {
          loadStudents();
          onClose();
          alert('Siswa berhasil diupdate!');
        } else {
          alert('Error: ' + (response.error || 'Gagal update siswa'));
        }
      } else {
        const response = await studentService.createStudent(formData);
        console.log('Create response:', response);
        if (response.success) {
          loadStudents();
          onClose();
          alert('Siswa berhasil dibuat!');
        } else {
          alert('Error: ' + (response.error || 'Gagal membuat siswa'));
        }
      }
    } catch (error) {
      console.error('Save error:', error);
      alert('Terjadi error saat menyimpan data');
    }
  };

  const handleDelete = async (id: string) => {
    if (confirm('Apakah Anda yakin ingin menghapus siswa ini?')) {
      try {
        const response = await studentService.deleteStudent(id);
        console.log('Delete response:', response);
        if (response.success) {
          loadStudents();
          alert('Siswa berhasil dihapus!');
        } else {
          alert('Error: ' + (response.error || 'Gagal menghapus siswa'));
        }
      } catch (error) {
        console.error('Delete error:', error);
        alert('Terjadi error saat menghapus data');
      }
    }
  };

  const handleRowClick = (student: Student) => {
    setProfileStudent(student);
    onProfileOpen();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'success';
      case 'INACTIVE': return 'warning';
      case 'GRADUATED': return 'primary';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'Aktif';
      case 'INACTIVE': return 'Tidak Aktif';
      case 'GRADUATED': return 'Lulus';
      default: return 'Unknown';
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Users className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold">Manajemen Siswa</h1>
                <p className="text-gray-600">Kelola data siswa dan informasi akademik</p>
              </div>
            </div>
            <div className="flex gap-3">
              <Button
                variant="flat"
                startContent={<Upload className="w-4 h-4" />}
                onPress={onBulkImportOpen}
              >
                Bulk Import
              </Button>
              <Button
                color="primary"
                startContent={<Plus className="w-4 h-4" />}
                onPress={handleCreate}
              >
                Tambah Siswa
              </Button>
            </div>
          </CardHeader>
        </Card>
      </motion.div>

      {/* Search and Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Input
                placeholder="Cari nama atau NISN..."
                aria-label="Cari siswa berdasarkan nama atau NISN"
                startContent={<Search className="w-4 h-4 text-gray-400" />}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              
              <Select
                label="Filter Kelas"
                placeholder="Semua kelas"
                selectedKeys={selectedClass ? [selectedClass] : []}
                onSelectionChange={(keys) => {
                  const classId = Array.from(keys)[0] as string;
                  setSelectedClass(classId);
                }}
              >
                <SelectItem key="" value="">Semua Kelas</SelectItem>
                {classes.map((cls) => (
                  <SelectItem key={cls.id} value={cls.id}>
                    {cls.name}
                  </SelectItem>
                ))}
              </Select>

              <div className="flex items-center justify-end">
                <Chip color="primary" variant="flat">
                  {filteredStudents.length} Siswa
                </Chip>
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Students Table */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Daftar Siswa</h3>
          </CardHeader>
          <CardBody>
            <Table aria-label="Students table">
              <TableHeader>
                <TableColumn>SISWA</TableColumn>
                <TableColumn>NISN</TableColumn>
                <TableColumn>KELAS</TableColumn>
                <TableColumn>JENIS KELAMIN</TableColumn>
                <TableColumn>STATUS</TableColumn>
                <TableColumn>AKSI</TableColumn>
              </TableHeader>
              <TableBody emptyContent={
                <EmptyState
                  icon={Users}
                  title="Belum ada siswa"
                  description="Mulai dengan menambahkan siswa pertama untuk memulai proses pembelajaran dan pengelolaan kelas"
                  actionLabel="Tambah Siswa Pertama"
                  onAction={handleCreate}
                  actionColor="primary"
                />
              }>
                {filteredStudents.map((student) => (
                  <TableRow
                    key={student.id}
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleRowClick(student)}
                  >
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar
                          name={student.fullName?.split(' ').map(n => n.charAt(0)).join('') || 'S'}
                          className="bg-gradient-to-r from-blue-400 to-purple-400"
                        />
                        <div>
                          <p className="font-medium">
                            {student.fullName}
                          </p>
                          <p className="text-sm text-gray-600">
                            {student.email || 'Email tidak tersedia'}
                          </p>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="font-mono text-sm">
                        {student.studentId}
                      </span>
                    </TableCell>
                    <TableCell>
                      {student.class?.name ? (
                        <Chip color="primary" variant="flat">
                          {student.class.name}
                        </Chip>
                      ) : (
                        <span className="text-gray-400">Belum ditentukan</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <span className="capitalize">
                        {student.gender === 'L' ? 'Laki-laki' : student.gender === 'P' ? 'Perempuan' : '-'}
                      </span>
                    </TableCell>
                    <TableCell>
                      <Chip
                        color={getStatusColor(student.status) as any}
                        variant="flat"
                      >
                        {getStatusText(student.status)}
                      </Chip>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2" onClick={(e) => e.stopPropagation()}>
                        <Button
                          size="sm"
                          variant="light"
                          startContent={<Edit className="w-4 h-4" />}
                          onPress={() => handleEdit(student)}
                        >
                          Edit
                        </Button>
                        <Button
                          size="sm"
                          color="danger"
                          variant="light"
                          startContent={<Trash2 className="w-4 h-4" />}
                          onPress={() => handleDelete(student.id)}
                        >
                          Hapus
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardBody>
        </Card>
      </motion.div>

      {/* Create/Edit Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="3xl">
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader>
                <h3>{isEditing ? 'Edit Siswa' : 'Tambah Siswa Baru'}</h3>
              </ModalHeader>
              <ModalBody>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="NISN"
                    placeholder="10 digit NISN"
                    value={formData.studentId}
                    onChange={(e) => setFormData({...formData, studentId: e.target.value})}
                    isRequired
                    maxLength={10}
                  />

                  <Select
                    label="Kelas"
                    placeholder="Pilih kelas"
                    selectedKeys={formData.classId ? [formData.classId] : []}
                    onSelectionChange={(keys) => {
                      const classId = Array.from(keys)[0] as string;
                      setFormData({...formData, classId});
                    }}
                    isRequired
                  >
                    {classes.map((cls) => (
                      <SelectItem key={cls.id} value={cls.id}>
                        {cls.name}
                      </SelectItem>
                    ))}
                  </Select>

                  <Input
                    label="Nama Lengkap"
                    placeholder="Nama lengkap siswa"
                    value={formData.fullName}
                    onChange={(e) => setFormData({...formData, fullName: e.target.value})}
                    isRequired
                    className="md:col-span-2"
                  />

                  <Input
                    label="Email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={(e) => setFormData({...formData, email: e.target.value})}
                  />

                  <Select
                    label="Jenis Kelamin"
                    placeholder="Pilih jenis kelamin"
                    selectedKeys={formData.gender ? [formData.gender] : []}
                    onSelectionChange={(keys) => {
                      const gender = Array.from(keys)[0] as string;
                      setFormData({...formData, gender});
                    }}
                  >
                    <SelectItem key="L" value="L">Laki-laki</SelectItem>
                    <SelectItem key="P" value="P">Perempuan</SelectItem>
                  </Select>

                  <Input
                    label="Tanggal Lahir"
                    type="date"
                    value={formData.dateOfBirth}
                    onChange={(e) => setFormData({...formData, dateOfBirth: e.target.value})}
                  />

                  <Input
                    label="No. Telepon"
                    placeholder="08xxxxxxxxxx"
                    value={formData.phone}
                    onChange={(e) => setFormData({...formData, phone: e.target.value})}
                  />

                  <Input
                    label="Alamat"
                    placeholder="Alamat lengkap"
                    value={formData.address}
                    onChange={(e) => setFormData({...formData, address: e.target.value})}
                    className="md:col-span-2"
                  />

                  <Input
                    label="Nama Orang Tua"
                    placeholder="Nama orang tua/wali"
                    value={formData.parentName}
                    onChange={(e) => setFormData({...formData, parentName: e.target.value})}
                  />

                  <Input
                    label="No. Telepon Orang Tua"
                    placeholder="08xxxxxxxxxx"
                    value={formData.parentPhone}
                    onChange={(e) => setFormData({...formData, parentPhone: e.target.value})}
                  />
                </div>
              </ModalBody>
              <ModalFooter>
                <Button variant="light" onPress={onClose}>
                  Batal
                </Button>
                <Button color="primary" onPress={handleSave}>
                  {isEditing ? 'Update' : 'Simpan'}
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>

      {/* Student Profile Modal */}
      <Modal isOpen={isProfileOpen} onClose={onProfileClose} size="2xl">
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader>
                <h3>Profil Siswa</h3>
              </ModalHeader>
              <ModalBody>
                {profileStudent && (
                  <div className="space-y-6">
                    {/* Header Profile */}
                    <div className="flex items-center gap-4 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
                      <Avatar
                        name={profileStudent.fullName?.split(' ').map(n => n.charAt(0)).join('') || 'S'}
                        className="bg-gradient-to-r from-blue-400 to-purple-400 text-white text-xl"
                        size="lg"
                      />
                      <div>
                        <h4 className="text-xl font-bold text-gray-900">{profileStudent.fullName}</h4>
                        <p className="text-gray-600">NISN: {profileStudent.studentId}</p>
                        {profileStudent.class && (
                          <Chip color="primary" variant="flat" size="sm">
                            {profileStudent.class.name}
                          </Chip>
                        )}
                      </div>
                    </div>

                    {/* Student Info */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-600">Email</label>
                        <p className="text-gray-900">{profileStudent.email || 'Tidak tersedia'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">Jenis Kelamin</label>
                        <p className="text-gray-900">
                          {profileStudent.gender === 'L' ? 'Laki-laki' : profileStudent.gender === 'P' ? 'Perempuan' : 'Tidak tersedia'}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">Tanggal Lahir</label>
                        <p className="text-gray-900">
                          {profileStudent.dateOfBirth ? new Date(profileStudent.dateOfBirth).toLocaleDateString('id-ID') : 'Tidak tersedia'}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">No. Telepon</label>
                        <p className="text-gray-900">{profileStudent.phone || 'Tidak tersedia'}</p>
                      </div>
                      <div className="md:col-span-2">
                        <label className="text-sm font-medium text-gray-600">Alamat</label>
                        <p className="text-gray-900">{profileStudent.address || 'Tidak tersedia'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">Nama Orang Tua</label>
                        <p className="text-gray-900">{profileStudent.parentName || 'Tidak tersedia'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600">No. Telepon Orang Tua</label>
                        <p className="text-gray-900">{profileStudent.parentPhone || 'Tidak tersedia'}</p>
                      </div>
                    </div>

                    {/* XP Info */}
                    {profileStudent.studentXp && (
                      <div className="p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg">
                        <h5 className="font-semibold text-gray-900 mb-2">Gamifikasi</h5>
                        <div className="grid grid-cols-3 gap-4 text-center">
                          <div>
                            <p className="text-2xl font-bold text-green-600">{profileStudent.studentXp.totalXp}</p>
                            <p className="text-sm text-gray-600">Total XP</p>
                          </div>
                          <div>
                            <p className="text-2xl font-bold text-blue-600">{profileStudent.studentXp.level}</p>
                            <p className="text-sm text-gray-600">Level</p>
                          </div>
                          <div>
                            <p className="text-lg font-semibold text-purple-600">{profileStudent.studentXp.levelName}</p>
                            <p className="text-sm text-gray-600">Rank</p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </ModalBody>
              <ModalFooter>
                <Button variant="light" onPress={onClose}>
                  Tutup
                </Button>
                <Button
                  color="primary"
                  startContent={<Edit className="w-4 h-4" />}
                  onPress={() => {
                    if (profileStudent) {
                      handleEdit(profileStudent);
                      onClose();
                    }
                  }}
                >
                  Edit Siswa
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>

      {/* Bulk Import Modal */}
      <BulkImportModal
        isOpen={isBulkImportOpen}
        onClose={onBulkImportClose}
        onSuccess={() => {
          loadStudents();
          onBulkImportClose();
        }}
      />
    </div>
  );
};

export default StudentManager;
