// Student Service for Guru Digital Pelangi
import { apiClient, DEMO_MODE, getAuthHeaders, API_BASE_URL } from './apiClient';
import { ApiResponse, Student } from './types';

export const studentService = {
  async getStudents(params?: { page?: number; limit?: number; search?: string; classId?: string }): Promise<ApiResponse<Student[]>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: [
            {
              id: 'student-1',
              studentId: '1234567890',
              fullName: '<PERSON>',
              email: '<EMAIL>',
              classId: 'class-10a',
              gender: 'L',
              status: 'ACTIVE',
              class: {
                name: '10A',
                gradeLevel: '10'
              },
              studentXp: {
                totalXp: 150,
                level: 2,
                levelName: 'Pelajar'
              }
            },
            {
              id: 'student-2',
              studentId: '1234567891',
              fullName: '<PERSON><PERSON>',
              email: '<EMAIL>',
              classId: 'class-10a',
              gender: 'P',
              status: 'ACTIVE',
              class: {
                name: '10A',
                gradeLevel: '10'
              },
              studentXp: {
                totalXp: 200,
                level: 3,
                levelName: 'Cendekiawan'
              }
            }
          ]
        };
      }

      const response = await apiClient.get('/students', { params });
      return {
        success: true,
        data: response.data.data.students,
        pagination: response.data.data.pagination
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal mengambil data siswa'
      };
    }
  },

  async createStudent(data: Omit<Student, 'id' | 'studentXp'>): Promise<ApiResponse<Student>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: { id: 'new-student', ...data } as Student,
          message: 'Siswa berhasil dibuat (Demo Mode)'
        };
      }

      const response = await apiClient.post('/students', data);
      return {
        success: true,
        data: response.data.data.student,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal membuat siswa'
      };
    }
  },

  async updateStudent(id: string, data: Partial<Student>): Promise<ApiResponse<Student>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: { id, ...data } as Student,
          message: 'Siswa berhasil diupdate (Demo Mode)'
        };
      }

      const response = await apiClient.put(`/students/${id}`, data);
      return {
        success: true,
        data: response.data.data.student,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal update siswa'
      };
    }
  },

  async deleteStudent(id: string): Promise<ApiResponse<void>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          message: 'Siswa berhasil dihapus (Demo Mode)'
        };
      }

      const response = await apiClient.delete(`/students/${id}`);
      return {
        success: true,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal menghapus siswa'
      };
    }
  },

  // Bulk import students
  async bulkImportStudents(students: any[]): Promise<ApiResponse<any>> {
    try {
      const response = await fetch(`${API_BASE_URL}/students/bulk`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify({ students }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error bulk importing students:', error);
      return { success: false, error: 'Gagal melakukan bulk import siswa' };
    }
  }
};
