// Teacher Management Component (Admin Only)
// Manages CRUD operations for teachers/guru + bulk import Excel

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Input,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Chip,
  Tooltip,
  Spinner,
  Select,
  SelectItem
} from '@heroui/react';
import { Plus, Edit, Trash2, Users, Upload, Download, Eye } from 'lucide-react';
import EmptyState from './common/EmptyState';
import { useAuthStore } from '../stores/authStore';

interface Teacher {
  id: string;
  firstName: string;
  lastName: string;
  fullName: string;
  email: string;
  nip?: string;
  status: 'ACTIVE' | 'INACTIVE';
  createdAt: string;
  classes: Array<{
    id: string;
    name: string;
    subject: string;
  }>;
  totalClasses: number;
  totalGrades: number;
}

interface TeacherFormData {
  fullName: string;
  email: string;
  nip: string;
  password: string;
  status: 'ACTIVE' | 'INACTIVE';
}

const TeacherManager: React.FC = () => {
  const { user } = useAuthStore();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isImportOpen, onOpen: onImportOpen, onClose: onImportClose } = useDisclosure();
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [editingTeacher, setEditingTeacher] = useState<Teacher | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [formData, setFormData] = useState<TeacherFormData>({
    fullName: '',
    email: '',
    nip: '',
    password: '',
    status: 'ACTIVE'
  });

  // Fetch teachers
  const fetchTeachers = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);
      if (statusFilter) params.append('status', statusFilter);

      const response = await fetch(`http://localhost:5000/api/teachers?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });

      if (response.ok) {
        const result = await response.json();
        setTeachers(result.data);
      }
    } catch (error) {
      console.error('Error fetching teachers:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTeachers();
  }, [searchTerm, statusFilter]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      const url = editingTeacher ? `/api/teachers/${editingTeacher.id}` : '/api/teachers';
      const method = editingTeacher ? 'PUT' : 'POST';

      // Don't send password if editing and password is empty
      const submitData = { ...formData };
      if (editingTeacher && !submitData.password) {
        delete submitData.password;
      }

      const response = await fetch(`http://localhost:5000${url}`, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify(submitData)
      });

      if (response.ok) {
        await fetchTeachers();
        handleCloseModal();
      } else {
        const error = await response.json();
        alert(error.message || 'Terjadi kesalahan');
      }
    } catch (error) {
      console.error('Error saving teacher:', error);
      alert('Terjadi kesalahan saat menyimpan data');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle modal open for create/edit
  const handleOpenModal = (teacher?: Teacher) => {
    if (teacher) {
      setEditingTeacher(teacher);
      setFormData({
        fullName: teacher.fullName,
        email: teacher.email,
        nip: teacher.nip || '',
        password: '', // Don't pre-fill password for editing
        status: teacher.status
      });
    } else {
      setEditingTeacher(null);
      setFormData({
        fullName: '',
        email: '',
        nip: '',
        password: '',
        status: 'ACTIVE'
      });
    }
    onOpen();
  };

  // Handle modal close
  const handleCloseModal = () => {
    setEditingTeacher(null);
    setFormData({
      fullName: '',
      email: '',
      nip: '',
      password: '',
      status: 'ACTIVE'
    });
    onClose();
  };

  // Handle bulk import
  const handleBulkImport = async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await fetch('http://localhost:5000/api/teachers/bulk-import', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: formData
      });

      if (response.ok) {
        const result = await response.json();
        alert(`Berhasil mengimpor ${result.imported} guru`);
        await fetchTeachers();
        onImportClose();
      } else {
        const error = await response.json();
        alert(error.message || 'Gagal mengimpor data');
      }
    } catch (error) {
      console.error('Error importing teachers:', error);
      alert('Terjadi kesalahan saat mengimpor data');
    }
  };

  // Download template Excel
  const downloadTemplate = () => {
    const link = document.createElement('a');
    link.href = 'http://localhost:5000/api/teachers/template';
    link.download = 'template-guru.csv';
    link.click();
  };

  // Check if user is admin
  if (user?.role !== 'ADMIN') {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500">Akses ditolak. Hanya admin yang dapat mengakses halaman ini.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Action Buttons */}
      <div className="flex justify-end gap-2">
        <Button
          color="secondary"
          variant="flat"
          startContent={<Upload className="w-4 h-4" />}
          onPress={onImportOpen}
        >
          Import Excel
        </Button>
        <Button
          color="primary"
          startContent={<Plus className="w-4 h-4" />}
          onPress={() => handleOpenModal()}
        >
          Tambah Guru
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardBody>
          <div className="flex gap-4">
            <Input
              placeholder="Cari nama, email, atau NIP..."
              aria-label="Cari guru berdasarkan nama, email, atau NIP"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1"
            />
            <Select
              placeholder="Filter Status"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-48"
            >
              <SelectItem key="" value="">Semua Status</SelectItem>
              <SelectItem key="ACTIVE" value="ACTIVE">Aktif</SelectItem>
              <SelectItem key="INACTIVE" value="INACTIVE">Nonaktif</SelectItem>
            </Select>
          </div>
        </CardBody>
      </Card>

      {/* Teachers Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            <span className="font-semibold">Daftar Guru ({teachers.length})</span>
          </div>
        </CardHeader>
        <CardBody>
          {loading ? (
            <div className="flex justify-center py-8">
              <Spinner size="lg" />
            </div>
          ) : (
            <Table aria-label="Teachers table">
              <TableHeader>
                <TableColumn>NAMA</TableColumn>
                <TableColumn>EMAIL</TableColumn>
                <TableColumn>NIP</TableColumn>
                <TableColumn>KELAS</TableColumn>
                <TableColumn>STATUS</TableColumn>
                <TableColumn>AKSI</TableColumn>
              </TableHeader>
              <TableBody emptyContent={
                <EmptyState
                  icon={Users}
                  title="Belum ada guru"
                  description="Mulai dengan menambahkan data guru pertama untuk memulai pengelolaan sekolah"
                  actionLabel="Tambah Guru Pertama"
                  onAction={() => handleOpenModal()}
                  actionColor="primary"
                />
              }>
                {teachers.map((teacher) => (
                  <TableRow key={teacher.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{teacher.fullName}</p>
                        <p className="text-sm text-gray-500">
                          {teacher.email}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <p className="text-sm">{teacher.email}</p>
                    </TableCell>
                    <TableCell>
                      <p className="text-sm">{teacher.nip || '-'}</p>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <span className="text-sm">{teacher.totalClasses} kelas</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Chip
                        size="sm"
                        color={teacher.status === 'ACTIVE' ? "success" : "danger"}
                        variant="flat"
                      >
                        {teacher.status === 'ACTIVE' ? "Aktif" : "Nonaktif"}
                      </Chip>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Tooltip content="Lihat Detail">
                          <Button
                            isIconOnly
                            size="sm"
                            variant="light"
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                        </Tooltip>
                        <Tooltip content="Edit">
                          <Button
                            isIconOnly
                            size="sm"
                            variant="light"
                            onPress={() => handleOpenModal(teacher)}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                        </Tooltip>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardBody>
      </Card>

      {/* Create/Edit Modal */}
      <Modal isOpen={isOpen} onClose={handleCloseModal} size="lg">
        <ModalContent>
          <form onSubmit={handleSubmit}>
            <ModalHeader>
              <span>{editingTeacher ? 'Edit' : 'Tambah'} Guru</span>
            </ModalHeader>
            <ModalBody className="space-y-4">
              <Input
                label="Nama Lengkap"
                placeholder="Contoh: Budi Santoso"
                value={formData.fullName}
                onChange={(e) => setFormData({ ...formData, fullName: e.target.value })}
                isRequired
              />
              <Input
                label="Email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                isRequired
              />
              <Input
                label="NIP"
                value={formData.nip}
                onChange={(e) => setFormData({ ...formData, nip: e.target.value })}
              />
              <Input
                label={editingTeacher ? "Password Baru (kosongkan jika tidak diubah)" : "Password"}
                type="password"
                value={formData.password}
                onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                isRequired={!editingTeacher}
              />
              <Select
                label="Status"
                selectedKeys={[formData.status]}
                onChange={(e) => setFormData({ ...formData, status: e.target.value as 'ACTIVE' | 'INACTIVE' })}
              >
                <SelectItem key="ACTIVE" value="ACTIVE">Aktif</SelectItem>
                <SelectItem key="INACTIVE" value="INACTIVE">Nonaktif</SelectItem>
              </Select>
            </ModalBody>
            <ModalFooter>
              <Button variant="light" onPress={handleCloseModal}>
                Batal
              </Button>
              <Button
                color="primary"
                type="submit"
                isLoading={submitting}
              >
                {editingTeacher ? 'Update' : 'Simpan'}
              </Button>
            </ModalFooter>
          </form>
        </ModalContent>
      </Modal>

      {/* Import Modal */}
      <Modal isOpen={isImportOpen} onClose={onImportClose}>
        <ModalContent>
          <ModalHeader>Import Data Guru dari Excel</ModalHeader>
          <ModalBody className="space-y-4">
            <div className="text-sm text-gray-600">
              <p>1. Download template Excel terlebih dahulu</p>
              <p>2. Isi data guru sesuai format template</p>
              <p>3. Upload file Excel yang sudah diisi</p>
            </div>
            <Button
              variant="flat"
              startContent={<Download className="w-4 h-4" />}
              onPress={downloadTemplate}
            >
              Download Template Excel
            </Button>
            <Input
              type="file"
              accept=".xlsx,.xls"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) {
                  handleBulkImport(file);
                }
              }}
            />
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onImportClose}>
              Tutup
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default TeacherManager;
