# 🔧 Backend Setup Guide - Directus Collections

## 📋 Overview

Panduan lengkap untuk setup backend Directus dengan semua collections yang diperlukan untuk sistem Guru Digital Pelangi.

## 🚀 Quick Start

### 1. Access Directus Admin
- URL: `[Your Directus Server URL]/admin`
- Login dengan admin credentials
- Navigate ke **Data Model** section

### 2. Create Collections Order
Buat collections dalam urutan ini untuk menghindari dependency issues:

1. **schools** (Core)
2. **subjects** (Core)  
3. **classes** (Depends on: schools)
4. **students** (Depends on: classes)
5. **grades** (Depends on: students, subjects, classes)
6. **student_xp** (Depends on: students)
7. **badges** (Independent)
8. **student_badges** (Depends on: students, badges)
9. **learning_plans** (Depends on: classes, subjects)
10. **question_categories** (Depends on: subjects)
11. **questions** (Depends on: subjects, question_categories)
12. **question_options** (Depends on: questions)
13. **activities** (Depends on: directus_users)

---

## 📊 Collection Schemas

### 1. Schools Collection
```sql
Collection Name: schools
Fields:
- id (UUID, Primary Key, Hidden)
- name (String, Required, Display Template)
- address (Text)
- phone (String)
- email (String)
- created_at (DateTime, Auto)
- updated_at (DateTime, Auto)
```

### 2. Subjects Collection
```sql
Collection Name: subjects
Fields:
- id (UUID, Primary Key, Hidden)
- name (String, Required, Display Template)
- code (String, Required, Unique)
- description (Text)
- created_at (DateTime, Auto)
- updated_at (DateTime, Auto)
```

### 3. Classes Collection
```sql
Collection Name: classes
Fields:
- id (UUID, Primary Key, Hidden)
- name (String, Required, Display Template)
- grade_level (String, Required)
- school_id (UUID, M2O → schools)
- teacher_id (UUID, M2O → directus_users)
- academic_year (String, Required, Default: "2024/2025")
- student_count (Integer, Default: 0)
- created_at (DateTime, Auto)
- updated_at (DateTime, Auto)
```

### 4. Students Collection
```sql
Collection Name: students
Fields:
- id (UUID, Primary Key, Hidden)
- student_id (String, Required, Unique) // NISN
- first_name (String, Required)
- last_name (String, Required)
- email (String)
- class_id (UUID, M2O → classes)
- date_of_birth (Date)
- gender (String, Options: L, P)
- address (Text)
- phone (String)
- parent_name (String)
- parent_phone (String)
- status (String, Options: active, inactive, graduated, Default: active)
- created_at (DateTime, Auto)
- updated_at (DateTime, Auto)
```

### 5. Grades Collection
```sql
Collection Name: grades
Fields:
- id (UUID, Primary Key, Hidden)
- student_id (UUID, M2O → students)
- subject_id (UUID, M2O → subjects)
- class_id (UUID, M2O → classes)
- grade_type (String, Options: tugas, quiz, ujian, praktik)
- score (Float, Required)
- max_score (Float, Required)
- description (Text)
- date (Date, Required)
- created_by (UUID, M2O → directus_users)
- created_at (DateTime, Auto)
- updated_at (DateTime, Auto)
```

### 6. Student XP Collection
```sql
Collection Name: student_xp
Fields:
- id (UUID, Primary Key, Hidden)
- student_id (UUID, M2O → students, Unique)
- total_xp (Integer, Default: 0)
- level (Integer, Default: 1)
- level_name (String, Options: Pemula, Pelajar, Cendekiawan, Ahli, Master)
- updated_at (DateTime, Auto)
```

### 7. Badges Collection
```sql
Collection Name: badges
Fields:
- id (UUID, Primary Key, Hidden)
- name (String, Required, Display Template)
- description (Text)
- icon (String)
- criteria (Text)
- xp_reward (Integer, Default: 0)
- created_at (DateTime, Auto)
- updated_at (DateTime, Auto)
```

### 8. Student Badges Collection
```sql
Collection Name: student_badges
Fields:
- id (UUID, Primary Key, Hidden)
- student_id (UUID, M2O → students)
- badge_id (UUID, M2O → badges)
- earned_at (DateTime, Auto)
```

### 9. Learning Plans Collection (RPP)
```sql
Collection Name: learning_plans
Fields:
- id (UUID, Primary Key, Hidden)
- title (String, Required, Display Template)
- class_id (UUID, M2O → classes)
- subject_id (UUID, M2O → subjects)
- objectives (JSON)
- materials (JSON)
- methods (JSON)
- activities (JSON)
- assessments (JSON)
- duration (Integer) // in minutes
- date (Date, Required)
- created_by (UUID, M2O → directus_users)
- created_at (DateTime, Auto)
- updated_at (DateTime, Auto)
```

### 10. Question Categories Collection
```sql
Collection Name: question_categories
Fields:
- id (UUID, Primary Key, Hidden)
- name (String, Required, Display Template)
- description (Text)
- subject_id (UUID, M2O → subjects)
- created_at (DateTime, Auto)
- updated_at (DateTime, Auto)
```

### 11. Questions Collection
```sql
Collection Name: questions
Fields:
- id (UUID, Primary Key, Hidden)
- question_text (Text, Required)
- question_type (String, Options: multiple_choice, multiple_choice_complex, true_false, fill_blank, essay)
- difficulty (String, Options: easy, medium, hard)
- subject_id (UUID, M2O → subjects)
- category_id (UUID, M2O → question_categories)
- correct_answer (Text, Required)
- explanation (Text)
- tags (JSON)
- created_by (UUID, M2O → directus_users)
- created_at (DateTime, Auto)
- updated_at (DateTime, Auto)
```

### 12. Question Options Collection
```sql
Collection Name: question_options
Fields:
- id (UUID, Primary Key, Hidden)
- question_id (UUID, M2O → questions)
- option_text (Text, Required)
- is_correct (Boolean, Default: false)
- order_index (Integer)
- created_at (DateTime, Auto)
- updated_at (DateTime, Auto)
```

### 13. Activities Collection
```sql
Collection Name: activities
Fields:
- id (UUID, Primary Key, Hidden)
- type (String, Options: grade, xp, badge, question, exercise)
- title (String, Required)
- description (Text)
- user_id (UUID, M2O → directus_users)
- created_at (DateTime, Auto)
```

---

## 👥 User Management Setup

### Update directus_users Collection
Add these fields to existing directus_users:
```sql
Additional Fields:
- nip (String, Unique) // For admin and guru
- role (String, Options: admin, guru, Default: guru)
```

### Create Sample Users
```sql
-- Admin User
INSERT INTO directus_users (id, first_name, last_name, email, password, role, status, nip)
VALUES ('admin-1', 'Admin', 'System', '<EMAIL>', '$argon2id$...', 'admin', 'active', NULL);

-- Guru User  
INSERT INTO directus_users (id, first_name, last_name, email, password, role, status, nip)
VALUES ('guru-1', 'Budi', 'Santoso', '<EMAIL>', '$argon2id$...', 'guru', 'active', '123456789012345678');
```

---

## 🔐 Permissions Setup

### Admin Role Permissions
- **Full Access**: All collections (CRUD)
- **System Access**: Directus admin panel
- **User Management**: Create/edit users

### Guru Role Permissions
- **Read/Write**: classes, students, grades, learning_plans, questions, question_options, activities, student_xp, student_badges
- **Read Only**: subjects, question_categories, badges, schools
- **No Access**: directus_users (except own profile)

### Siswa Role Permissions (Future)
- **Read Only**: Own student record, own grades, own XP data, badges, leaderboard
- **No Write Access**: Any collection

---

## 📝 Sample Data

### Sample School
```json
{
  "name": "SMA Digital Pelangi",
  "address": "Jl. Pendidikan No. 123, Jakarta",
  "phone": "021-12345678",
  "email": "<EMAIL>"
}
```

### Sample Subjects
```json
[
  {"name": "Matematika", "code": "MTK", "description": "Mata pelajaran Matematika"},
  {"name": "Bahasa Indonesia", "code": "BIN", "description": "Mata pelajaran Bahasa Indonesia"},
  {"name": "IPA", "code": "IPA", "description": "Ilmu Pengetahuan Alam"},
  {"name": "IPS", "code": "IPS", "description": "Ilmu Pengetahuan Sosial"},
  {"name": "Bahasa Inggris", "code": "ENG", "description": "Mata pelajaran Bahasa Inggris"}
]
```

### Sample Classes
```json
[
  {"name": "7A", "grade_level": "7", "academic_year": "2024/2025", "student_count": 30},
  {"name": "7B", "grade_level": "7", "academic_year": "2024/2025", "student_count": 28},
  {"name": "8A", "grade_level": "8", "academic_year": "2024/2025", "student_count": 32}
]
```

### Sample Students
```json
[
  {
    "student_id": "1234567890",
    "first_name": "Ahmad",
    "last_name": "Pratama", 
    "email": "<EMAIL>",
    "gender": "L",
    "status": "active"
  },
  {
    "student_id": "1234567891",
    "first_name": "Siti",
    "last_name": "Nurhaliza",
    "email": "<EMAIL>", 
    "gender": "P",
    "status": "active"
  }
]
```

### Sample Badges
```json
[
  {
    "name": "Perfect Attendance",
    "description": "Hadir sempurna selama 1 bulan",
    "icon": "🎯",
    "criteria": "Tidak absen selama 30 hari berturut-turut",
    "xp_reward": 100
  },
  {
    "name": "Quiz Master", 
    "description": "Menyelesaikan 10 quiz dengan nilai sempurna",
    "icon": "🧠",
    "criteria": "Mendapat nilai 100 pada 10 quiz berbeda",
    "xp_reward": 150
  }
]
```

---

## ⚡ Quick Setup Script

Untuk mempercepat setup, gunakan script ini di Directus admin panel:

1. **Import Collections**: Use Directus schema import feature
2. **Run SQL Scripts**: Execute sample data insertion
3. **Set Permissions**: Configure role-based access
4. **Test API**: Verify all endpoints working

---

## 🔍 Verification Checklist

- [ ] All collections created successfully
- [ ] Relationships properly configured
- [ ] Sample data inserted
- [ ] Permissions set correctly
- [ ] API endpoints accessible
- [ ] Frontend can connect to backend

---

*Setup complete! Ready for frontend integration.*
