// Enhanced Gamification System with Tabs - Guru Digital Pelangi
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Card, 
  CardBody, 
  CardHeader, 
  Button, 
  Input, 
  Chip,
  Tabs,
  Tab,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Avatar,
  Select,
  SelectItem,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Textarea
} from '@heroui/react';
import { 
  Trophy, 
  Star, 
  Award, 
  Users, 
  Plus, 
  Search, 
  Gift, 
  Target,
  TrendingUp,
  Medal,
  Crown,
  Zap,
  Calendar,
  Edit,
  Trash2,
  Shield,
  Flame
} from 'lucide-react';
import GamificationDashboard from './GamificationDashboard';

// Data akan dimuat dari API

const mockLevels = [
  { level: 1, name: '<PERSON><PERSON><PERSON>', xpRequired: 0, benefits: 'Akses dasar ke semua fitur' },
  { level: 2, name: '<PERSON><PERSON><PERSON><PERSON>', xpRequired: 100, benefits: '<PERSON>ks<PERSON> ke quiz tambahan' },
  { level: 3, name: '<PERSON><PERSON>', xpRequired: 300, benefits: 'Akses ke materi advanced' },
  { level: 4, name: '<PERSON><PERSON>', xpRequired: 600, benefits: 'Akses ke proyek khusus' },
  { level: 5, name: 'Master', xpRequired: 1000, benefits: 'Akses ke semua fitur premium' },
  { level: 6, name: 'Grandmaster', xpRequired: 1500, benefits: 'Akses mentor junior' },
  { level: 7, name: 'Legend', xpRequired: 2000, benefits: 'Akses semua konten eksklusif' },
  { level: 8, name: 'Mythic', xpRequired: 2500, benefits: 'Dapat membuat konten sendiri' },
  { level: 9, name: 'Immortal', xpRequired: 3000, benefits: 'Akses ke program khusus' },
  { level: 10, name: 'Divine', xpRequired: 4000, benefits: 'Status tertinggi dengan semua privilege' },
];

// Challenges akan dimuat dari API

const iconOptions = [
  '🏆', '⭐', '🎯', '🔥', '💎', '👑', '🚀', '⚡', '🌟', '🎖️',
  '🥇', '🥈', '🥉', '🎪', '🎨', '📚', '🔬', '🧮', '🌍', '💡'
];

const GamificationTabs: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('xp');
  const [selectedStudent, setSelectedStudent] = useState<any>(null);

  // Data states
  const [students, setStudents] = useState([]);
  const [badges, setBadges] = useState([]);
  const [challenges, setChallenges] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  
  // Modal states
  const { isOpen: isBadgeModalOpen, onOpen: onBadgeModalOpen, onClose: onBadgeModalClose } = useDisclosure();
  const { isOpen: isChallengeModalOpen, onOpen: onChallengeModalOpen, onClose: onChallengeModalClose } = useDisclosure();
  const { isOpen: isRewardModalOpen, onOpen: onRewardModalOpen, onClose: onRewardModalClose } = useDisclosure();

  // Form states
  const [badgeForm, setBadgeForm] = useState({
    name: '',
    description: '',
    xpReward: 50,
    icon: '🏆'
  });

  const [challengeForm, setChallengeForm] = useState({
    title: '',
    description: '',
    duration: 7,
    targetType: 'ALL_STUDENTS',
    xpReward: 100
  });

  const [rewardForm, setRewardForm] = useState({
    type: 'xp',
    xpAmount: 50,
    badgeId: '',
    description: ''
  });

  // Load data on component mount
  useEffect(() => {
    if (activeTab === 'students') {
      loadStudents();
    } else if (activeTab === 'badges') {
      loadBadges();
    } else if (activeTab === 'challenges') {
      loadChallenges();
    }
  }, [activeTab]);

  const loadStudents = async () => {
    setIsLoading(true);
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('http://localhost:5000/api/students', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (response.ok) {
        const result = await response.json();
        setStudents(result.data || []);
      }
    } catch (error) {
      console.error('Error loading students:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadBadges = async () => {
    // TODO: Implement badges API
    setBadges([]);
  };

  const loadChallenges = async () => {
    // TODO: Implement challenges API
    setChallenges([]);
  };

  // Statistics
  const stats = {
    totalStudents: students.length,
    totalBadges: badges.length,
    averageScore: 85,
    topStudent: students.length > 0 ? students[0] : null
  };

  // Filtered and sorted students
  const filteredStudents = students
    .filter((student: any) =>
      student.fullName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.class?.name?.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a: any, b: any) => {
      if (sortBy === 'name') return a.fullName?.localeCompare(b.fullName) || 0;
      if (sortBy === 'xp') return (b.studentXp?.totalXp || 0) - (a.studentXp?.totalXp || 0);
      return 0;
    });

  const handleCreateBadge = () => {
    console.log('Creating badge:', badgeForm);
    setBadgeForm({ name: '', description: '', xpReward: 50, icon: '🏆' });
    onBadgeModalClose();
  };

  const handleCreateChallenge = () => {
    console.log('Creating challenge:', challengeForm);
    setChallengeForm({ title: '', description: '', duration: 7, targetType: 'ALL_STUDENTS', xpReward: 100 });
    onChallengeModalClose();
  };

  const handleGiveReward = () => {
    console.log('Giving reward to:', selectedStudent, rewardForm);
    setRewardForm({ type: 'xp', xpAmount: 50, badgeId: '', description: '' });
    onRewardModalClose();
  };

  const openRewardModal = (student: any) => {
    setSelectedStudent(student);
    onRewardModalOpen();
  };

  const getLevelColor = (level: number) => {
    if (level >= 9) return 'danger'; // Divine/Immortal
    if (level >= 7) return 'secondary'; // Legend/Mythic
    if (level >= 5) return 'warning'; // Master/Grandmaster
    if (level >= 3) return 'success'; // Mahir/Ahli
    if (level >= 2) return 'primary'; // Berkembang
    return 'default'; // Pemula
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card>
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="p-3 bg-gradient-to-br from-purple-100 to-pink-100 rounded-full">
                <Trophy className="w-6 h-6 text-purple-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold">Sistem Gamifikasi</h1>
                <p className="text-gray-600">Kelola poin, badge, level, dan tantangan siswa</p>
              </div>
            </div>
          </CardHeader>
        </Card>
      </motion.div>

      {/* Tabs */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <Tabs 
          selectedKey={activeTab} 
          onSelectionChange={(key) => setActiveTab(key as string)}
          className="w-full"
        >
          <Tab key="dashboard" title={
            <div className="flex items-center gap-2">
              <Trophy className="w-4 h-4" />
              <span>Dashboard</span>
            </div>
          }>
            <GamificationDashboard />
          </Tab>

          <Tab key="badges" title={
            <div className="flex items-center gap-2">
              <Award className="w-4 h-4" />
              <span>Badge</span>
            </div>
          }>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <h3 className="text-lg font-semibold">Manajemen Badge</h3>
                <Button
                  color="primary"
                  startContent={<Plus className="w-4 h-4" />}
                  onPress={onBadgeModalOpen}
                >
                  Buat Badge
                </Button>
              </CardHeader>
              <CardBody>
                {isLoading ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  </div>
                ) : badges.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <p>Belum ada badge yang dibuat</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {badges.map((badge: any) => (
                    <Card key={badge.id} className="border">
                      <CardBody className="p-4">
                        <div className="flex items-center gap-3 mb-3">
                          <div className="text-2xl">{badge.icon}</div>
                          <div>
                            <h4 className="font-semibold">{badge.name}</h4>
                            <p className="text-sm text-gray-600">{badge.description}</p>
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <Chip color="warning" variant="flat" size="sm">
                            {badge.xpReward} XP
                          </Chip>
                          <div className="flex gap-1">
                            <Button isIconOnly size="sm" variant="light">
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button isIconOnly size="sm" variant="light" color="danger">
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </CardBody>
                    </Card>
                  ))}
                  </div>
                )}
              </CardBody>
            </Card>
          </Tab>

          <Tab key="levels" title={
            <div className="flex items-center gap-2">
              <Shield className="w-4 h-4" />
              <span>Level</span>
            </div>
          }>
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold">Sistem Level</h3>
              </CardHeader>
              <CardBody>
                <div className="space-y-4">
                  {mockLevels.map((level) => (
                    <Card key={level.level} className="border">
                      <CardBody className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <div className="flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-br from-blue-100 to-purple-100">
                              <span className="text-lg font-bold text-blue-600">
                                {level.level}
                              </span>
                            </div>
                            <div>
                              <h4 className="font-semibold text-lg">{level.name}</h4>
                              <p className="text-sm text-gray-600">{level.benefits}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <Chip
                              color={getLevelColor(level.level)}
                              variant="flat"
                              startContent={<Zap className="w-3 h-3" />}
                            >
                              {level.xpRequired} XP
                            </Chip>
                            <div className="mt-2">
                              <Button size="sm" variant="flat">
                                Edit Level
                              </Button>
                            </div>
                          </div>
                        </div>
                      </CardBody>
                    </Card>
                  ))}
                </div>
              </CardBody>
            </Card>
          </Tab>

          <Tab key="challenges" title={
            <div className="flex items-center gap-2">
              <Target className="w-4 h-4" />
              <span>Challenge</span>
            </div>
          }>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <h3 className="text-lg font-semibold">Manajemen Challenge</h3>
                <Button
                  color="primary"
                  startContent={<Plus className="w-4 h-4" />}
                  onPress={onChallengeModalOpen}
                >
                  Buat Challenge
                </Button>
              </CardHeader>
              <CardBody>
                {isLoading ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  </div>
                ) : challenges.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <p>Belum ada challenge yang dibuat</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {challenges.map((challenge: any) => (
                    <Card key={challenge.id} className="border">
                      <CardBody className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h4 className="font-semibold">{challenge.title}</h4>
                              <Chip
                                color={challenge.isActive ? 'success' : 'default'}
                                size="sm"
                              >
                                {challenge.isActive ? 'Aktif' : 'Nonaktif'}
                              </Chip>
                            </div>
                            <p className="text-sm text-gray-600 mb-3">{challenge.description}</p>
                            <div className="flex items-center gap-4 text-sm">
                              <div className="flex items-center gap-1">
                                <Calendar className="w-4 h-4 text-blue-500" />
                                <span>{challenge.duration} hari</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Users className="w-4 h-4 text-green-500" />
                                <span>{challenge.targetType}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Zap className="w-4 h-4 text-yellow-500" />
                                <span>{challenge.xpReward} XP</span>
                              </div>
                            </div>
                          </div>
                          <div className="flex gap-1">
                            <Button isIconOnly size="sm" variant="light">
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button isIconOnly size="sm" variant="light" color="danger">
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </CardBody>
                    </Card>
                  ))}
                  </div>
                )}
              </CardBody>
            </Card>
          </Tab>

          <Tab key="students" title={
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              <span>Siswa</span>
            </div>
          }>
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between w-full">
                  <h3 className="text-lg font-semibold">Daftar Siswa & Reward</h3>
                  <div className="flex gap-4">
                    <Input
                      placeholder="Cari siswa..."
                      aria-label="Cari siswa berdasarkan nama"
                      startContent={<Search className="w-4 h-4 text-gray-400" />}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-64"
                    />
                    <Select
                      placeholder="Urutkan"
                      className="w-40"
                      selectedKeys={[sortBy]}
                      onSelectionChange={(keys) => setSortBy(Array.from(keys)[0] as string)}
                    >
                      <SelectItem key="xp" textValue="Urutkan: XP">Urutkan: XP</SelectItem>
                      <SelectItem key="name" textValue="Urutkan: Nama">Urutkan: Nama</SelectItem>
                    </Select>
                  </div>
                </div>
              </CardHeader>
              <CardBody>
                <Table aria-label="Students table">
                  <TableHeader>
                    <TableColumn>SISWA</TableColumn>
                    <TableColumn>KELAS</TableColumn>
                    <TableColumn>XP</TableColumn>
                    <TableColumn>LEVEL</TableColumn>
                    <TableColumn>AKSI</TableColumn>
                  </TableHeader>
                  <TableBody emptyContent={
                    isLoading ? (
                      <div className="flex justify-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        <p>Belum ada data siswa</p>
                      </div>
                    )
                  }>
                    {filteredStudents.map((student: any) => (
                      <TableRow key={student.id}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <Avatar
                              name={getInitials(student.fullName || 'S')}
                              size="sm"
                              color={getLevelColor(student.studentXp?.level || 1)}
                            />
                            <span className="font-medium">{student.fullName}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Chip color="primary" variant="flat" size="sm">
                            {student.class?.name || 'Belum ada kelas'}
                          </Chip>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Zap className="w-4 h-4 text-yellow-500" />
                            <span className="font-medium">{student.studentXp?.totalXp || 0}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Chip
                            color={getLevelColor(student.studentXp?.level || 1)}
                            variant="flat"
                            size="sm"
                          >
                            Lv.{student.studentXp?.level || 1} {student.studentXp?.levelName || 'Pemula'}
                          </Chip>
                        </TableCell>
                        <TableCell>
                          <Button
                            size="sm"
                            color="success"
                            variant="flat"
                            startContent={<Gift className="w-4 h-4" />}
                            onPress={() => openRewardModal(student)}
                          >
                            Reward
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardBody>
            </Card>
          </Tab>
        </Tabs>
      </motion.div>

      {/* Badge Modal */}
      <Modal isOpen={isBadgeModalOpen} onClose={onBadgeModalClose} size="2xl">
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader>
                <div className="flex items-center gap-2">
                  <Award className="w-5 h-5" />
                  <span>Buat Badge Baru</span>
                </div>
              </ModalHeader>
              <ModalBody>
                <div className="space-y-4">
                  <Input
                    label="Nama Badge"
                    placeholder="Masukkan nama badge"
                    value={badgeForm.name}
                    onChange={(e) => setBadgeForm({...badgeForm, name: e.target.value})}
                  />
                  <Textarea
                    label="Deskripsi"
                    placeholder="Masukkan deskripsi badge"
                    value={badgeForm.description}
                    onChange={(e) => setBadgeForm({...badgeForm, description: e.target.value})}
                  />
                  <Input
                    label="XP Reward"
                    type="number"
                    placeholder="50"
                    value={badgeForm.xpReward.toString()}
                    onChange={(e) => setBadgeForm({...badgeForm, xpReward: parseInt(e.target.value) || 50})}
                    endContent={<span className="text-sm text-gray-500">XP</span>}
                  />
                  <div>
                    <label className="block text-sm font-medium mb-2">Pilih Icon</label>
                    <div className="grid grid-cols-10 gap-2">
                      {iconOptions.map((icon) => (
                        <button
                          key={icon}
                          type="button"
                          className={`p-2 text-xl border rounded-lg hover:bg-gray-50 ${
                            badgeForm.icon === icon ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                          }`}
                          onClick={() => setBadgeForm({...badgeForm, icon})}
                        >
                          {icon}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              </ModalBody>
              <ModalFooter>
                <Button variant="light" onPress={onClose}>
                  Batal
                </Button>
                <Button color="primary" onPress={handleCreateBadge}>
                  Buat Badge
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>

      {/* Challenge Modal */}
      <Modal isOpen={isChallengeModalOpen} onClose={onChallengeModalClose} size="2xl">
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader>
                <div className="flex items-center gap-2">
                  <Target className="w-5 h-5" />
                  <span>Buat Challenge Baru</span>
                </div>
              </ModalHeader>
              <ModalBody>
                <div className="space-y-4">
                  <Input
                    label="Judul Challenge"
                    placeholder="Masukkan judul challenge"
                    value={challengeForm.title}
                    onChange={(e) => setChallengeForm({...challengeForm, title: e.target.value})}
                  />
                  <Textarea
                    label="Deskripsi"
                    placeholder="Masukkan deskripsi challenge"
                    value={challengeForm.description}
                    onChange={(e) => setChallengeForm({...challengeForm, description: e.target.value})}
                  />
                  <Input
                    label="Durasi (hari)"
                    type="number"
                    placeholder="7"
                    value={challengeForm.duration.toString()}
                    onChange={(e) => setChallengeForm({...challengeForm, duration: parseInt(e.target.value) || 7})}
                    endContent={<span className="text-sm text-gray-500">hari</span>}
                  />
                  <Select
                    label="Target Siswa"
                    placeholder="Pilih target siswa"
                    selectedKeys={[challengeForm.targetType]}
                    onSelectionChange={(keys) => setChallengeForm({...challengeForm, targetType: Array.from(keys)[0] as string})}
                  >
                    <SelectItem key="ALL_STUDENTS" textValue="Semua Siswa">Semua Siswa</SelectItem>
                    <SelectItem key="GRADE_7" textValue="Kelas 7">Kelas 7</SelectItem>
                    <SelectItem key="GRADE_8" textValue="Kelas 8">Kelas 8</SelectItem>
                    <SelectItem key="GRADE_9" textValue="Kelas 9">Kelas 9</SelectItem>
                  </Select>
                  <Input
                    label="XP Reward"
                    type="number"
                    placeholder="100"
                    value={challengeForm.xpReward.toString()}
                    onChange={(e) => setChallengeForm({...challengeForm, xpReward: parseInt(e.target.value) || 100})}
                    endContent={<span className="text-sm text-gray-500">XP</span>}
                  />
                </div>
              </ModalBody>
              <ModalFooter>
                <Button variant="light" onPress={onClose}>
                  Batal
                </Button>
                <Button color="primary" onPress={handleCreateChallenge}>
                  Buat Challenge
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>

      {/* Reward Modal */}
      <Modal isOpen={isRewardModalOpen} onClose={onRewardModalClose} size="lg">
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader>
                <div className="flex items-center gap-2">
                  <Gift className="w-5 h-5" />
                  <span>Beri Reward</span>
                </div>
              </ModalHeader>
              <ModalBody>
                {selectedStudent && (
                  <div className="space-y-4">
                    <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                      <Avatar
                        name={getInitials(selectedStudent.name)}
                        color={getLevelColor(selectedStudent.level)}
                      />
                      <div>
                        <h4 className="font-semibold">{selectedStudent.name}</h4>
                        <p className="text-sm text-gray-600">{selectedStudent.class}</p>
                      </div>
                    </div>

                    <Select
                      label="Jenis Reward"
                      placeholder="Pilih jenis reward"
                      selectedKeys={[rewardForm.type]}
                      onSelectionChange={(keys) => setRewardForm({...rewardForm, type: Array.from(keys)[0] as string})}
                    >
                      <SelectItem key="xp" textValue="Poin XP">Poin XP</SelectItem>
                      <SelectItem key="badge" textValue="Badge">Badge</SelectItem>
                    </Select>

                    {rewardForm.type === 'xp' && (
                      <Input
                        label="Jumlah XP"
                        type="number"
                        placeholder="50"
                        value={rewardForm.xpAmount.toString()}
                        onChange={(e) => setRewardForm({...rewardForm, xpAmount: parseInt(e.target.value) || 50})}
                        endContent={<span className="text-sm text-gray-500">XP</span>}
                      />
                    )}

                    {rewardForm.type === 'badge' && (
                      <Select
                        label="Pilih Badge"
                        placeholder="Pilih badge yang akan diberikan"
                        selectedKeys={rewardForm.badgeId ? [rewardForm.badgeId] : []}
                        onSelectionChange={(keys) => setRewardForm({...rewardForm, badgeId: Array.from(keys)[0] as string})}
                      >
                        {badges.map((badge: any) => (
                          <SelectItem key={badge.id} textValue={badge.name}>
                            <div className="flex items-center gap-2">
                              <span>{badge.icon}</span>
                              <span>{badge.name}</span>
                              <Chip size="sm" color="warning" variant="flat">
                                {badge.xpReward} XP
                              </Chip>
                            </div>
                          </SelectItem>
                        ))}
                      </Select>
                    )}

                    <Textarea
                      label="Keterangan (Opsional)"
                      placeholder="Alasan pemberian reward..."
                      value={rewardForm.description}
                      onChange={(e) => setRewardForm({...rewardForm, description: e.target.value})}
                    />
                  </div>
                )}
              </ModalBody>
              <ModalFooter>
                <Button variant="light" onPress={onClose}>
                  Batal
                </Button>
                <Button color="success" onPress={handleGiveReward}>
                  Berikan Reward
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </div>
  );
};

export default GamificationTabs;
