import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Chip, Avatar } from '@heroui/react';
import { useAuthStore } from '../stores/authStore';

const DashboardContent = () => {
  const { user } = useAuthStore();
  const [stats, setStats] = useState({
    totalClasses: 0,
    totalStudents: 0,
    activeAssignments: 0,
    averageGrade: 0
  });
  const [recentClasses, setRecentClasses] = useState([]);
  const [activities, setActivities] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  const getUserName = () => {
    if (user?.firstName && user?.lastName) {
      return `${user.firstName} ${user.lastName}`;
    }
    if (user?.name) {
      return user.name;
    }
    return 'User';
  };

  // Load dashboard data
  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setIsLoading(true);
    try {
      const token = localStorage.getItem('auth_token');
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      };

      // Load stats
      const statsResponse = await fetch('http://localhost:5000/api/dashboard/stats', { headers });
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData.data);
      }

      // Load recent classes
      const classesResponse = await fetch('http://localhost:5000/api/dashboard/recent-classes', { headers });
      if (classesResponse.ok) {
        const classesData = await classesResponse.json();
        setRecentClasses(classesData.data);
      }

      // Load activities
      const activitiesResponse = await fetch('http://localhost:5000/api/dashboard/recent-activities', { headers });
      if (activitiesResponse.ok) {
        const activitiesData = await activitiesResponse.json();
        setActivities(activitiesData.data);
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const statsCards = [
    {
      title: 'Total Kelas',
      value: stats.totalClasses.toString(),
      subtitle: 'Kelas aktif',
      change: 'Data real-time',
      icon: '📚',
      color: 'primary'
    },
    {
      title: 'Total Siswa',
      value: stats.totalStudents.toString(),
      subtitle: 'Siswa terdaftar',
      change: 'Data real-time',
      icon: '👥',
      color: 'success'
    },
    {
      title: 'Tugas Aktif',
      value: stats.activeAssignments.toString(),
      subtitle: 'Tugas berlangsung',
      change: 'Data real-time',
      icon: '📝',
      color: 'warning'
    },
    {
      title: 'Rata-rata Nilai',
      value: stats.averageGrade.toString(),
      subtitle: 'Nilai keseluruhan',
      change: 'Data real-time',
      icon: '📊',
      color: 'secondary'
    }
  ];

  return (
    <div className="p-8 space-y-8">
      {/* Welcome Banner */}
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <Card className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800">
          <CardBody className="p-8 text-white relative overflow-hidden">
            <div className="relative z-10">
              <h1 className="text-3xl font-bold mb-2">Selamat Datang, {getUserName()}! 👋</h1>
              <p className="text-blue-100 text-lg">
                {user?.role === 'ADMIN'
                  ? 'Kelola sistem dengan bijak dan efisien'
                  : user?.role === 'GURU'
                  ? 'Hari ini adalah kesempatan baru untuk menginspirasi siswa Anda'
                  : 'Semangat belajar dan raih prestasi terbaikmu!'
                }
              </p>
            </div>
            <div className="absolute top-4 right-8">
              <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                <span className="text-3xl">📊</span>
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsCards.map((stat, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            whileHover={{ scale: 1.02 }}
          >
            <Card className="hover:shadow-lg transition-shadow">
              <CardBody className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-3xl">{stat.icon}</span>
                  <Chip color={stat.color as any} variant="flat">
                    {stat.icon}
                  </Chip>
                </div>
                <h3 className="text-3xl font-bold mb-1">{stat.value}</h3>
                <p className="text-gray-600 mb-2">{stat.subtitle}</p>
                <p className="text-gray-500 text-sm">{stat.change}</p>
              </CardBody>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Recent Classes */}
        <motion.div 
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="lg:col-span-2"
        >
          <Card>
            <CardHeader className="flex justify-between items-center">
              <h3 className="text-xl font-bold">Kelas Terbaru</h3>
              <Button color="primary" size="sm">
                + Tambah Kelas
              </Button>
            </CardHeader>
            <CardBody>
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              ) : recentClasses.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p>Belum ada kelas terbaru</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {recentClasses.map((kelas: any, index) => (
                    <motion.div
                      key={index}
                      whileHover={{ scale: 1.01 }}
                      className="flex items-center p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors"
                    >
                      <Avatar
                        name={kelas.name?.charAt(0) || 'K'}
                        className="bg-blue-600 text-white font-bold mr-4"
                      />
                      <div className="flex-1">
                        <h4 className="font-semibold text-gray-900">
                          {kelas.name} {kelas.subject?.name ? `- ${kelas.subject.name}` : ''}
                        </h4>
                        <p className="text-gray-600 text-sm">
                          {kelas._count?.students || 0} siswa
                        </p>
                      </div>
                      <div className="flex space-x-2 ml-4">
                        <Button isIconOnly size="sm" variant="light">👁️</Button>
                        <Button isIconOnly size="sm" variant="light">✏️</Button>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </CardBody>
          </Card>
        </motion.div>

        {/* Activities */}
        <motion.div 
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
        >
          <Card>
            <CardHeader>
              <div>
                <h3 className="text-xl font-bold">⚡ Aktivitas Terbaru</h3>
                <p className="text-sm text-gray-500">Update terbaru dari kelas Anda</p>
              </div>
            </CardHeader>
            <CardBody>
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              ) : activities.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p>Belum ada aktivitas terbaru</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {activities.map((activity: any, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors"
                    >
                      <Avatar
                        name={activity.type?.charAt(0).toUpperCase() || 'A'}
                        size="sm"
                        className="bg-blue-100 text-blue-600"
                      />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">{activity.description}</p>
                        <p className="text-xs text-gray-600">{activity.user?.firstName} {activity.user?.lastName}</p>
                        <p className="text-xs text-gray-500">
                          {new Date(activity.createdAt).toLocaleDateString('id-ID')}
                        </p>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}

              <Button
                variant="light"
                color="primary"
                className="w-full mt-4"
                size="sm"
              >
                Lihat Semua Aktivitas
              </Button>
            </CardBody>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default DashboardContent;
