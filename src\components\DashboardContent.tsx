import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Chip, Avatar } from '@heroui/react';

const DashboardContent = () => {
  const stats = [
    { 
      title: 'Total Kelas', 
      value: '3', 
      subtitle: 'Kelas aktif',
      change: '+2 dari bulan lalu',
      icon: '📚',
      color: 'primary'
    },
    { 
      title: 'Total Siswa', 
      value: '89', 
      subtitle: 'Siswa terdaftar',
      change: '+8 dari bulan lalu',
      icon: '👥',
      color: 'success'
    },
    { 
      title: 'Tugas Aktif', 
      value: '12', 
      subtitle: 'Tugas berlangsung',
      change: '+3 minggu ini',
      icon: '📝',
      color: 'warning'
    },
    { 
      title: 'Rata-rata <PERSON>', 
      value: '8.5', 
      subtitle: '<PERSON><PERSON> keseluruhan',
      change: '+0.3 dari bulan lalu',
      icon: '📊',
      color: 'secondary'
    }
  ];

  const recentClasses = [
    { id: '9B', subject: 'Bahasa Indonesia (IPS)', time: '07:00-08:30' },
    { id: '9A', subject: 'Administrasi (IPA)', time: '08:30-10:00' },
    { id: '9C', subject: 'IPA', time: '10:15-11:45', status: 'Berlangsung' }
  ];

  const activities = [
    { 
      type: 'assignment',
      title: 'Tugas Matematika dikumpulkan',
      user: 'Ahmad Pratama',
      time: '2 jam lalu',
      icon: '📝'
    },
    { 
      type: 'quiz',
      title: 'Nilai Quiz Fisika tersedia',
      user: 'Siti Nurhaliza',
      time: '4 jam lalu',
      icon: '🎯'
    },
    { 
      type: 'attendance',
      title: 'Presensi kelas 9A selesai',
      user: 'System',
      time: '1 hari lalu',
      icon: '✅'
    },
    { 
      type: 'badge',
      title: 'Badge "Expert Learner" diraih',
      user: 'Budi Santoso',
      time: '2 hari lalu',
      icon: '🏆'
    }
  ];

  return (
    <div className="p-8 space-y-8">
      {/* Welcome Banner */}
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <Card className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800">
          <CardBody className="p-8 text-white relative overflow-hidden">
            <div className="relative z-10">
              <h1 className="text-3xl font-bold mb-2">Selamat Datang, Devi Saidulloh! 👋</h1>
              <p className="text-blue-100 text-lg">Hari ini adalah kesempatan baru untuk menginspirasi siswa Anda</p>
            </div>
            <div className="absolute top-4 right-8">
              <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                <span className="text-3xl">📊</span>
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            whileHover={{ scale: 1.02 }}
          >
            <Card className="hover:shadow-lg transition-shadow">
              <CardBody className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-3xl">{stat.icon}</span>
                  <Chip color={stat.color as any} variant="flat">
                    {stat.icon}
                  </Chip>
                </div>
                <h3 className="text-3xl font-bold mb-1">{stat.value}</h3>
                <p className="text-gray-600 mb-2">{stat.subtitle}</p>
                <p className="text-gray-500 text-sm">{stat.change}</p>
              </CardBody>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Recent Classes */}
        <motion.div 
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="lg:col-span-2"
        >
          <Card>
            <CardHeader className="flex justify-between items-center">
              <h3 className="text-xl font-bold">Kelas Terbaru</h3>
              <Button color="primary" size="sm">
                + Tambah Kelas
              </Button>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                {recentClasses.map((kelas, index) => (
                  <motion.div
                    key={index}
                    whileHover={{ scale: 1.01 }}
                    className="flex items-center p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors"
                  >
                    <Avatar 
                      name={kelas.id}
                      className="bg-blue-600 text-white font-bold mr-4"
                    />
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900">{kelas.subject}</h4>
                      <p className="text-gray-600 text-sm">{kelas.time}</p>
                    </div>
                    {kelas.status && (
                      <Chip color="success" variant="flat">
                        {kelas.status}
                      </Chip>
                    )}
                    <div className="flex space-x-2 ml-4">
                      <Button isIconOnly size="sm" variant="light">👁️</Button>
                      <Button isIconOnly size="sm" variant="light">✏️</Button>
                      <Button isIconOnly size="sm" variant="light">🗑️</Button>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardBody>
          </Card>
        </motion.div>

        {/* Activities */}
        <motion.div 
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
        >
          <Card>
            <CardHeader>
              <div>
                <h3 className="text-xl font-bold">⚡ Aktivitas Terbaru</h3>
                <p className="text-sm text-gray-500">Update terbaru dari kelas Anda</p>
              </div>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                {activities.map((activity, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors"
                  >
                    <Avatar 
                      name={activity.icon}
                      size="sm"
                      className="bg-blue-100 text-blue-600"
                    />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">{activity.title}</p>
                      <p className="text-xs text-gray-600">{activity.user}</p>
                      <p className="text-xs text-gray-500">{activity.time}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
              
              <Button 
                variant="light" 
                color="primary" 
                className="w-full mt-4"
                size="sm"
              >
                Lihat Semua Aktivitas
              </Button>
            </CardBody>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default DashboardContent;
