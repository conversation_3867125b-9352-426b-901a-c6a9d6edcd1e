// Modern Assignment Management Component - Guru Digital Pelangi
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Input,
  Select,
  SelectItem,
  Chip,
  Avatar,
  Tooltip
} from '@heroui/react';
import {
  Plus,
  Search,
  RefreshCw,
  FileText,
  CheckCircle,
  Clock,
  AlertCircle,
  Calendar,
  Award,
  Eye,
  Edit,
  Copy,
  Trash2,
  Users
} from 'lucide-react';
import EmptyState from '../../common/EmptyState';

interface Assignment {
  id: string;
  title: string;
  description: string;
  className: string;
  deadline: string;
  points: number;
  status: 'active' | 'overdue' | 'completed';
  submissionsCount: number;
  totalStudents: number;
}

const AssignmentManager: React.FC = () => {
  const [assignments, setAssignments] = useState<Assignment[]>([
    {
      id: '1',
      title: 'UH 2',
      description: 'bab bumi',
      className: 'Unknown Class',
      deadline: '2025-06-07',
      points: 100,
      status: 'overdue',
      submissionsCount: 15,
      totalStudents: 30
    },
    {
      id: '2',
      title: 'asal',
      description: 'semua bab',
      className: 'Unknown Class',
      deadline: '2025-06-09',
      points: 100,
      status: 'overdue',
      submissionsCount: 8,
      totalStudents: 30
    }
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClass, setSelectedClass] = useState('');
  const [sortBy, setSortBy] = useState('deadline');

  const handleCreate = () => {
    console.log('Create assignment');
  };

  const handleRefresh = () => {
    console.log('Refresh assignments');
  };

  // Filter assignments
  const filteredAssignments = assignments.filter(assignment =>
    assignment.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    assignment.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Calculate statistics
  const stats = {
    total: assignments.length,
    active: assignments.filter(a => a.status === 'active').length,
    overdue: assignments.filter(a => a.status === 'overdue').length,
    completed: assignments.filter(a => a.status === 'completed').length,
    thisWeek: assignments.filter(a => {
      const deadline = new Date(a.deadline);
      const now = new Date();
      const weekFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
      return deadline >= now && deadline <= weekFromNow;
    }).length,
    averagePoints: Math.round(assignments.reduce((sum, a) => sum + a.points, 0) / assignments.length) || 0
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'overdue': return 'danger';
      case 'completed': return 'primary';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'Aktif';
      case 'overdue': return 'Lewat Deadline';
      case 'completed': return 'Selesai';
      default: return 'Unknown';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  return (
    <div className="p-6 space-y-6 max-w-7xl mx-auto">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Tugas</h1>
              <p className="text-gray-600 mt-1">Kelola semua tugas dan aktivitas pembelajaran siswa</p>
            </div>
            <div className="flex gap-3">
              <Button
                variant="flat"
                startContent={<RefreshCw className="w-4 h-4" />}
                onPress={handleRefresh}
              >
                Refresh
              </Button>
              <Button
                color="primary"
                startContent={<Plus className="w-4 h-4" />}
                onPress={handleCreate}
              >
                Buat Tugas Baru
              </Button>
            </div>
          </CardHeader>
        </Card>
      </motion.div>

      {/* Search and Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <Card>
          <CardBody>
            <div className="flex flex-col md:flex-row gap-4">
              <Input
                placeholder="Cari tugas berdasarkan judul atau deskripsi..."
                startContent={<Search className="w-4 h-4 text-gray-400" />}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="flex-1"
              />
              <Select
                placeholder="Semua Kelas"
                className="md:w-48"
                selectedKeys={selectedClass ? [selectedClass] : []}
                onSelectionChange={(keys) => setSelectedClass(Array.from(keys)[0] as string)}
              >
                <SelectItem key="all">Semua Kelas</SelectItem>
                <SelectItem key="7a">Kelas 7A</SelectItem>
                <SelectItem key="7b">Kelas 7B</SelectItem>
              </Select>
              <Select
                placeholder="Urutkan: Deadline"
                className="md:w-48"
                selectedKeys={[sortBy]}
                onSelectionChange={(keys) => setSortBy(Array.from(keys)[0] as string)}
              >
                <SelectItem key="deadline">Urutkan: Deadline</SelectItem>
                <SelectItem key="title">Urutkan: Judul</SelectItem>
                <SelectItem key="points">Urutkan: Poin</SelectItem>
              </Select>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Statistics Cards */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <Card className="bg-blue-50 border-blue-200">
            <CardBody className="p-4 text-center">
              <FileText className="w-6 h-6 text-blue-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-blue-900">{stats.total}</p>
              <p className="text-sm text-blue-700">Total Tugas</p>
            </CardBody>
          </Card>

          <Card className="bg-green-50 border-green-200">
            <CardBody className="p-4 text-center">
              <CheckCircle className="w-6 h-6 text-green-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-green-900">{stats.active}</p>
              <p className="text-sm text-green-700">Tugas Aktif</p>
            </CardBody>
          </Card>

          <Card className="bg-red-50 border-red-200">
            <CardBody className="p-4 text-center">
              <AlertCircle className="w-6 h-6 text-red-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-red-900">{stats.overdue}</p>
              <p className="text-sm text-red-700">Lewat Deadline</p>
            </CardBody>
          </Card>

          <Card className="bg-purple-50 border-purple-200">
            <CardBody className="p-4 text-center">
              <CheckCircle className="w-6 h-6 text-purple-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-purple-900">{stats.completed}</p>
              <p className="text-sm text-purple-700">Selesai</p>
            </CardBody>
          </Card>

          <Card className="bg-orange-50 border-orange-200">
            <CardBody className="p-4 text-center">
              <Calendar className="w-6 h-6 text-orange-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-orange-900">{stats.thisWeek}</p>
              <p className="text-sm text-orange-700">Minggu Ini</p>
            </CardBody>
          </Card>

          <Card className="bg-indigo-50 border-indigo-200">
            <CardBody className="p-4 text-center">
              <Award className="w-6 h-6 text-indigo-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-indigo-900">{stats.averagePoints}</p>
              <p className="text-sm text-indigo-700">Rata-rata Poin</p>
            </CardBody>
          </Card>
        </div>
      </motion.div>

      {/* Assignment List Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div className="flex items-center gap-2">
              <Users className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-semibold">Daftar Tugas</h3>
            </div>
            <p className="text-sm text-gray-600">
              Menampilkan {filteredAssignments.length} dari {assignments.length} tugas
            </p>
          </CardHeader>
        </Card>
      </motion.div>

      {/* Assignment Cards */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <Card>
          <CardBody className="p-6">
            {filteredAssignments.length === 0 ? (
              <EmptyState
                icon={FileText}
                title="Belum ada tugas"
                description="Mulai dengan membuat tugas pertama untuk memberikan penilaian dan latihan kepada siswa"
                actionLabel="Buat Tugas Pertama"
                onAction={handleCreate}
                actionColor="primary"
              />
            ) : (
              <div className="space-y-4">
                {filteredAssignments.map((assignment) => (
                  <Card key={assignment.id} className="border border-gray-200 hover:shadow-md transition-shadow">
                    <CardBody className="p-4">
                      <div className="flex items-start gap-4">
                        {/* Avatar with Initial */}
                        <Avatar
                          name={assignment.title.charAt(0).toUpperCase()}
                          className="bg-blue-500 text-white font-semibold"
                          size="lg"
                        />

                        {/* Content */}
                        <div className="flex-1">
                          <div className="flex items-start justify-between">
                            <div>
                              <h4 className="text-lg font-semibold text-gray-900 mb-1">
                                {assignment.title}
                              </h4>
                              <p className="text-gray-600 mb-3">{assignment.description}</p>

                              <div className="flex items-center gap-4 text-sm text-gray-500">
                                <span className="flex items-center gap-1">
                                  <Users className="w-4 h-4" />
                                  {assignment.className}
                                </span>
                                <span className="flex items-center gap-1">
                                  <Calendar className="w-4 h-4" />
                                  {formatDate(assignment.deadline)}
                                </span>
                                <span className="flex items-center gap-1">
                                  <Award className="w-4 h-4" />
                                  {assignment.points} poin
                                </span>
                              </div>
                            </div>

                            {/* Status and Actions */}
                            <div className="flex items-center gap-3">
                              <Chip
                                color={getStatusColor(assignment.status)}
                                variant="flat"
                                size="sm"
                              >
                                {getStatusText(assignment.status)}
                              </Chip>

                              <div className="flex gap-1">
                                <Tooltip content="Lihat Detail">
                                  <Button isIconOnly size="sm" variant="light">
                                    <Eye className="w-4 h-4" />
                                  </Button>
                                </Tooltip>
                                <Tooltip content="Edit">
                                  <Button isIconOnly size="sm" variant="light">
                                    <Edit className="w-4 h-4" />
                                  </Button>
                                </Tooltip>
                                <Tooltip content="Copy">
                                  <Button isIconOnly size="sm" variant="light">
                                    <Copy className="w-4 h-4" />
                                  </Button>
                                </Tooltip>
                                <Tooltip content="Hapus">
                                  <Button isIconOnly size="sm" variant="light" color="danger">
                                    <Trash2 className="w-4 h-4" />
                                  </Button>
                                </Tooltip>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                ))}
              </div>
            )}
          </CardBody>
        </Card>
      </motion.div>
    </div>
  );
};

export default AssignmentManager;