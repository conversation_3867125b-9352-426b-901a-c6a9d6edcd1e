// Assignment Management Component with Empty State
import React, { useState } from 'react';
import { 
  Card, 
  CardBody, 
  CardHeader, 
  Button, 
  Input, 
  Chip
} from '@heroui/react';
import { Plus, Search, FileText, BookOpen, PenTool } from 'lucide-react';
import EmptyState from '../../common/EmptyState';

interface Assignment {
  id: string;
  title: string;
  subject: string;
  dueDate: string;
  status: 'draft' | 'published' | 'closed';
}

const AssignmentManager: React.FC = () => {
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [searchTerm, setSearchTerm] = useState('');

  const handleCreate = () => {
    // TODO: Implement create assignment
    console.log('Create assignment');
  };

  const filteredAssignments = assignments.filter(assignment =>
    assignment.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    assignment.subject.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <FileText className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold">Manajemen Tugas</h1>
              <p className="text-gray-600">Kelola tugas dan penilaian siswa</p>
            </div>
          </div>
          <Button
            color="primary"
            startContent={<Plus className="w-4 h-4" />}
            onPress={handleCreate}
          >
            Buat Tugas
          </Button>
        </CardHeader>
      </Card>

      {/* Search */}
      <Card>
        <CardBody>
          <div className="flex gap-4">
            <Input
              placeholder="Cari tugas..."
              startContent={<Search className="w-4 h-4 text-gray-400" />}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1"
            />
            <Chip color="primary" variant="flat">
              {filteredAssignments.length} Tugas
            </Chip>
          </div>
        </CardBody>
      </Card>

      {/* Assignments List */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Daftar Tugas</h3>
        </CardHeader>
        <CardBody>
          {filteredAssignments.length === 0 ? (
            <EmptyState
              icon={PenTool}
              title="Belum ada tugas"
              description="Mulai dengan membuat tugas pertama untuk memberikan penilaian dan latihan kepada siswa"
              actionLabel="Buat Tugas Pertama"
              onAction={handleCreate}
              actionColor="primary"
            />
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredAssignments.map((assignment) => (
                <Card key={assignment.id} className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <FileText className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold">{assignment.title}</h4>
                      <p className="text-sm text-gray-600">{assignment.subject}</p>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  );
};

export default AssignmentManager;
