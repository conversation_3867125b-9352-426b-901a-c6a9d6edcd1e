// Modern Assignment Management Component - Guru Digital Pelangi
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Input,
  Select,
  SelectItem,
  Chip,
  Avatar,
  Tooltip
} from '@heroui/react';
import {
  Plus,
  Search,
  RefreshCw,
  FileText,
  CheckCircle,
  Clock,
  AlertCircle,
  Calendar,
  Award,
  Eye,
  Edit,
  Copy,
  Trash2,
  Users
} from 'lucide-react';
import EmptyState from '../../common/EmptyState';
import AssignmentModal from './AssignmentModal';
import { assignmentService, classService } from '../../../services/expressApi';

interface Assignment {
  id: string;
  title: string;
  description: string;
  className: string;
  deadline: string;
  points: number;
  status: 'active' | 'overdue' | 'completed';
  submissionsCount: number;
  totalStudents: number;
  class?: {
    id: string;
    name: string;
    subject?: {
      name: string;
    };
  };
}

interface Class {
  id: string;
  name: string;
  subject?: {
    name: string;
  };
}

interface AssignmentStats {
  total: number;
  active: number;
  overdue: number;
  completed: number;
  thisWeek: number;
  averagePoints: number;
}

const AssignmentManager: React.FC = () => {
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [classes, setClasses] = useState<Class[]>([]);
  const [stats, setStats] = useState<AssignmentStats>({
    total: 0,
    active: 0,
    overdue: 0,
    completed: 0,
    thisWeek: 0,
    averagePoints: 0
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClass, setSelectedClass] = useState('');
  const [sortBy, setSortBy] = useState('deadline');
  const [isLoading, setIsLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingAssignment, setEditingAssignment] = useState<any>(null);

  // Load data on component mount
  useEffect(() => {
    loadClasses();
    loadAssignments();
    loadStats();
  }, []);

  // Reload assignments when filters change
  useEffect(() => {
    loadAssignments();
  }, [selectedClass, searchTerm]);

  const loadClasses = async () => {
    const response = await classService.getClasses();
    if (response.success && response.data) {
      setClasses(response.data);
    }
  };

  const loadAssignments = async () => {
    setIsLoading(true);
    const params: any = {};
    if (selectedClass) params.classId = selectedClass;
    if (searchTerm) params.search = searchTerm;

    const response = await assignmentService.getAssignments(params);
    if (response.success && response.data) {
      setAssignments(response.data.map((assignment: any) => ({
        ...assignment,
        className: assignment.class?.name || 'Unknown Class'
      })));
    }
    setIsLoading(false);
  };

  const loadStats = async () => {
    const response = await assignmentService.getAssignmentStats();
    if (response.success && response.data) {
      setStats(response.data);
    }
  };

  const handleCreate = () => {
    setEditingAssignment(null);
    setIsModalOpen(true);
  };

  const handleEdit = (assignment: any) => {
    setEditingAssignment(assignment);
    setIsModalOpen(true);
  };

  const handleDelete = async (assignment: any) => {
    if (confirm(`Apakah Anda yakin ingin menghapus tugas "${assignment.title}"?`)) {
      const response = await assignmentService.deleteAssignment(assignment.id);
      if (response.success) {
        loadAssignments();
        loadStats();
      } else {
        alert(response.error || 'Gagal menghapus tugas');
      }
    }
  };

  const handleModalSuccess = () => {
    loadAssignments();
    loadStats();
    setIsModalOpen(false);
  };

  const handleRefresh = () => {
    loadAssignments();
    loadStats();
  };

  // Filter assignments (filtering is now done on server side, but keep for client-side sorting)
  const filteredAssignments = assignments;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'overdue': return 'danger';
      case 'completed': return 'primary';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'Aktif';
      case 'overdue': return 'Lewat Deadline';
      case 'completed': return 'Selesai';
      default: return 'Unknown';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  return (
    <div className="p-6 space-y-6 max-w-7xl mx-auto">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Tugas</h1>
              <p className="text-gray-600 mt-1">Kelola semua tugas dan aktivitas pembelajaran siswa</p>
            </div>
            <div className="flex gap-3">
              <Button
                variant="flat"
                startContent={<RefreshCw className="w-4 h-4" />}
                onPress={handleRefresh}
              >
                Refresh
              </Button>
              <Button
                color="primary"
                startContent={<Plus className="w-4 h-4" />}
                onPress={handleCreate}
              >
                Buat Tugas Baru
              </Button>
            </div>
          </CardHeader>
        </Card>
      </motion.div>

      {/* Search and Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <Card>
          <CardBody>
            <div className="flex flex-col md:flex-row gap-4">
              <Input
                placeholder="Cari tugas berdasarkan judul atau deskripsi..."
                aria-label="Cari tugas berdasarkan judul atau deskripsi"
                startContent={<Search className="w-4 h-4 text-gray-400" />}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="flex-1"
              />
              <Select
                placeholder="Semua Kelas"
                className="md:w-48"
                selectedKeys={selectedClass ? [selectedClass] : []}
                onSelectionChange={(keys) => setSelectedClass(Array.from(keys)[0] as string)}
              >
                <SelectItem key="">Semua Kelas</SelectItem>
                {classes.map((cls) => (
                  <SelectItem key={cls.id} value={cls.id}>
                    {cls.name} {cls.subject?.name ? `- ${cls.subject.name}` : ''}
                  </SelectItem>
                ))}
              </Select>
              <Select
                placeholder="Urutkan: Deadline"
                className="md:w-48"
                selectedKeys={[sortBy]}
                onSelectionChange={(keys) => setSortBy(Array.from(keys)[0] as string)}
              >
                <SelectItem key="deadline">Urutkan: Deadline</SelectItem>
                <SelectItem key="title">Urutkan: Judul</SelectItem>
                <SelectItem key="points">Urutkan: Poin</SelectItem>
              </Select>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Statistics Cards */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <Card className="bg-blue-50 border-blue-200">
            <CardBody className="p-4 text-center">
              <FileText className="w-6 h-6 text-blue-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-blue-900">{stats.total}</p>
              <p className="text-sm text-blue-700">Total Tugas</p>
            </CardBody>
          </Card>

          <Card className="bg-green-50 border-green-200">
            <CardBody className="p-4 text-center">
              <CheckCircle className="w-6 h-6 text-green-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-green-900">{stats.active}</p>
              <p className="text-sm text-green-700">Tugas Aktif</p>
            </CardBody>
          </Card>

          <Card className="bg-red-50 border-red-200">
            <CardBody className="p-4 text-center">
              <AlertCircle className="w-6 h-6 text-red-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-red-900">{stats.overdue}</p>
              <p className="text-sm text-red-700">Lewat Deadline</p>
            </CardBody>
          </Card>

          <Card className="bg-purple-50 border-purple-200">
            <CardBody className="p-4 text-center">
              <CheckCircle className="w-6 h-6 text-purple-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-purple-900">{stats.completed}</p>
              <p className="text-sm text-purple-700">Selesai</p>
            </CardBody>
          </Card>

          <Card className="bg-orange-50 border-orange-200">
            <CardBody className="p-4 text-center">
              <Calendar className="w-6 h-6 text-orange-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-orange-900">{stats.thisWeek}</p>
              <p className="text-sm text-orange-700">Minggu Ini</p>
            </CardBody>
          </Card>

          <Card className="bg-indigo-50 border-indigo-200">
            <CardBody className="p-4 text-center">
              <Award className="w-6 h-6 text-indigo-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-indigo-900">{stats.averagePoints}</p>
              <p className="text-sm text-indigo-700">Rata-rata Poin</p>
            </CardBody>
          </Card>
        </div>
      </motion.div>

      {/* Assignment List Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div className="flex items-center gap-2">
              <Users className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-semibold">Daftar Tugas</h3>
            </div>
            <p className="text-sm text-gray-600">
              Menampilkan {filteredAssignments.length} dari {assignments.length} tugas
            </p>
          </CardHeader>
        </Card>
      </motion.div>

      {/* Assignment Cards */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <Card>
          <CardBody className="p-6">
            {isLoading ? (
              <div className="flex justify-center items-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : filteredAssignments.length === 0 ? (
              <EmptyState
                icon={FileText}
                title="Belum ada tugas"
                description="Mulai dengan membuat tugas pertama untuk memberikan penilaian dan latihan kepada siswa"
                actionLabel="Buat Tugas Pertama"
                onAction={handleCreate}
                actionColor="primary"
              />
            ) : (
              <div className="space-y-4">
                {filteredAssignments.map((assignment) => (
                  <Card key={assignment.id} className="border border-gray-200 hover:shadow-md transition-shadow">
                    <CardBody className="p-4">
                      <div className="flex items-start gap-4">
                        {/* Avatar with Initial */}
                        <Avatar
                          name={assignment.title.charAt(0).toUpperCase()}
                          className="bg-blue-500 text-white font-semibold"
                          size="lg"
                        />

                        {/* Content */}
                        <div className="flex-1">
                          <div className="flex items-start justify-between">
                            <div>
                              <h4 className="text-lg font-semibold text-gray-900 mb-1">
                                {assignment.title}
                              </h4>
                              <p className="text-gray-600 mb-3">{assignment.description}</p>

                              <div className="flex items-center gap-4 text-sm text-gray-500">
                                <span className="flex items-center gap-1">
                                  <Users className="w-4 h-4" />
                                  {assignment.className}
                                </span>
                                <span className="flex items-center gap-1">
                                  <Calendar className="w-4 h-4" />
                                  {formatDate(assignment.deadline)}
                                </span>
                                <span className="flex items-center gap-1">
                                  <Award className="w-4 h-4" />
                                  {assignment.points} poin
                                </span>
                              </div>
                            </div>

                            {/* Status and Actions */}
                            <div className="flex items-center gap-3">
                              <Chip
                                color={getStatusColor(assignment.status)}
                                variant="flat"
                                size="sm"
                              >
                                {getStatusText(assignment.status)}
                              </Chip>

                              <div className="flex gap-1">
                                <Tooltip content="Lihat Detail">
                                  <Button isIconOnly size="sm" variant="light">
                                    <Eye className="w-4 h-4" />
                                  </Button>
                                </Tooltip>
                                <Tooltip content="Edit">
                                  <Button
                                    isIconOnly
                                    size="sm"
                                    variant="light"
                                    onPress={() => handleEdit(assignment)}
                                  >
                                    <Edit className="w-4 h-4" />
                                  </Button>
                                </Tooltip>
                                <Tooltip content="Copy">
                                  <Button isIconOnly size="sm" variant="light">
                                    <Copy className="w-4 h-4" />
                                  </Button>
                                </Tooltip>
                                <Tooltip content="Hapus">
                                  <Button
                                    isIconOnly
                                    size="sm"
                                    variant="light"
                                    color="danger"
                                    onPress={() => handleDelete(assignment)}
                                  >
                                    <Trash2 className="w-4 h-4" />
                                  </Button>
                                </Tooltip>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                ))}
              </div>
            )}
          </CardBody>
        </Card>
      </motion.div>

      {/* Assignment Modal */}
      <AssignmentModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSuccess={handleModalSuccess}
        assignment={editingAssignment}
      />
    </div>
  );
};

export default AssignmentManager;