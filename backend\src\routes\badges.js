// Badge Management Routes - Guru Digital Pelangi
import express from 'express';
import { PrismaClient } from '@prisma/client';
import { authenticateToken, requireRole } from '../middleware/auth.js';

const router = express.Router();
const prisma = new PrismaClient();

// Get all badges
router.get('/', authenticateToken, async (req, res) => {
  try {
    const badges = await prisma.badge.findMany({
      orderBy: { createdAt: 'desc' },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        },
        _count: {
          select: {
            studentBadges: true
          }
        }
      }
    });

    res.json({
      success: true,
      data: badges,
      message: 'Badges retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching badges:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// Create new badge
router.post('/', authenticateToken, requireRole(['ADMIN', 'GURU']), async (req, res) => {
  try {
    const { name, description, xpReward, icon } = req.body;

    // Validation
    if (!name || !description || !xpReward || !icon) {
      return res.status(400).json({
        success: false,
        error: 'Name, description, xpReward, and icon are required'
      });
    }

    if (xpReward < 1 || xpReward > 1000) {
      return res.status(400).json({
        success: false,
        error: 'XP reward must be between 1 and 1000'
      });
    }

    // Check if badge name already exists
    const existingBadge = await prisma.badge.findFirst({
      where: { name }
    });

    if (existingBadge) {
      return res.status(400).json({
        success: false,
        error: 'Badge with this name already exists'
      });
    }

    const badge = await prisma.badge.create({
      data: {
        name,
        description,
        xpReward: parseInt(xpReward),
        icon,
        createdById: req.user.id
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      data: badge,
      message: 'Badge created successfully'
    });
  } catch (error) {
    console.error('Error creating badge:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

export default router;
