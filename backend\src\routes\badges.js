// Badges Routes
import express from 'express';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// Semua routes memerlukan authentication
router.use(authenticateToken);

// GET /api/badges - Get all badges
router.get('/', async (req, res) => {
  res.json({
    success: true,
    data: { badges: [] },
    message: 'Badges feature coming soon'
  });
});

export default router;
