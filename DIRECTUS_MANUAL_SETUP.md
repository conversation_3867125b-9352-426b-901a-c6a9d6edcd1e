# 🔧 Directus Manual Setup Guide

## 📋 Prerequisites
1. Access to Directus admin panel
2. Admin credentials
3. About 30-45 minutes to complete

## 🚀 Step-by-Step Setup

### **STEP 1: Access Directus Admin**
1. Open your Directus admin URL
2. Login with admin credentials
3. Navigate to **Settings** → **Data Model**

---

### **STEP 2: Create Core Collections (No Dependencies)**

#### 2.1 Create "schools" Collection
1. Click **"Create Collection"**
2. **Collection Name**: `schools`
3. **Primary Key Field**: `id` (UUID, Auto-generated)
4. Click **"Create Collection"**

**Add Fields to schools:**
- `name` (String, Required) - School name
- `address` (Text) - School address  
- `phone` (String) - Phone number
- `email` (String) - Email address
- `created_at` (DateTime, Auto-generated)
- `updated_at` (DateTime, Auto-generated)

#### 2.2 Create "subjects" Collection
1. Click **"Create Collection"**
2. **Collection Name**: `subjects`
3. **Primary Key Field**: `id` (UUID, Auto-generated)

**Add Fields to subjects:**
- `name` (String, Required) - Subject name
- `code` (String, Required, Unique) - Subject code (MTK, BIN, etc.)
- `description` (Text) - Subject description
- `created_at` (DateTime, Auto-generated)
- `updated_at` (DateTime, Auto-generated)

#### 2.3 Create "badges" Collection
1. Click **"Create Collection"**
2. **Collection Name**: `badges`
3. **Primary Key Field**: `id` (UUID, Auto-generated)

**Add Fields to badges:**
- `name` (String, Required) - Badge name
- `description` (Text) - Badge description
- `icon` (String) - Badge icon (emoji or icon name)
- `criteria` (Text) - How to earn this badge
- `xp_reward` (Integer, Default: 0) - XP reward for earning badge
- `created_at` (DateTime, Auto-generated)
- `updated_at` (DateTime, Auto-generated)

---

### **STEP 3: Create Collections with Dependencies**

#### 3.1 Create "classes" Collection
1. Click **"Create Collection"**
2. **Collection Name**: `classes`
3. **Primary Key Field**: `id` (UUID, Auto-generated)

**Add Fields to classes:**
- `name` (String, Required) - Class name (7A, 8B, etc.)
- `grade_level` (String, Required) - Grade level (7, 8, 9, etc.)
- `school_id` (UUID, Many-to-One → schools) - Related school
- `teacher_id` (UUID, Many-to-One → directus_users) - Class teacher
- `academic_year` (String, Required, Default: "2024/2025") - Academic year
- `student_count` (Integer, Default: 0) - Number of students
- `created_at` (DateTime, Auto-generated)
- `updated_at` (DateTime, Auto-generated)

#### 3.2 Create "students" Collection
1. Click **"Create Collection"**
2. **Collection Name**: `students`
3. **Primary Key Field**: `id` (UUID, Auto-generated)

**Add Fields to students:**
- `student_id` (String, Required, Unique) - NISN (10 digits)
- `first_name` (String, Required) - First name
- `last_name` (String, Required) - Last name
- `email` (String) - Email address
- `class_id` (UUID, Many-to-One → classes) - Student's class
- `date_of_birth` (Date) - Birth date
- `gender` (String, Options: L, P) - Gender
- `address` (Text) - Home address
- `phone` (String) - Phone number
- `parent_name` (String) - Parent/guardian name
- `parent_phone` (String) - Parent phone
- `status` (String, Options: active, inactive, graduated, Default: active)
- `created_at` (DateTime, Auto-generated)
- `updated_at` (DateTime, Auto-generated)

#### 3.3 Create "grades" Collection
1. Click **"Create Collection"**
2. **Collection Name**: `grades`
3. **Primary Key Field**: `id` (UUID, Auto-generated)

**Add Fields to grades:**
- `student_id` (UUID, Many-to-One → students) - Student
- `subject_id` (UUID, Many-to-One → subjects) - Subject
- `class_id` (UUID, Many-to-One → classes) - Class
- `grade_type` (String, Options: tugas, quiz, ujian, praktik) - Grade type
- `score` (Float, Required) - Score achieved
- `max_score` (Float, Required) - Maximum possible score
- `description` (Text) - Grade description
- `date` (Date, Required) - Grade date
- `created_by` (UUID, Many-to-One → directus_users) - Teacher who created
- `created_at` (DateTime, Auto-generated)
- `updated_at` (DateTime, Auto-generated)

#### 3.4 Create "student_xp" Collection
1. Click **"Create Collection"**
2. **Collection Name**: `student_xp`
3. **Primary Key Field**: `id` (UUID, Auto-generated)

**Add Fields to student_xp:**
- `student_id` (UUID, Many-to-One → students, Unique) - Student
- `total_xp` (Integer, Default: 0) - Total XP earned
- `level` (Integer, Default: 1) - Current level
- `level_name` (String, Options: Pemula, Pelajar, Cendekiawan, Ahli, Master)
- `updated_at` (DateTime, Auto-generated)

#### 3.5 Create "student_badges" Collection
1. Click **"Create Collection"**
2. **Collection Name**: `student_badges`
3. **Primary Key Field**: `id` (UUID, Auto-generated)

**Add Fields to student_badges:**
- `student_id` (UUID, Many-to-One → students) - Student
- `badge_id` (UUID, Many-to-One → badges) - Badge earned
- `earned_at` (DateTime, Auto-generated) - When earned

---

### **STEP 4: Create Learning & Assessment Collections**

#### 4.1 Create "learning_plans" Collection (RPP)
1. Click **"Create Collection"**
2. **Collection Name**: `learning_plans`
3. **Primary Key Field**: `id` (UUID, Auto-generated)

**Add Fields to learning_plans:**
- `title` (String, Required) - RPP title
- `class_id` (UUID, Many-to-One → classes) - Target class
- `subject_id` (UUID, Many-to-One → subjects) - Subject
- `objectives` (JSON) - Learning objectives
- `materials` (JSON) - Learning materials
- `methods` (JSON) - Teaching methods
- `activities` (JSON) - Learning activities
- `assessments` (JSON) - Assessment methods
- `duration` (Integer) - Duration in minutes
- `date` (Date, Required) - Lesson date
- `created_by` (UUID, Many-to-One → directus_users) - Creator
- `created_at` (DateTime, Auto-generated)
- `updated_at` (DateTime, Auto-generated)

#### 4.2 Create "question_categories" Collection
1. Click **"Create Collection"**
2. **Collection Name**: `question_categories`
3. **Primary Key Field**: `id` (UUID, Auto-generated)

**Add Fields to question_categories:**
- `name` (String, Required) - Category name
- `description` (Text) - Category description
- `subject_id` (UUID, Many-to-One → subjects) - Related subject
- `created_at` (DateTime, Auto-generated)
- `updated_at` (DateTime, Auto-generated)

#### 4.3 Create "questions" Collection
1. Click **"Create Collection"**
2. **Collection Name**: `questions`
3. **Primary Key Field**: `id` (UUID, Auto-generated)

**Add Fields to questions:**
- `question_text` (Text, Required) - Question content
- `question_type` (String, Options: multiple_choice, multiple_choice_complex, true_false, fill_blank, essay)
- `difficulty` (String, Options: easy, medium, hard) - Difficulty level
- `subject_id` (UUID, Many-to-One → subjects) - Subject
- `category_id` (UUID, Many-to-One → question_categories) - Category
- `correct_answer` (Text, Required) - Correct answer
- `explanation` (Text) - Answer explanation
- `tags` (JSON) - Question tags
- `created_by` (UUID, Many-to-One → directus_users) - Creator
- `created_at` (DateTime, Auto-generated)
- `updated_at` (DateTime, Auto-generated)

#### 4.4 Create "question_options" Collection
1. Click **"Create Collection"**
2. **Collection Name**: `question_options`
3. **Primary Key Field**: `id` (UUID, Auto-generated)

**Add Fields to question_options:**
- `question_id` (UUID, Many-to-One → questions) - Related question
- `option_text` (Text, Required) - Option content
- `is_correct` (Boolean, Default: false) - Is this the correct answer
- `order_index` (Integer) - Display order
- `created_at` (DateTime, Auto-generated)
- `updated_at` (DateTime, Auto-generated)

#### 4.5 Create "activities" Collection
1. Click **"Create Collection"**
2. **Collection Name**: `activities`
3. **Primary Key Field**: `id` (UUID, Auto-generated)

**Add Fields to activities:**
- `type` (String, Options: grade, xp, badge, question, exercise) - Activity type
- `title` (String, Required) - Activity title
- `description` (Text) - Activity description
- `user_id` (UUID, Many-to-One → directus_users) - Related user
- `created_at` (DateTime, Auto-generated)

---

### **STEP 5: Update directus_users Collection**

Add these fields to existing **directus_users** collection:
- `nip` (String, Unique) - NIP for admin and guru
- `role` (String, Options: admin, guru, Default: guru) - User role

---

### **STEP 6: Insert Sample Data**

#### Sample Schools
```json
{
  "name": "SMA Digital Pelangi",
  "address": "Jl. Pendidikan No. 123, Jakarta",
  "phone": "021-12345678",
  "email": "<EMAIL>"
}
```

#### Sample Subjects
```json
[
  {"name": "Matematika", "code": "MTK", "description": "Mata pelajaran Matematika"},
  {"name": "Bahasa Indonesia", "code": "BIN", "description": "Mata pelajaran Bahasa Indonesia"},
  {"name": "IPA", "code": "IPA", "description": "Ilmu Pengetahuan Alam"},
  {"name": "IPS", "code": "IPS", "description": "Ilmu Pengetahuan Sosial"},
  {"name": "Bahasa Inggris", "code": "ENG", "description": "Mata pelajaran Bahasa Inggris"}
]
```

#### Sample Classes
```json
[
  {"name": "7A", "grade_level": "7", "academic_year": "2024/2025", "student_count": 30},
  {"name": "7B", "grade_level": "7", "academic_year": "2024/2025", "student_count": 28},
  {"name": "8A", "grade_level": "8", "academic_year": "2024/2025", "student_count": 32}
]
```

#### Sample Students
```json
[
  {
    "student_id": "1234567890",
    "first_name": "Ahmad",
    "last_name": "Pratama",
    "email": "<EMAIL>",
    "gender": "L",
    "status": "active"
  },
  {
    "student_id": "1234567891", 
    "first_name": "Siti",
    "last_name": "Nurhaliza",
    "email": "<EMAIL>",
    "gender": "P", 
    "status": "active"
  }
]
```

#### Sample Badges
```json
[
  {
    "name": "Perfect Attendance",
    "description": "Hadir sempurna selama 1 bulan",
    "icon": "🎯",
    "criteria": "Tidak absen selama 30 hari berturut-turut",
    "xp_reward": 100
  },
  {
    "name": "Quiz Master",
    "description": "Menyelesaikan 10 quiz dengan nilai sempurna", 
    "icon": "🧠",
    "criteria": "Mendapat nilai 100 pada 10 quiz berbeda",
    "xp_reward": 150
  }
]
```

---

### **STEP 7: Set Permissions**

#### Admin Role
- **Full Access**: All collections (Create, Read, Update, Delete)

#### Guru Role  
- **Full Access**: classes, students, grades, learning_plans, questions, question_options, activities, student_xp, student_badges
- **Read Only**: subjects, question_categories, badges, schools
- **No Access**: directus_users (except own profile)

---

## ✅ **Verification Checklist**

After completing setup:
- [ ] All 13 collections created
- [ ] All fields added with correct types
- [ ] Relationships properly configured
- [ ] Sample data inserted
- [ ] Permissions configured
- [ ] API accessible from frontend

---

**Estimated Time**: 30-45 minutes
**Next Step**: Test frontend connection with real data!
