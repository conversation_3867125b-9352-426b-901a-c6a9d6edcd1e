// Class Service for Guru Digital Pelangi
import { apiClient, DEMO_MODE } from './apiClient';
import { ApiResponse, Class } from './types';

export const classService = {
  async getClasses(params?: { page?: number; limit?: number; search?: string }): Promise<ApiResponse<Class[]>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: [
            {
              id: 'class-1',
              name: 'Matematika Kelas 9C',
              subject: '<PERSON><PERSON><PERSON><PERSON>',
              description: 'Kelas matematika untuk siswa kelas 9C dengan fokus pada aljabar dan geometri',
              studentCount: 25
            },
            {
              id: 'class-2',
              name: 'Fisika Kelas 11A',
              subject: '<PERSON>sik<PERSON>',
              description: 'Kelas fisika untuk siswa kelas 11A dengan praktikum laboratorium',
              studentCount: 23
            },
            {
              id: 'class-3',
              name: 'Bahasa Indonesia Kelas 7A',
              subject: 'Bahasa Indonesia',
              description: 'Kelas bahasa Indonesia dengan fokus pada kemampuan menulis dan berbicara',
              studentCount: 28
            }
          ]
        };
      }

      const response = await apiClient.get('/classes', { params });
      return {
        success: true,
        data: response.data.data.classes,
        pagination: response.data.data.pagination
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal mengambil data kelas'
      };
    }
  },

  async createClass(data: Omit<Class, 'id' | 'studentCount'>): Promise<ApiResponse<Class>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: { id: 'new-class', studentCount: 0, ...data },
          message: 'Kelas berhasil dibuat (Demo Mode)'
        };
      }

      const response = await apiClient.post('/classes', data);
      return {
        success: true,
        data: response.data.data.class,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal membuat kelas'
      };
    }
  },

  async updateClass(id: string, data: Partial<Class>): Promise<ApiResponse<Class>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          data: { id, ...data } as Class,
          message: 'Kelas berhasil diupdate (Demo Mode)'
        };
      }

      const response = await apiClient.put(`/classes/${id}`, data);
      return {
        success: true,
        data: response.data.data.class,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal update kelas'
      };
    }
  },

  async deleteClass(id: string): Promise<ApiResponse<void>> {
    try {
      if (DEMO_MODE) {
        return {
          success: true,
          message: 'Kelas berhasil dihapus (Demo Mode)'
        };
      }

      const response = await apiClient.delete(`/classes/${id}`);
      return {
        success: true,
        message: response.data.message
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Gagal menghapus kelas'
      };
    }
  }
};
