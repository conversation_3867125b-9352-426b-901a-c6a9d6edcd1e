generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model School {
  id        String   @id @default(cuid())
  name      String
  address   String?  @db.Text
  phone     String?
  email     String?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  classes   Class[]

  @@map("schools")
}

model Subject {
  id                 String             @id @default(cuid())
  name               String
  code               String             @unique
  description        String?            @db.Text
  createdAt          DateTime           @default(now()) @map("created_at")
  updatedAt          DateTime           @updatedAt @map("updated_at")
  classes            Class[]
  grades             Grade[]
  learningPlans      LearningPlan[]
  questionCategories QuestionCategory[]
  questions          Question[]

  @@map("subjects")
}

model Class {
  id            String         @id @default(cuid())
  name          String
  subjectId     String?        @map("subject_id")
  description   String?        @db.Text
  gradeLevel    String?        @map("grade_level")
  schoolId      String?        @map("school_id")
  academicYear  String         @default("2024/2025") @map("academic_year")
  studentCount  Int            @default(0) @map("student_count")
  createdAt     DateTime       @default(now()) @map("created_at")
  updatedAt     DateTime       @updatedAt @map("updated_at")
  assignments   Assignment[]
  attendances   Attendance[]
  classTeachers ClassTeacher[]
  school        School?        @relation(fields: [schoolId], references: [id])
  subject       Subject?       @relation(fields: [subjectId], references: [id])
  grades        Grade[]
  learningPlans LearningPlan[]
  students      Student[]

  @@index([schoolId], map: "classes_school_id_fkey")
  @@index([subjectId], map: "classes_subject_id_fkey")
  @@map("classes")
}

model ClassTeacher {
  id        String   @id @default(cuid())
  classId   String   @map("class_id")
  teacherId String   @map("teacher_id")
  createdAt DateTime @default(now()) @map("created_at")
  class     Class    @relation(fields: [classId], references: [id], onDelete: Cascade)
  teacher   User     @relation(fields: [teacherId], references: [id], onDelete: Cascade)

  @@unique([classId, teacherId])
  @@index([teacherId], map: "class_teachers_teacher_id_fkey")
  @@map("class_teachers")
}

model User {
  id            String         @id @default(cuid())
  email         String         @unique
  fullName      String         @map("full_name")
  nip           String?        @unique
  role          Role           @default(GURU)
  password      String
  status        Status         @default(ACTIVE)
  createdAt     DateTime       @default(now()) @map("created_at")
  updatedAt     DateTime       @updatedAt @map("updated_at")
  activities    Activity[]
  assignments   Assignment[]
  challenges    Challenge[]
  classTeachers ClassTeacher[]
  grades        Grade[]
  learningPlans LearningPlan[]
  questions     Question[]

  @@map("users")
}

model Student {
  id                      String                   @id @default(cuid())
  studentId               String                   @unique @map("student_id")
  fullName                String                   @map("full_name")
  email                   String?
  classId                 String?                  @map("class_id")
  dateOfBirth             DateTime?                @map("date_of_birth") @db.Date
  gender                  Gender?
  address                 String?                  @db.Text
  phone                   String?
  parentName              String?                  @map("parent_name")
  parentPhone             String?                  @map("parent_phone")
  status                  Status                   @default(ACTIVE)
  createdAt               DateTime                 @default(now()) @map("created_at")
  updatedAt               DateTime                 @updatedAt @map("updated_at")
  submissions             AssignmentSubmission[]
  attendances             Attendance[]
  challengeParticipations ChallengeParticipation[]
  grades                  Grade[]
  achievements            StudentAchievement[]
  studentBadges           StudentBadge[]
  studentXp               StudentXp?
  class                   Class?                   @relation(fields: [classId], references: [id])

  @@index([classId], map: "students_class_id_fkey")
  @@map("students")
}

model Grade {
  id            String    @id @default(cuid())
  studentId     String    @map("student_id")
  subjectId     String    @map("subject_id")
  classId       String    @map("class_id")
  gradeType     GradeType @map("grade_type")
  score         Float
  maxScore      Float     @map("max_score")
  description   String?   @db.Text
  date          DateTime  @db.Date
  createdBy     String    @map("created_by")
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  class         Class     @relation(fields: [classId], references: [id])
  createdByUser User      @relation(fields: [createdBy], references: [id])
  student       Student   @relation(fields: [studentId], references: [id], onDelete: Cascade)
  subject       Subject   @relation(fields: [subjectId], references: [id])

  @@index([classId], map: "grades_class_id_fkey")
  @@index([createdBy], map: "grades_created_by_fkey")
  @@index([studentId], map: "grades_student_id_fkey")
  @@index([subjectId], map: "grades_subject_id_fkey")
  @@map("grades")
}

model Attendance {
  id        String           @id @default(cuid())
  studentId String           @map("student_id")
  classId   String           @map("class_id")
  date      DateTime         @db.Date
  status    AttendanceStatus
  reason    AbsentReason?
  timeIn    String?          @map("time_in")
  notes     String?          @db.Text
  createdAt DateTime         @default(now()) @map("created_at")
  updatedAt DateTime         @updatedAt @map("updated_at")
  class     Class            @relation(fields: [classId], references: [id])
  student   Student          @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@unique([studentId, classId, date])
  @@index([classId], map: "attendances_class_id_fkey")
  @@map("attendances")
}

model StudentXp {
  id               String    @id @default(cuid())
  studentId        String    @unique @map("student_id")
  totalXp          Int       @default(0) @map("total_xp")
  level            Int       @default(1)
  levelName        String    @default("Pemula") @map("level_name")
  attendanceStreak Int       @default(0) @map("attendance_streak")
  assignmentStreak Int       @default(0) @map("assignment_streak")
  lastAttendance   DateTime? @map("last_attendance") @db.Date
  lastAssignment   DateTime? @map("last_assignment") @db.Date
  updatedAt        DateTime  @updatedAt @map("updated_at")
  student          Student   @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@map("student_xp")
}

model Badge {
  id            String         @id @default(cuid())
  name          String
  description   String?        @db.Text
  icon          String?
  criteria      String?        @db.Text
  xpReward      Int            @default(0) @map("xp_reward")
  createdAt     DateTime       @default(now()) @map("created_at")
  updatedAt     DateTime       @updatedAt @map("updated_at")
  studentBadges StudentBadge[]

  @@map("badges")
}

model StudentBadge {
  id        String   @id @default(cuid())
  studentId String   @map("student_id")
  badgeId   String   @map("badge_id")
  earnedAt  DateTime @default(now()) @map("earned_at")
  badge     Badge    @relation(fields: [badgeId], references: [id])
  student   Student  @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@unique([studentId, badgeId])
  @@index([badgeId], map: "student_badges_badge_id_fkey")
  @@map("student_badges")
}

model GamificationSettings {
  id                String   @id @default(cuid())
  name              String   @unique
  description       String?  @db.Text
  xpPerGrade        Int      @default(1) @map("xp_per_grade")
  xpAttendanceBonus Int      @default(10) @map("xp_attendance_bonus")
  xpAbsentPenalty   Int      @default(5) @map("xp_absent_penalty")
  levelThresholds   Json     @map("level_thresholds")
  isActive          Boolean  @default(true) @map("is_active")
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  @@map("gamification_settings")
}

model StudentAchievement {
  id          String   @id @default(cuid())
  studentId   String   @map("student_id")
  type        String
  title       String
  description String?  @db.Text
  xpReward    Int      @default(0) @map("xp_reward")
  earnedAt    DateTime @default(now()) @map("earned_at")
  metadata    Json?
  student     Student  @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@index([studentId], map: "student_achievements_student_id_fkey")
  @@map("student_achievements")
}

model Challenge {
  id             String                   @id @default(cuid())
  title          String
  description    String                   @db.Text
  duration       Int
  targetType     String
  targetData     Json?
  xpReward       Int                      @default(0) @map("xp_reward")
  isActive       Boolean                  @default(true) @map("is_active")
  startDate      DateTime                 @default(now()) @map("start_date")
  endDate        DateTime                 @map("end_date")
  createdBy      String                   @map("created_by")
  createdAt      DateTime                 @default(now()) @map("created_at")
  updatedAt      DateTime                 @updatedAt @map("updated_at")
  participations ChallengeParticipation[]
  createdByUser  User                     @relation(fields: [createdBy], references: [id])

  @@index([createdBy], map: "challenges_created_by_fkey")
  @@map("challenges")
}

model ChallengeParticipation {
  id          String    @id @default(cuid())
  challengeId String    @map("challenge_id")
  studentId   String    @map("student_id")
  status      String    @default("ACTIVE")
  progress    Int       @default(0)
  completedAt DateTime? @map("completed_at")
  xpEarned    Int       @default(0) @map("xp_earned")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  challenge   Challenge @relation(fields: [challengeId], references: [id], onDelete: Cascade)
  student     Student   @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@unique([challengeId, studentId])
  @@index([studentId], map: "challenge_participations_student_id_fkey")
  @@map("challenge_participations")
}

model Assignment {
  id           String                 @id @default(cuid())
  title        String
  description  String?                @db.Text
  instructions String?                @db.Text
  points       Int                    @default(100)
  deadline     DateTime
  status       AssignmentStatus       @default(DRAFT)
  type         AssignmentType         @default(TUGAS_HARIAN)
  classId      String                 @map("class_id")
  teacherId    String                 @map("teacher_id")
  createdAt    DateTime               @default(now()) @map("created_at")
  updatedAt    DateTime               @updatedAt @map("updated_at")
  submissions  AssignmentSubmission[]
  class        Class                  @relation(fields: [classId], references: [id], onDelete: Cascade)
  teacher      User                   @relation(fields: [teacherId], references: [id], onDelete: Cascade)

  @@index([classId], map: "assignments_class_id_fkey")
  @@index([teacherId], map: "assignments_teacher_id_fkey")
  @@map("assignments")
}

model AssignmentSubmission {
  id           String           @id @default(cuid())
  assignmentId String           @map("assignment_id")
  studentId    String           @map("student_id")
  content      String?          @db.Text
  attachments  String?          @db.Text
  score        Float?
  feedback     String?          @db.Text
  status       SubmissionStatus @default(NOT_SUBMITTED)
  submittedAt  DateTime?        @map("submitted_at")
  gradedAt     DateTime?        @map("graded_at")
  createdAt    DateTime         @default(now()) @map("created_at")
  updatedAt    DateTime         @updatedAt @map("updated_at")
  assignment   Assignment       @relation(fields: [assignmentId], references: [id], onDelete: Cascade)
  student      Student          @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@unique([assignmentId, studentId])
  @@index([studentId], map: "assignment_submissions_student_id_fkey")
  @@map("assignment_submissions")
}

model LearningPlan {
  id            String   @id @default(cuid())
  title         String
  classId       String   @map("class_id")
  subjectId     String   @map("subject_id")
  objectives    Json?
  materials     Json?
  methods       Json?
  activities    Json?
  assessments   Json?
  duration      Int?
  date          DateTime @db.Date
  createdBy     String   @map("created_by")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")
  class         Class    @relation(fields: [classId], references: [id])
  createdByUser User     @relation(fields: [createdBy], references: [id])
  subject       Subject  @relation(fields: [subjectId], references: [id])

  @@index([classId], map: "learning_plans_class_id_fkey")
  @@index([createdBy], map: "learning_plans_created_by_fkey")
  @@index([subjectId], map: "learning_plans_subject_id_fkey")
  @@map("learning_plans")
}

model QuestionCategory {
  id          String     @id @default(cuid())
  name        String
  description String?    @db.Text
  subjectId   String     @map("subject_id")
  createdAt   DateTime   @default(now()) @map("created_at")
  updatedAt   DateTime   @updatedAt @map("updated_at")
  subject     Subject    @relation(fields: [subjectId], references: [id])
  questions   Question[]

  @@index([subjectId], map: "question_categories_subject_id_fkey")
  @@map("question_categories")
}

model Question {
  id            String            @id @default(cuid())
  questionText  String            @map("question_text") @db.Text
  questionType  QuestionType      @map("question_type")
  difficulty    Difficulty
  subjectId     String            @map("subject_id")
  categoryId    String?           @map("category_id")
  correctAnswer String            @map("correct_answer") @db.Text
  explanation   String?           @db.Text
  tags          Json?
  createdBy     String            @map("created_by")
  createdAt     DateTime          @default(now()) @map("created_at")
  updatedAt     DateTime          @updatedAt @map("updated_at")
  options       QuestionOption[]
  category      QuestionCategory? @relation(fields: [categoryId], references: [id])
  createdByUser User              @relation(fields: [createdBy], references: [id])
  subject       Subject           @relation(fields: [subjectId], references: [id])

  @@index([categoryId], map: "questions_category_id_fkey")
  @@index([createdBy], map: "questions_created_by_fkey")
  @@index([subjectId], map: "questions_subject_id_fkey")
  @@map("questions")
}

model QuestionOption {
  id         String   @id @default(cuid())
  questionId String   @map("question_id")
  optionText String   @map("option_text") @db.Text
  isCorrect  Boolean  @default(false) @map("is_correct")
  orderIndex Int?     @map("order_index")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")
  question   Question @relation(fields: [questionId], references: [id], onDelete: Cascade)

  @@index([questionId], map: "question_options_question_id_fkey")
  @@map("question_options")
}

model Activity {
  id          String       @id @default(cuid())
  type        ActivityType
  title       String
  description String?      @db.Text
  userId      String?      @map("user_id")
  createdAt   DateTime     @default(now()) @map("created_at")
  user        User?        @relation(fields: [userId], references: [id])

  @@index([userId], map: "activities_user_id_fkey")
  @@map("activities")
}

enum Role {
  ADMIN
  GURU
}

enum Status {
  ACTIVE
  INACTIVE
  GRADUATED
}

enum Gender {
  L
  P
}

enum GradeType {
  TUGAS_HARIAN
  QUIZ
  ULANGAN_HARIAN
  PTS
  PAS
  PRAKTIK
  SIKAP
  KETERAMPILAN
}

enum AssignmentStatus {
  DRAFT
  PUBLISHED
  CLOSED
}

enum AssignmentType {
  TUGAS_HARIAN
  QUIZ
  ULANGAN_HARIAN
  PTS
  PAS
  PRAKTIK
  PROYEK
}

enum SubmissionStatus {
  NOT_SUBMITTED
  SUBMITTED
  LATE_SUBMITTED
  GRADED
}

enum AttendanceStatus {
  PRESENT
  ABSENT
  LATE
  EXCUSED
}

enum AbsentReason {
  ALPA
  IZIN
  SAKIT
}

enum QuestionType {
  MULTIPLE_CHOICE
  MULTIPLE_CHOICE_COMPLEX
  TRUE_FALSE
  FILL_BLANK
  ESSAY
}

enum Difficulty {
  EASY
  MEDIUM
  HARD
}

enum ActivityType {
  GRADE
  XP
  BADGE
  QUESTION
  EXERCISE
}
