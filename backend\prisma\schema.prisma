// Prisma Schema for Guru Digital Pelangi
// Database: MySQL
// ORM: Prisma 6.1.0

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// Core Models

model School {
  id        String   @id @default(cuid())
  name      String
  address   String?  @db.Text
  phone     String?
  email     String?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  classes Class[]

  @@map("schools")
}

model Subject {
  id          String   @id @default(cuid())
  name        String
  code        String   @unique
  description String?  @db.Text
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  grades            Grade[]
  learningPlans     LearningPlan[]
  questions         Question[]
  questionCategories QuestionCategory[]

  @@map("subjects")
}

model Class {
  id           String   @id @default(cuid())
  name         String
  subject      String?
  startTime    String?  @map("start_time")
  endTime      String?  @map("end_time")
  description  String?  @db.Text
  gradeLevel   String?  @map("grade_level")
  schoolId     String?  @map("school_id")
  teacherId    String?  @map("teacher_id")
  academicYear String   @default("2024/2025") @map("academic_year")
  studentCount Int      @default(0) @map("student_count")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  school        School?        @relation(fields: [schoolId], references: [id])
  teacher       User?          @relation(fields: [teacherId], references: [id])
  students      Student[]
  grades        Grade[]
  learningPlans LearningPlan[]
  attendances   Attendance[]

  @@map("classes")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  firstName String   @map("first_name")
  lastName  String   @map("last_name")
  nip       String?  @unique // For admin and guru
  role      Role     @default(GURU)
  password  String
  status    Status   @default(ACTIVE)
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  classes       Class[]
  grades        Grade[]
  learningPlans LearningPlan[]
  questions     Question[]
  activities    Activity[]

  @@map("users")
}

model Student {
  id           String    @id @default(cuid())
  studentId    String    @unique @map("student_id") // NISN
  firstName    String    @map("first_name")
  lastName     String    @map("last_name")
  email        String?
  classId      String?   @map("class_id")
  dateOfBirth  DateTime? @map("date_of_birth") @db.Date
  gender       Gender?
  address      String?   @db.Text
  phone        String?
  parentName   String?   @map("parent_name")
  parentPhone  String?   @map("parent_phone")
  status       Status    @default(ACTIVE)
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")

  // Relations
  class         Class?          @relation(fields: [classId], references: [id])
  grades        Grade[]
  studentXp     StudentXp?
  studentBadges StudentBadge[]
  attendances   Attendance[]

  @@map("students")
}

// Academic Models

model Grade {
  id          String    @id @default(cuid())
  studentId   String    @map("student_id")
  subjectId   String    @map("subject_id")
  classId     String    @map("class_id")
  gradeType   GradeType @map("grade_type")
  score       Float
  maxScore    Float     @map("max_score")
  description String?   @db.Text
  date        DateTime  @db.Date
  createdBy   String    @map("created_by")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  // Relations
  student   Student @relation(fields: [studentId], references: [id], onDelete: Cascade)
  subject   Subject @relation(fields: [subjectId], references: [id])
  class     Class   @relation(fields: [classId], references: [id])
  createdByUser User @relation(fields: [createdBy], references: [id])

  @@map("grades")
}

model Attendance {
  id        String           @id @default(cuid())
  studentId String           @map("student_id")
  classId   String           @map("class_id")
  date      DateTime         @db.Date
  status    AttendanceStatus
  timeIn    String?          @map("time_in")
  notes     String?          @db.Text
  createdAt DateTime         @default(now()) @map("created_at")
  updatedAt DateTime         @updatedAt @map("updated_at")

  // Relations
  student Student @relation(fields: [studentId], references: [id], onDelete: Cascade)
  class   Class   @relation(fields: [classId], references: [id])

  @@unique([studentId, classId, date])
  @@map("attendances")
}

// Gamification Models

model StudentXp {
  id        String   @id @default(cuid())
  studentId String   @unique @map("student_id")
  totalXp   Int      @default(0) @map("total_xp")
  level     Int      @default(1)
  levelName String   @default("Pemula") @map("level_name")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  student Student @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@map("student_xp")
}

model Badge {
  id          String   @id @default(cuid())
  name        String
  description String?  @db.Text
  icon        String?
  criteria    String?  @db.Text
  xpReward    Int      @default(0) @map("xp_reward")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  studentBadges StudentBadge[]

  @@map("badges")
}

model StudentBadge {
  id        String   @id @default(cuid())
  studentId String   @map("student_id")
  badgeId   String   @map("badge_id")
  earnedAt  DateTime @default(now()) @map("earned_at")

  // Relations
  student Student @relation(fields: [studentId], references: [id], onDelete: Cascade)
  badge   Badge   @relation(fields: [badgeId], references: [id])

  @@unique([studentId, badgeId])
  @@map("student_badges")
}

// Learning & Assessment Models

model LearningPlan {
  id          String   @id @default(cuid())
  title       String
  classId     String   @map("class_id")
  subjectId   String   @map("subject_id")
  objectives  Json?
  materials   Json?
  methods     Json?
  activities  Json?
  assessments Json?
  duration    Int? // in minutes
  date        DateTime @db.Date
  createdBy   String   @map("created_by")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  class     Class   @relation(fields: [classId], references: [id])
  subject   Subject @relation(fields: [subjectId], references: [id])
  createdByUser User @relation(fields: [createdBy], references: [id])

  @@map("learning_plans")
}

model QuestionCategory {
  id          String   @id @default(cuid())
  name        String
  description String?  @db.Text
  subjectId   String   @map("subject_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  subject   Subject    @relation(fields: [subjectId], references: [id])
  questions Question[]

  @@map("question_categories")
}

model Question {
  id             String       @id @default(cuid())
  questionText   String       @map("question_text") @db.Text
  questionType   QuestionType @map("question_type")
  difficulty     Difficulty
  subjectId      String       @map("subject_id")
  categoryId     String?      @map("category_id")
  correctAnswer  String       @map("correct_answer") @db.Text
  explanation    String?      @db.Text
  tags           Json?
  createdBy      String       @map("created_by")
  createdAt      DateTime     @default(now()) @map("created_at")
  updatedAt      DateTime     @updatedAt @map("updated_at")

  // Relations
  subject   Subject           @relation(fields: [subjectId], references: [id])
  category  QuestionCategory? @relation(fields: [categoryId], references: [id])
  createdByUser User          @relation(fields: [createdBy], references: [id])
  options   QuestionOption[]

  @@map("questions")
}

model QuestionOption {
  id         String   @id @default(cuid())
  questionId String   @map("question_id")
  optionText String   @map("option_text") @db.Text
  isCorrect  Boolean  @default(false) @map("is_correct")
  orderIndex Int?     @map("order_index")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  // Relations
  question Question @relation(fields: [questionId], references: [id], onDelete: Cascade)

  @@map("question_options")
}

model Activity {
  id          String       @id @default(cuid())
  type        ActivityType
  title       String
  description String?      @db.Text
  userId      String?      @map("user_id")
  createdAt   DateTime     @default(now()) @map("created_at")

  // Relations
  user User? @relation(fields: [userId], references: [id])

  @@map("activities")
}

// Enums

enum Role {
  ADMIN
  GURU
}

enum Status {
  ACTIVE
  INACTIVE
  GRADUATED
}

enum Gender {
  L // Laki-laki
  P // Perempuan
}

enum GradeType {
  TUGAS
  QUIZ
  UJIAN
  PRAKTIK
}

enum AttendanceStatus {
  PRESENT
  ABSENT
  LATE
  EXCUSED
}

enum QuestionType {
  MULTIPLE_CHOICE
  MULTIPLE_CHOICE_COMPLEX
  TRUE_FALSE
  FILL_BLANK
  ESSAY
}

enum Difficulty {
  EASY
  MEDIUM
  HARD
}

enum ActivityType {
  GRADE
  XP
  BADGE
  QUESTION
  EXERCISE
}
