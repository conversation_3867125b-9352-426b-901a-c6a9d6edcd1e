// Prisma Schema for Guru Digital Pelangi
// Database: MySQL
// ORM: Prisma 6.1.0

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// Core Models

model School {
  id        String   @id @default(cuid())
  name      String
  address   String?  @db.Text
  phone     String?
  email     String?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  classes Class[]

  @@map("schools")
}

model Subject {
  id          String   @id @default(cuid())
  name        String
  code        String   @unique
  description String?  @db.Text
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  classes           Class[]
  grades            Grade[]
  learningPlans     LearningPlan[]
  questions         Question[]
  questionCategories QuestionCategory[]

  @@map("subjects")
}

model Class {
  id           String   @id @default(cuid())
  name         String
  subjectId    String?  @map("subject_id") // Temporary nullable for migration
  startTime    String?  @map("start_time")
  endTime      String?  @map("end_time")
  description  String?  @db.Text
  gradeLevel   String?  @map("grade_level")
  schoolId     String?  @map("school_id")
  academicYear String   @default("2024/2025") @map("academic_year")
  studentCount Int      @default(0) @map("student_count")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  school        School?        @relation(fields: [schoolId], references: [id])
  subject       Subject?       @relation(fields: [subjectId], references: [id])
  students      Student[]
  grades        Grade[]
  learningPlans LearningPlan[]
  attendances   Attendance[]
  classTeachers ClassTeacher[] // Many-to-many with teachers

  // Unique constraint will be added later after migration
  @@map("classes")
}

// Junction table for many-to-many relationship between Class and Teacher
model ClassTeacher {
  id        String   @id @default(cuid())
  classId   String   @map("class_id")
  teacherId String   @map("teacher_id")
  createdAt DateTime @default(now()) @map("created_at")

  // Relations
  class   Class @relation(fields: [classId], references: [id], onDelete: Cascade)
  teacher User  @relation(fields: [teacherId], references: [id], onDelete: Cascade)

  // Unique constraint: satu guru tidak bisa di-assign ke kelas yang sama dua kali
  @@unique([classId, teacherId])
  @@map("class_teachers")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  firstName String   @map("first_name")
  lastName  String   @map("last_name")
  nip       String?  @unique // For admin and guru
  role      Role     @default(GURU)
  password  String
  status    Status   @default(ACTIVE)
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  grades        Grade[]
  learningPlans LearningPlan[]
  questions     Question[]
  activities    Activity[]
  classTeachers ClassTeacher[] // Many-to-many with classes

  @@map("users")
}

model Student {
  id           String    @id @default(cuid())
  studentId    String    @unique @map("student_id") // NISN
  fullName     String    @map("full_name")
  email        String?
  classId      String?   @map("class_id")
  dateOfBirth  DateTime? @map("date_of_birth") @db.Date
  gender       Gender?
  address      String?   @db.Text
  phone        String?
  parentName   String?   @map("parent_name")
  parentPhone  String?   @map("parent_phone")
  status       Status    @default(ACTIVE)
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")

  // Relations
  class         Class?               @relation(fields: [classId], references: [id])
  grades        Grade[]
  studentXp     StudentXp?
  studentBadges StudentBadge[]
  attendances   Attendance[]
  achievements  StudentAchievement[]

  @@map("students")
}

// Academic Models

model Grade {
  id          String    @id @default(cuid())
  studentId   String    @map("student_id")
  subjectId   String    @map("subject_id")
  classId     String    @map("class_id")
  gradeType   GradeType @map("grade_type")
  score       Float
  maxScore    Float     @map("max_score")
  description String?   @db.Text
  date        DateTime  @db.Date
  createdBy   String    @map("created_by")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  // Relations
  student   Student @relation(fields: [studentId], references: [id], onDelete: Cascade)
  subject   Subject @relation(fields: [subjectId], references: [id])
  class     Class   @relation(fields: [classId], references: [id])
  createdByUser User @relation(fields: [createdBy], references: [id])

  @@map("grades")
}

model Attendance {
  id        String           @id @default(cuid())
  studentId String           @map("student_id")
  classId   String           @map("class_id")
  date      DateTime         @db.Date
  status    AttendanceStatus
  reason    AbsentReason?    // Sub-kategori untuk ABSENT: ALPA, IZIN, SAKIT
  timeIn    String?          @map("time_in")
  notes     String?          @db.Text
  createdAt DateTime         @default(now()) @map("created_at")
  updatedAt DateTime         @updatedAt @map("updated_at")

  // Relations
  student Student @relation(fields: [studentId], references: [id], onDelete: Cascade)
  class   Class   @relation(fields: [classId], references: [id])

  @@unique([studentId, classId, date])
  @@map("attendances")
}

// Gamification Models

model StudentXp {
  id                String   @id @default(cuid())
  studentId         String   @unique @map("student_id")
  totalXp           Int      @default(0) @map("total_xp")
  level             Int      @default(1)
  levelName         String   @default("Pemula") @map("level_name")
  attendanceStreak  Int      @default(0) @map("attendance_streak") // Consecutive days present
  assignmentStreak  Int      @default(0) @map("assignment_streak") // Consecutive assignments submitted
  lastAttendance    DateTime? @map("last_attendance") @db.Date
  lastAssignment    DateTime? @map("last_assignment") @db.Date
  updatedAt         DateTime @updatedAt @map("updated_at")

  // Relations
  student Student @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@map("student_xp")
}

model Badge {
  id          String   @id @default(cuid())
  name        String
  description String?  @db.Text
  icon        String?
  criteria    String?  @db.Text
  xpReward    Int      @default(0) @map("xp_reward")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  studentBadges StudentBadge[]

  @@map("badges")
}

model StudentBadge {
  id        String   @id @default(cuid())
  studentId String   @map("student_id")
  badgeId   String   @map("badge_id")
  earnedAt  DateTime @default(now()) @map("earned_at")

  // Relations
  student Student @relation(fields: [studentId], references: [id], onDelete: Cascade)
  badge   Badge   @relation(fields: [badgeId], references: [id])

  @@unique([studentId, badgeId])
  @@map("student_badges")
}

model GamificationSettings {
  id                String   @id @default(cuid())
  name              String   @unique
  description       String?  @db.Text
  xpPerGrade        Int      @default(1) @map("xp_per_grade") // XP per point nilai (misal: nilai 80 = 80 XP)
  xpAttendanceBonus Int      @default(10) @map("xp_attendance_bonus") // Bonus XP untuk hadir
  xpAbsentPenalty   Int      @default(5) @map("xp_absent_penalty") // Penalty XP untuk tidak hadir
  levelThresholds   Json     @map("level_thresholds") // Array threshold XP untuk setiap level
  isActive          Boolean  @default(true) @map("is_active")
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  @@map("gamification_settings")
}

model StudentAchievement {
  id           String   @id @default(cuid())
  studentId    String   @map("student_id")
  type         String   // "PERFECT_SCORE", "ATTENDANCE_STREAK", "TOP_PERFORMER", etc
  title        String
  description  String?  @db.Text
  xpReward     Int      @default(0) @map("xp_reward")
  earnedAt     DateTime @default(now()) @map("earned_at")
  metadata     Json?    // Additional data like streak count, score, etc

  // Relations
  student Student @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@map("student_achievements")
}

// Learning & Assessment Models

model LearningPlan {
  id          String   @id @default(cuid())
  title       String
  classId     String   @map("class_id")
  subjectId   String   @map("subject_id")
  objectives  Json?
  materials   Json?
  methods     Json?
  activities  Json?
  assessments Json?
  duration    Int? // in minutes
  date        DateTime @db.Date
  createdBy   String   @map("created_by")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  class     Class   @relation(fields: [classId], references: [id])
  subject   Subject @relation(fields: [subjectId], references: [id])
  createdByUser User @relation(fields: [createdBy], references: [id])

  @@map("learning_plans")
}

model QuestionCategory {
  id          String   @id @default(cuid())
  name        String
  description String?  @db.Text
  subjectId   String   @map("subject_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  subject   Subject    @relation(fields: [subjectId], references: [id])
  questions Question[]

  @@map("question_categories")
}

model Question {
  id             String       @id @default(cuid())
  questionText   String       @map("question_text") @db.Text
  questionType   QuestionType @map("question_type")
  difficulty     Difficulty
  subjectId      String       @map("subject_id")
  categoryId     String?      @map("category_id")
  correctAnswer  String       @map("correct_answer") @db.Text
  explanation    String?      @db.Text
  tags           Json?
  createdBy      String       @map("created_by")
  createdAt      DateTime     @default(now()) @map("created_at")
  updatedAt      DateTime     @updatedAt @map("updated_at")

  // Relations
  subject   Subject           @relation(fields: [subjectId], references: [id])
  category  QuestionCategory? @relation(fields: [categoryId], references: [id])
  createdByUser User          @relation(fields: [createdBy], references: [id])
  options   QuestionOption[]

  @@map("questions")
}

model QuestionOption {
  id         String   @id @default(cuid())
  questionId String   @map("question_id")
  optionText String   @map("option_text") @db.Text
  isCorrect  Boolean  @default(false) @map("is_correct")
  orderIndex Int?     @map("order_index")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  // Relations
  question Question @relation(fields: [questionId], references: [id], onDelete: Cascade)

  @@map("question_options")
}

model Activity {
  id          String       @id @default(cuid())
  type        ActivityType
  title       String
  description String?      @db.Text
  userId      String?      @map("user_id")
  createdAt   DateTime     @default(now()) @map("created_at")

  // Relations
  user User? @relation(fields: [userId], references: [id])

  @@map("activities")
}

// Enums

enum Role {
  ADMIN
  GURU
}

enum Status {
  ACTIVE
  INACTIVE
  GRADUATED
}

enum Gender {
  L // Laki-laki
  P // Perempuan
}

enum GradeType {
  TUGAS_HARIAN
  QUIZ
  ULANGAN_HARIAN
  PTS              // Penilaian Tengah Semester
  PAS              // Penilaian Akhir Semester
  PRAKTIK
  SIKAP
  KETERAMPILAN
}

enum AttendanceStatus {
  PRESENT
  ABSENT
  LATE
  EXCUSED
}

enum AbsentReason {
  ALPA   // Tanpa keterangan
  IZIN   // Dengan izin
  SAKIT  // Karena sakit
}

enum QuestionType {
  MULTIPLE_CHOICE
  MULTIPLE_CHOICE_COMPLEX
  TRUE_FALSE
  FILL_BLANK
  ESSAY
}

enum Difficulty {
  EASY
  MEDIUM
  HARD
}

enum ActivityType {
  GRADE
  XP
  BADGE
  QUESTION
  EXERCISE
}
