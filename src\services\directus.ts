// Updated: Directus API integration for Guru Digital Pelangi
import { createDirectus, rest, authentication, readItems, createItem, updateItem, deleteItem, readItem } from '@directus/sdk';
import type { DirectusSchema, User, Class, Student, Grade, Question, StudentXP, Badge, Leaderboard, ApiResponse } from '../types/api';

// Directus client configuration
const directusUrl = import.meta.env.VITE_DIRECTUS_URL || 'http://localhost:8055';

export const directus = createDirectus<DirectusSchema>(directusUrl)
  .with(rest())
  .with(authentication());

// Authentication service
export const authService = {
  async login(identifier: string, password: string): Promise<ApiResponse<User>> {
    try {
      // TEMPORARY: Demo mode with hardcoded credentials
      // This will be replaced once Directus collections are setup

      const demoCredentials = [
        {
          identifier: '<EMAIL>',
          password: 'admin123',
          user: {
            id: 'admin-1',
            first_name: 'Admin',
            last_name: 'System',
            email: '<EMAIL>',
            role: 'admin' as const,
            status: 'active' as const,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        },
        {
          identifier: '123456789012345678',
          password: 'guru123',
          user: {
            id: 'guru-1',
            first_name: 'Budi',
            last_name: 'Santoso',
            email: '<EMAIL>',
            role: 'guru' as const,
            nip: '123456789012345678',
            status: 'active' as const,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        },
        {
          identifier: '1234567890',
          password: 'siswa123',
          user: {
            id: 'siswa-1',
            first_name: 'Ahmad',
            last_name: 'Pratama',
            email: '<EMAIL>',
            role: 'siswa' as const,
            student_id: '1234567890',
            class_id: 'class-7a',
            class_name: '7A',
            status: 'active' as const,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        }
      ];

      // Find matching credential
      const credential = demoCredentials.find(
        cred => cred.identifier === identifier && cred.password === password
      );

      if (!credential) {
        return {
          success: false,
          error: 'NIP/NISN/Email atau password salah'
        };
      }

      return {
        success: true,
        data: credential.user as User,
        message: 'Login berhasil (Demo Mode)'
      };

      // TODO: Replace with real Directus authentication once collections are setup
      /*
      // Determine if identifier is NIP (admin/guru) or NISN (siswa)
      const isNISN = /^\d{10}$/.test(identifier); // NISN is typically 10 digits
      const isNIP = /^\d{18}$/.test(identifier); // NIP is typically 18 digits

      let user: any = null;

      if (isNISN) {
        // Login as student using NISN
        const students = await directus.request(readItems('students', {
          filter: { student_id: { _eq: identifier } },
          fields: ['*', 'class.name'],
          limit: 1
        }));

        if (students.length > 0) {
          const student = students[0];
          user = {
            id: student.id,
            first_name: student.first_name,
            last_name: student.last_name,
            email: student.email || `${identifier}@student.local`,
            role: 'siswa',
            student_id: student.student_id,
            class_id: student.class_id,
            class_name: student.class?.name
          };
        }
      } else if (isNIP) {
        // Login as admin/guru using NIP
        const users = await directus.request(readItems('directus_users', {
          filter: {
            _and: [
              { nip: { _eq: identifier } },
              { status: { _eq: 'active' } }
            ]
          },
          limit: 1
        }));

        if (users.length > 0) {
          user = users[0];
        }
      } else {
        // Fallback to email login for admin panel access
        await directus.login(identifier, password);
        const users = await directus.request(readItems('directus_users', {
          filter: { email: { _eq: identifier } },
          limit: 1
        }));

        if (users.length > 0) {
          user = users[0];
        }
      }

      if (!user) {
        return {
          success: false,
          error: 'NIP/NISN tidak ditemukan atau tidak aktif'
        };
      }

      return {
        success: true,
        data: user as User,
        message: 'Login berhasil'
      };
      */
    } catch (error) {
      return {
        success: false,
        error: 'Terjadi kesalahan saat login'
      };
    }
  },

  async logout(): Promise<void> {
    await directus.logout();
  },

  async getCurrentUser(): Promise<User | null> {
    try {
      const user = await directus.request(readItems('users', {
        filter: { id: { _eq: '$CURRENT_USER' } },
        limit: 1
      }));
      return user[0] as User || null;
    } catch (error) {
      return null;
    }
  }
};

// Class management service
export const classService = {
  async getClasses(): Promise<ApiResponse<Class[]>> {
    try {
      const classes = await directus.request(readItems('classes', {
        fields: ['*', 'teacher.first_name', 'teacher.last_name', 'students.id'],
        sort: ['name']
      }));
      
      return {
        success: true,
        data: classes as Class[]
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal mengambil data kelas'
      };
    }
  },

  async createClass(classData: Partial<Class>): Promise<ApiResponse<Class>> {
    try {
      const newClass = await directus.request(createItem('classes', classData));
      return {
        success: true,
        data: newClass as Class,
        message: 'Kelas berhasil dibuat'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal membuat kelas'
      };
    }
  },

  async updateClass(id: string, classData: Partial<Class>): Promise<ApiResponse<Class>> {
    try {
      const updatedClass = await directus.request(updateItem('classes', id, classData));
      return {
        success: true,
        data: updatedClass as Class,
        message: 'Kelas berhasil diupdate'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal mengupdate kelas'
      };
    }
  },

  async deleteClass(id: string): Promise<ApiResponse<void>> {
    try {
      await directus.request(deleteItem('classes', id));
      return {
        success: true,
        message: 'Kelas berhasil dihapus'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal menghapus kelas'
      };
    }
  }
};

// Student management service
export const studentService = {
  async getStudents(classId?: string): Promise<ApiResponse<Student[]>> {
    try {
      const filter = classId ? { class_id: { _eq: classId } } : {};
      const students = await directus.request(readItems('students', {
        fields: ['*', 'class.name', 'xp_data.total_xp', 'xp_data.level'],
        filter,
        sort: ['first_name']
      }));
      
      return {
        success: true,
        data: students as Student[]
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal mengambil data siswa'
      };
    }
  },

  async createStudent(studentData: Partial<Student>): Promise<ApiResponse<Student>> {
    try {
      const newStudent = await directus.request(createItem('students', studentData));
      
      // Create initial XP data for the student
      await directus.request(createItem('student_xp', {
        student_id: newStudent.id,
        total_xp: 0,
        level: 1,
        level_name: 'Pemula'
      }));
      
      return {
        success: true,
        data: newStudent as Student,
        message: 'Siswa berhasil ditambahkan'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal menambahkan siswa'
      };
    }
  },

  async updateStudent(id: string, studentData: Partial<Student>): Promise<ApiResponse<Student>> {
    try {
      const updatedStudent = await directus.request(updateItem('students', id, studentData));
      return {
        success: true,
        data: updatedStudent as Student,
        message: 'Data siswa berhasil diupdate'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal mengupdate data siswa'
      };
    }
  },

  async deleteStudent(id: string): Promise<ApiResponse<void>> {
    try {
      await directus.request(deleteItem('students', id));
      return {
        success: true,
        message: 'Siswa berhasil dihapus'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal menghapus siswa'
      };
    }
  }
};

// Grade management service
export const gradeService = {
  async getGrades(studentId?: string, classId?: string): Promise<ApiResponse<Grade[]>> {
    try {
      const filter: any = {};
      if (studentId) filter.student_id = { _eq: studentId };
      if (classId) filter.class_id = { _eq: classId };
      
      const grades = await directus.request(readItems('grades', {
        fields: ['*', 'student.first_name', 'student.last_name', 'subject.name'],
        filter,
        sort: ['-date']
      }));
      
      return {
        success: true,
        data: grades as Grade[]
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal mengambil data nilai'
      };
    }
  },

  async createGrade(gradeData: Partial<Grade>): Promise<ApiResponse<Grade>> {
    try {
      const newGrade = await directus.request(createItem('grades', gradeData));
      
      // Award XP based on grade
      if (gradeData.student_id && gradeData.score && gradeData.max_score) {
        const percentage = (gradeData.score / gradeData.max_score) * 100;
        let xpReward = 0;
        
        if (percentage >= 90) xpReward = 50;
        else if (percentage >= 80) xpReward = 40;
        else if (percentage >= 70) xpReward = 30;
        else if (percentage >= 60) xpReward = 20;
        
        if (xpReward > 0) {
          await xpService.addXP(gradeData.student_id, xpReward, 'Nilai bagus!');
        }
      }
      
      return {
        success: true,
        data: newGrade as Grade,
        message: 'Nilai berhasil ditambahkan'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal menambahkan nilai'
      };
    }
  }
};

// XP and Gamification service
export const xpService = {
  async addXP(studentId: string, xpAmount: number, reason: string): Promise<ApiResponse<StudentXP>> {
    try {
      // Get current XP data
      const currentXP = await directus.request(readItems('student_xp', {
        filter: { student_id: { _eq: studentId } },
        limit: 1
      }));

      if (currentXP.length === 0) {
        // Create new XP record
        const newXP = await directus.request(createItem('student_xp', {
          student_id: studentId,
          total_xp: xpAmount,
          level: 1,
          level_name: 'Pemula'
        }));

        return {
          success: true,
          data: newXP as StudentXP,
          message: `+${xpAmount} XP! ${reason}`
        };
      } else {
        // Update existing XP
        const current = currentXP[0] as StudentXP;
        const newTotalXP = current.total_xp + xpAmount;
        const newLevel = this.calculateLevel(newTotalXP);
        const levelName = this.getLevelName(newLevel);

        const updatedXP = await directus.request(updateItem('student_xp', current.id, {
          total_xp: newTotalXP,
          level: newLevel,
          level_name: levelName
        }));

        // Check if level up occurred
        if (newLevel > current.level) {
          // Award level up badge or notification
          await this.handleLevelUp(studentId, newLevel);
        }

        return {
          success: true,
          data: updatedXP as StudentXP,
          message: `+${xpAmount} XP! ${reason}${newLevel > current.level ? ` LEVEL UP! Sekarang Level ${newLevel}` : ''}`
        };
      }
    } catch (error) {
      return {
        success: false,
        error: 'Gagal menambahkan XP'
      };
    }
  },

  calculateLevel(totalXP: number): number {
    if (totalXP >= 1000) return 5; // Master
    if (totalXP >= 600) return 4;  // Ahli
    if (totalXP >= 300) return 3;  // Cendekiawan
    if (totalXP >= 100) return 2;  // Pelajar
    return 1; // Pemula
  },

  getLevelName(level: number): 'Pemula' | 'Pelajar' | 'Cendekiawan' | 'Ahli' | 'Master' {
    const levelNames = {
      1: 'Pemula' as const,
      2: 'Pelajar' as const,
      3: 'Cendekiawan' as const,
      4: 'Ahli' as const,
      5: 'Master' as const
    };
    return levelNames[level as keyof typeof levelNames] || 'Pemula';
  },

  async handleLevelUp(studentId: string, newLevel: number): Promise<void> {
    // Create activity log for level up
    await directus.request(createItem('activities', {
      type: 'xp',
      title: 'Level Up!',
      description: `Naik ke Level ${newLevel}`,
      user_id: studentId
    }));
  },

  async getLeaderboard(classId?: string): Promise<ApiResponse<Leaderboard[]>> {
    try {
      const leaderboard = await directus.request(readItems('student_xp', {
        fields: ['*', 'student.first_name', 'student.last_name', 'student.class.name'],
        filter: classId ? { 'student.class_id': { _eq: classId } } : {},
        sort: ['-total_xp'],
        limit: 50
      }));

      // Transform to leaderboard format with ranking
      const leaderboardData = leaderboard.map((item, index) => ({
        id: item.id,
        student_id: item.student_id,
        class_id: item.student?.class?.id || '',
        rank: index + 1,
        total_score: item.total_xp,
        total_xp: item.total_xp,
        updated_at: item.updated_at,
        student: item.student
      })) as Leaderboard[];

      return {
        success: true,
        data: leaderboardData
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal mengambil data leaderboard'
      };
    }
  }
};

// Question bank service
export const questionService = {
  async getQuestions(subjectId?: string, difficulty?: string): Promise<ApiResponse<Question[]>> {
    try {
      const filter: any = {};
      if (subjectId) filter.subject_id = { _eq: subjectId };
      if (difficulty) filter.difficulty = { _eq: difficulty };

      const questions = await directus.request(readItems('questions', {
        fields: ['*', 'subject.name', 'options.*'],
        filter,
        sort: ['-created_at']
      }));

      return {
        success: true,
        data: questions as Question[]
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal mengambil data soal'
      };
    }
  },

  async createQuestion(questionData: Partial<Question>): Promise<ApiResponse<Question>> {
    try {
      const newQuestion = await directus.request(createItem('questions', questionData));
      return {
        success: true,
        data: newQuestion as Question,
        message: 'Soal berhasil ditambahkan'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal menambahkan soal'
      };
    }
  },

  // Fisher-Yates Shuffle algorithm for randomizing questions
  shuffleQuestions(questions: Question[]): Question[] {
    const shuffled = [...questions];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }
};

// Presensi (Attendance) service
export const attendanceService = {
  async getAttendance(classId?: string, date?: string): Promise<ApiResponse<any[]>> {
    try {
      // TODO: Implement when attendance collection is created
      // For now, return dummy data
      const dummyAttendance = [
        {
          id: '1',
          student_id: 'siswa-1',
          class_id: 'class-7a',
          date: new Date().toISOString().split('T')[0],
          status: 'present',
          time_in: '07:30',
          notes: '',
          student: { first_name: 'Ahmad', last_name: 'Pratama' }
        },
        {
          id: '2',
          student_id: 'siswa-2',
          class_id: 'class-7a',
          date: new Date().toISOString().split('T')[0],
          status: 'late',
          time_in: '08:15',
          notes: 'Terlambat karena macet',
          student: { first_name: 'Siti', last_name: 'Nurhaliza' }
        }
      ];

      return {
        success: true,
        data: dummyAttendance
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal mengambil data presensi'
      };
    }
  },

  async markAttendance(attendanceData: any): Promise<ApiResponse<any>> {
    try {
      // TODO: Implement when attendance collection is created
      return {
        success: true,
        data: { ...attendanceData, id: Date.now().toString() },
        message: 'Presensi berhasil disimpan'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal menyimpan presensi'
      };
    }
  },

  async getAttendanceReport(classId: string, startDate: string, endDate: string): Promise<ApiResponse<any>> {
    try {
      // TODO: Implement attendance analytics
      const dummyReport = {
        totalDays: 20,
        totalStudents: 30,
        averageAttendance: 92.5,
        presentCount: 555,
        absentCount: 25,
        lateCount: 20
      };

      return {
        success: true,
        data: dummyReport
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal mengambil laporan presensi'
      };
    }
  }
};

// School service
export const schoolService = {
  async getSchools(): Promise<ApiResponse<any[]>> {
    try {
      const schools = await directus.request(readItems('schools', {
        sort: ['name']
      }));

      return {
        success: true,
        data: schools
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal mengambil data sekolah'
      };
    }
  }
};

// Subject service
export const subjectService = {
  async getSubjects(): Promise<ApiResponse<any[]>> {
    try {
      const subjects = await directus.request(readItems('subjects', {
        sort: ['name']
      }));

      return {
        success: true,
        data: subjects
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal mengambil data mata pelajaran'
      };
    }
  }
};

// Dashboard service
export const dashboardService = {
  async getDashboardStats(): Promise<ApiResponse<any>> {
    try {
      // TEMPORARY: Return dummy data until collections are ready
      const dummyStats = {
        totalClasses: 3,
        totalStudents: 89,
        totalQuestions: 156,
        averageGrade: 85,
        activeExercises: 12,
        topStudents: [
          { id: '1', student: { first_name: 'Ahmad', last_name: 'Pratama' }, total_xp: 850, level: 4 },
          { id: '2', student: { first_name: 'Siti', last_name: 'Nurhaliza' }, total_xp: 720, level: 3 },
          { id: '3', student: { first_name: 'Budi', last_name: 'Santoso' }, total_xp: 680, level: 3 }
        ],
        recentActivities: [
          { id: '1', type: 'grade', title: 'Nilai Matematika ditambahkan', user: { first_name: 'Guru', last_name: 'Matematika' }, created_at: new Date().toISOString() },
          { id: '2', type: 'xp', title: 'XP diberikan untuk tugas', user: { first_name: 'Ahmad', last_name: 'Pratama' }, created_at: new Date().toISOString() }
        ]
      };

      return {
        success: true,
        data: dummyStats
      };

      // TODO: Replace with real data when collections are ready
      /*
      const [classes, students, questions] = await Promise.all([
        directus.request(readItems('classes', { aggregate: { count: '*' } })),
        directus.request(readItems('students', { aggregate: { count: '*' } })),
        directus.request(readItems('questions', { aggregate: { count: '*' } }))
      ]);

      const grades = await directus.request(readItems('grades', {
        aggregate: { avg: 'score' }
      }));

      const topStudents = await directus.request(readItems('student_xp', {
        fields: ['*', 'student.first_name', 'student.last_name'],
        sort: ['-total_xp'],
        limit: 5
      }));

      const recentActivities = await directus.request(readItems('activities', {
        fields: ['*', 'user.first_name', 'user.last_name'],
        sort: ['-created_at'],
        limit: 10
      }));

      return {
        success: true,
        data: {
          totalClasses: classes[0]?.count || 0,
          totalStudents: students[0]?.count || 0,
          totalQuestions: questions[0]?.count || 0,
          averageGrade: Math.round(grades[0]?.avg || 0),
          activeExercises: 0,
          topStudents: topStudents,
          recentActivities: recentActivities
        }
      };
      */
    } catch (error) {
      return {
        success: false,
        error: 'Gagal mengambil data dashboard'
      };
    }
  }
};
