// Updated: Directus API integration for Guru Digital Pelangi
import { createDirectus, rest, authentication, readItems, createItem, updateItem, deleteItem, readItem } from '@directus/sdk';
import type { DirectusSchema, User, Class, Student, Grade, Question, StudentXP, Badge, Leaderboard, ApiResponse } from '../types/api';

// Directus client configuration
const directusUrl = import.meta.env.VITE_DIRECTUS_URL || 'http://localhost:8055';

export const directus = createDirectus<DirectusSchema>(directusUrl)
  .with(rest())
  .with(authentication());

// Authentication service
export const authService = {
  async login(email: string, password: string): Promise<ApiResponse<User>> {
    try {
      await directus.login(email, password);
      const user = await directus.request(readItems('users', {
        filter: { email: { _eq: email } },
        limit: 1
      }));
      
      return {
        success: true,
        data: user[0] as User,
        message: '<PERSON>gin berhasil'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Email atau password salah'
      };
    }
  },

  async logout(): Promise<void> {
    await directus.logout();
  },

  async getCurrentUser(): Promise<User | null> {
    try {
      const user = await directus.request(readItems('users', {
        filter: { id: { _eq: '$CURRENT_USER' } },
        limit: 1
      }));
      return user[0] as User || null;
    } catch (error) {
      return null;
    }
  }
};

// Class management service
export const classService = {
  async getClasses(): Promise<ApiResponse<Class[]>> {
    try {
      const classes = await directus.request(readItems('classes', {
        fields: ['*', 'teacher.first_name', 'teacher.last_name', 'students.id'],
        sort: ['name']
      }));
      
      return {
        success: true,
        data: classes as Class[]
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal mengambil data kelas'
      };
    }
  },

  async createClass(classData: Partial<Class>): Promise<ApiResponse<Class>> {
    try {
      const newClass = await directus.request(createItem('classes', classData));
      return {
        success: true,
        data: newClass as Class,
        message: 'Kelas berhasil dibuat'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal membuat kelas'
      };
    }
  },

  async updateClass(id: string, classData: Partial<Class>): Promise<ApiResponse<Class>> {
    try {
      const updatedClass = await directus.request(updateItem('classes', id, classData));
      return {
        success: true,
        data: updatedClass as Class,
        message: 'Kelas berhasil diupdate'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal mengupdate kelas'
      };
    }
  },

  async deleteClass(id: string): Promise<ApiResponse<void>> {
    try {
      await directus.request(deleteItem('classes', id));
      return {
        success: true,
        message: 'Kelas berhasil dihapus'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal menghapus kelas'
      };
    }
  }
};

// Student management service
export const studentService = {
  async getStudents(classId?: string): Promise<ApiResponse<Student[]>> {
    try {
      const filter = classId ? { class_id: { _eq: classId } } : {};
      const students = await directus.request(readItems('students', {
        fields: ['*', 'class.name', 'xp_data.total_xp', 'xp_data.level'],
        filter,
        sort: ['first_name']
      }));
      
      return {
        success: true,
        data: students as Student[]
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal mengambil data siswa'
      };
    }
  },

  async createStudent(studentData: Partial<Student>): Promise<ApiResponse<Student>> {
    try {
      const newStudent = await directus.request(createItem('students', studentData));
      
      // Create initial XP data for the student
      await directus.request(createItem('student_xp', {
        student_id: newStudent.id,
        total_xp: 0,
        level: 1,
        level_name: 'Pemula'
      }));
      
      return {
        success: true,
        data: newStudent as Student,
        message: 'Siswa berhasil ditambahkan'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal menambahkan siswa'
      };
    }
  },

  async updateStudent(id: string, studentData: Partial<Student>): Promise<ApiResponse<Student>> {
    try {
      const updatedStudent = await directus.request(updateItem('students', id, studentData));
      return {
        success: true,
        data: updatedStudent as Student,
        message: 'Data siswa berhasil diupdate'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal mengupdate data siswa'
      };
    }
  },

  async deleteStudent(id: string): Promise<ApiResponse<void>> {
    try {
      await directus.request(deleteItem('students', id));
      return {
        success: true,
        message: 'Siswa berhasil dihapus'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal menghapus siswa'
      };
    }
  }
};

// Grade management service
export const gradeService = {
  async getGrades(studentId?: string, classId?: string): Promise<ApiResponse<Grade[]>> {
    try {
      const filter: any = {};
      if (studentId) filter.student_id = { _eq: studentId };
      if (classId) filter.class_id = { _eq: classId };
      
      const grades = await directus.request(readItems('grades', {
        fields: ['*', 'student.first_name', 'student.last_name', 'subject.name'],
        filter,
        sort: ['-date']
      }));
      
      return {
        success: true,
        data: grades as Grade[]
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal mengambil data nilai'
      };
    }
  },

  async createGrade(gradeData: Partial<Grade>): Promise<ApiResponse<Grade>> {
    try {
      const newGrade = await directus.request(createItem('grades', gradeData));
      
      // Award XP based on grade
      if (gradeData.student_id && gradeData.score && gradeData.max_score) {
        const percentage = (gradeData.score / gradeData.max_score) * 100;
        let xpReward = 0;
        
        if (percentage >= 90) xpReward = 50;
        else if (percentage >= 80) xpReward = 40;
        else if (percentage >= 70) xpReward = 30;
        else if (percentage >= 60) xpReward = 20;
        
        if (xpReward > 0) {
          await xpService.addXP(gradeData.student_id, xpReward, 'Nilai bagus!');
        }
      }
      
      return {
        success: true,
        data: newGrade as Grade,
        message: 'Nilai berhasil ditambahkan'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal menambahkan nilai'
      };
    }
  }
};

// XP and Gamification service
export const xpService = {
  async addXP(studentId: string, xpAmount: number, reason: string): Promise<ApiResponse<StudentXP>> {
    try {
      // Get current XP data
      const currentXP = await directus.request(readItems('student_xp', {
        filter: { student_id: { _eq: studentId } },
        limit: 1
      }));

      if (currentXP.length === 0) {
        // Create new XP record
        const newXP = await directus.request(createItem('student_xp', {
          student_id: studentId,
          total_xp: xpAmount,
          level: 1,
          level_name: 'Pemula'
        }));

        return {
          success: true,
          data: newXP as StudentXP,
          message: `+${xpAmount} XP! ${reason}`
        };
      } else {
        // Update existing XP
        const current = currentXP[0] as StudentXP;
        const newTotalXP = current.total_xp + xpAmount;
        const newLevel = this.calculateLevel(newTotalXP);
        const levelName = this.getLevelName(newLevel);

        const updatedXP = await directus.request(updateItem('student_xp', current.id, {
          total_xp: newTotalXP,
          level: newLevel,
          level_name: levelName
        }));

        // Check if level up occurred
        if (newLevel > current.level) {
          // Award level up badge or notification
          await this.handleLevelUp(studentId, newLevel);
        }

        return {
          success: true,
          data: updatedXP as StudentXP,
          message: `+${xpAmount} XP! ${reason}${newLevel > current.level ? ` LEVEL UP! Sekarang Level ${newLevel}` : ''}`
        };
      }
    } catch (error) {
      return {
        success: false,
        error: 'Gagal menambahkan XP'
      };
    }
  },

  calculateLevel(totalXP: number): number {
    if (totalXP >= 1000) return 5; // Master
    if (totalXP >= 600) return 4;  // Ahli
    if (totalXP >= 300) return 3;  // Cendekiawan
    if (totalXP >= 100) return 2;  // Pelajar
    return 1; // Pemula
  },

  getLevelName(level: number): 'Pemula' | 'Pelajar' | 'Cendekiawan' | 'Ahli' | 'Master' {
    const levelNames = {
      1: 'Pemula' as const,
      2: 'Pelajar' as const,
      3: 'Cendekiawan' as const,
      4: 'Ahli' as const,
      5: 'Master' as const
    };
    return levelNames[level as keyof typeof levelNames] || 'Pemula';
  },

  async handleLevelUp(studentId: string, newLevel: number): Promise<void> {
    // Create activity log for level up
    await directus.request(createItem('activities', {
      type: 'xp',
      title: 'Level Up!',
      description: `Naik ke Level ${newLevel}`,
      user_id: studentId
    }));
  },

  async getLeaderboard(classId?: string): Promise<ApiResponse<Leaderboard[]>> {
    try {
      const leaderboard = await directus.request(readItems('student_xp', {
        fields: ['*', 'student.first_name', 'student.last_name', 'student.class.name'],
        filter: classId ? { 'student.class_id': { _eq: classId } } : {},
        sort: ['-total_xp'],
        limit: 50
      }));

      // Transform to leaderboard format with ranking
      const leaderboardData = leaderboard.map((item, index) => ({
        id: item.id,
        student_id: item.student_id,
        class_id: item.student?.class?.id || '',
        rank: index + 1,
        total_score: item.total_xp,
        total_xp: item.total_xp,
        updated_at: item.updated_at,
        student: item.student
      })) as Leaderboard[];

      return {
        success: true,
        data: leaderboardData
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal mengambil data leaderboard'
      };
    }
  }
};

// Question bank service
export const questionService = {
  async getQuestions(subjectId?: string, difficulty?: string): Promise<ApiResponse<Question[]>> {
    try {
      const filter: any = {};
      if (subjectId) filter.subject_id = { _eq: subjectId };
      if (difficulty) filter.difficulty = { _eq: difficulty };

      const questions = await directus.request(readItems('questions', {
        fields: ['*', 'subject.name', 'options.*'],
        filter,
        sort: ['-created_at']
      }));

      return {
        success: true,
        data: questions as Question[]
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal mengambil data soal'
      };
    }
  },

  async createQuestion(questionData: Partial<Question>): Promise<ApiResponse<Question>> {
    try {
      const newQuestion = await directus.request(createItem('questions', questionData));
      return {
        success: true,
        data: newQuestion as Question,
        message: 'Soal berhasil ditambahkan'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal menambahkan soal'
      };
    }
  },

  // Fisher-Yates Shuffle algorithm for randomizing questions
  shuffleQuestions(questions: Question[]): Question[] {
    const shuffled = [...questions];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }
};

// Dashboard service
export const dashboardService = {
  async getDashboardStats(): Promise<ApiResponse<any>> {
    try {
      // Get total counts
      const [classes, students, questions] = await Promise.all([
        directus.request(readItems('classes', { aggregate: { count: '*' } })),
        directus.request(readItems('students', { aggregate: { count: '*' } })),
        directus.request(readItems('questions', { aggregate: { count: '*' } }))
      ]);

      // Get average grade
      const grades = await directus.request(readItems('grades', {
        aggregate: { avg: 'score' }
      }));

      // Get top students
      const topStudents = await directus.request(readItems('student_xp', {
        fields: ['*', 'student.first_name', 'student.last_name'],
        sort: ['-total_xp'],
        limit: 5
      }));

      // Get recent activities
      const recentActivities = await directus.request(readItems('activities', {
        fields: ['*', 'user.first_name', 'user.last_name'],
        sort: ['-created_at'],
        limit: 10
      }));

      return {
        success: true,
        data: {
          totalClasses: classes[0]?.count || 0,
          totalStudents: students[0]?.count || 0,
          totalQuestions: questions[0]?.count || 0,
          averageGrade: Math.round(grades[0]?.avg || 0),
          activeExercises: 0, // TODO: implement exercises
          topStudents: topStudents,
          recentActivities: recentActivities
        }
      };
    } catch (error) {
      return {
        success: false,
        error: 'Gagal mengambil data dashboard'
      };
    }
  }
};
