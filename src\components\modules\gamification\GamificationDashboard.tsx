// Gamification Dashboard Component for Guru Digital Pelangi
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Input,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Chip,
  Progress,
  Avatar,
  Divider,
  Pagination
} from '@heroui/react';
import {
  Trophy,
  Star,
  TrendingUp,
  Award,
  Target,
  Users,
  Crown,
  Medal,
  Zap,
  Flame,
  Search
} from 'lucide-react';
import { gamificationService, studentService } from '../../../services/expressApi';

interface Student {
  id: string;
  fullName: string;
  studentId: string;
  class?: {
    id: string;
    name: string;
  };
  studentXp?: {
    totalXp: number;
    level: number;
    levelName: string;
    attendanceStreak: number;
    assignmentStreak: number;
  };
}

const GamificationDashboard = () => {
  const [students, setStudents] = useState<Student[]>([]);
  const [filteredStudents, setFilteredStudents] = useState<Student[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const studentsPerPage = 10;

  useEffect(() => {
    loadStudents();
  }, []);

  useEffect(() => {
    filterStudents();
  }, [students, searchTerm]);

  const loadStudents = async () => {
    setIsLoading(true);
    try {
      const response = await studentService.getStudents();
      if (response.success && response.data) {
        // Sort students by XP (highest first)
        const sortedStudents = response.data.sort((a: Student, b: Student) => {
          const aXp = a.studentXp?.totalXp || 0;
          const bXp = b.studentXp?.totalXp || 0;
          return bXp - aXp;
        });
        setStudents(sortedStudents);
      }
    } catch (error) {
      console.error('Error loading students:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filterStudents = () => {
    if (!searchTerm) {
      setFilteredStudents(students);
    } else {
      const filtered = students.filter((student) =>
        student.fullName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        student.class?.name?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredStudents(filtered);
    }
    setCurrentPage(1); // Reset to first page when filtering
  };

  // Calculate pagination
  const totalPages = Math.ceil(filteredStudents.length / studentsPerPage);
  const startIndex = (currentPage - 1) * studentsPerPage;
  const endIndex = startIndex + studentsPerPage;
  const currentStudents = filteredStudents.slice(startIndex, endIndex);

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="w-5 h-5 text-yellow-500" />;
      case 2:
        return <Medal className="w-5 h-5 text-gray-400" />;
      case 3:
        return <Award className="w-5 h-5 text-amber-600" />;
      default:
        return <span className="text-sm font-bold text-gray-600">#{rank}</span>;
    }
  };

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1:
        return 'warning'; // Gold
      case 2:
        return 'default'; // Silver
      case 3:
        return 'secondary'; // Bronze
      default:
        return 'primary';
    }
  };

  const getLevelColor = (level: number) => {
    if (level >= 6) return 'danger'; // Legend
    if (level >= 5) return 'secondary'; // Master
    if (level >= 4) return 'warning'; // Juara
    if (level >= 3) return 'success'; // Berprestasi
    if (level >= 2) return 'primary'; // Pelajar
    return 'default'; // Pemula
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card>
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-gradient-to-br from-purple-100 to-pink-100 rounded-full">
                  <Trophy className="w-6 h-6 text-purple-600" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold">Dashboard Gamifikasi</h1>
                  <p className="text-gray-600">Leaderboard dan pencapaian siswa</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Chip color="success" variant="flat" startContent={<Star className="w-4 h-4" />}>
                  Sistem Aktif
                </Chip>
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Search and Filter */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Filter Siswa</h3>
          </CardHeader>
          <CardBody>
            <div className="max-w-md">
              <Input
                placeholder="Cari nama siswa atau kelas..."
                startContent={<Search className="w-4 h-4 text-gray-400" />}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Leaderboard */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between w-full">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Trophy className="w-5 h-5 text-yellow-500" />
                Leaderboard Siswa
              </h3>
              <Chip color="primary" variant="flat" startContent={<Users className="w-4 h-4" />}>
                {filteredStudents.length} Siswa
              </Chip>
            </div>
          </CardHeader>
          <CardBody>
              {/* Top 3 Podium */}
              {filteredStudents.length >= 3 && (
                <div className="mb-6">
                  <div className="flex items-end justify-center gap-4 mb-4">
                    {/* 2nd Place */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                      className="text-center"
                    >
                      <div className="bg-gradient-to-br from-gray-100 to-gray-200 p-4 rounded-lg h-24 flex flex-col justify-end">
                        <Avatar
                          name={getInitials(filteredStudents[1].fullName || 'S')}
                          className="mx-auto mb-2"
                          color="default"
                        />
                        <div className="text-xs font-medium">{filteredStudents[1].fullName}</div>
                        <div className="text-xs text-gray-600">{filteredStudents[1].studentXp?.totalXp || 0} XP</div>
                      </div>
                      <div className="mt-2 text-lg font-bold text-gray-500">#2</div>
                    </motion.div>

                    {/* 1st Place */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                      className="text-center"
                    >
                      <div className="bg-gradient-to-br from-yellow-100 to-yellow-200 p-4 rounded-lg h-32 flex flex-col justify-end">
                        <Crown className="w-6 h-6 text-yellow-600 mx-auto mb-1" />
                        <Avatar
                          name={getInitials(filteredStudents[0].fullName || 'S')}
                          className="mx-auto mb-2"
                          color="warning"
                        />
                        <div className="text-sm font-bold">{filteredStudents[0].fullName}</div>
                        <div className="text-xs text-yellow-700">{filteredStudents[0].studentXp?.totalXp || 0} XP</div>
                      </div>
                      <div className="mt-2 text-xl font-bold text-yellow-600">#1</div>
                    </motion.div>

                    {/* 3rd Place */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4 }}
                      className="text-center"
                    >
                      <div className="bg-gradient-to-br from-amber-100 to-amber-200 p-4 rounded-lg h-20 flex flex-col justify-end">
                        <Avatar
                          name={getInitials(filteredStudents[2].fullName || 'S')}
                          className="mx-auto mb-2"
                          color="secondary"
                        />
                        <div className="text-xs font-medium">{filteredStudents[2].fullName}</div>
                        <div className="text-xs text-amber-700">{filteredStudents[2].studentXp?.totalXp || 0} XP</div>
                      </div>
                      <div className="mt-2 text-lg font-bold text-amber-600">#3</div>
                    </motion.div>
                  </div>
                  <Divider className="my-6" />
                </div>
              )}

              {/* Full Leaderboard Table */}
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              ) : filteredStudents.length === 0 ? (
                <div className="text-center py-12">
                  <Trophy className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-600 mb-2">Belum Ada Siswa</h3>
                  <p className="text-gray-500">Belum ada data siswa untuk ditampilkan di leaderboard.</p>
                </div>
              ) : (
                <>
                  <Table aria-label="Leaderboard table">
                    <TableHeader>
                      <TableColumn>RANK</TableColumn>
                      <TableColumn>SISWA</TableColumn>
                      <TableColumn>KELAS</TableColumn>
                      <TableColumn>LEVEL</TableColumn>
                      <TableColumn>TOTAL XP</TableColumn>
                    </TableHeader>
                    <TableBody>
                      {currentStudents.map((student, index) => {
                        const rank = startIndex + index + 1;
                        return (
                          <TableRow key={student.id}>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                {getRankIcon(rank)}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-3">
                                <Avatar
                                  name={getInitials(student.fullName || 'S')}
                                  size="sm"
                                  color={getRankColor(rank)}
                                />
                                <div>
                                  <div className="font-medium">{student.fullName}</div>
                                  <div className="text-xs text-gray-500">{student.studentId}</div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Chip color="primary" variant="flat" size="sm">
                                {student.class?.name || 'Belum ada kelas'}
                              </Chip>
                            </TableCell>
                            <TableCell>
                              <Chip
                                color={getLevelColor(student.studentXp?.level || 1)}
                                variant="flat"
                                size="sm"
                              >
                                Lv.{student.studentXp?.level || 1} {student.studentXp?.levelName || 'Pemula'}
                              </Chip>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Zap className="w-4 h-4 text-yellow-500" />
                                <span className="font-medium">{(student.studentXp?.totalXp || 0).toLocaleString()}</span>
                              </div>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>

                  {/* Pagination */}
                  {totalPages > 1 && (
                    <div className="flex justify-center mt-6">
                      <Pagination
                        loop
                        showControls
                        color="success"
                        page={currentPage}
                        total={totalPages}
                        onChange={setCurrentPage}
                      />
                    </div>
                  )}
                </>
              )}
            </CardBody>
          </Card>
        </motion.div>
    </div>
  );
};

export default GamificationDashboard;
