// Gamification Dashboard Component for Guru Digital Pelangi
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Select,
  SelectItem,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Chip,
  Progress,
  Avatar,
  Divider
} from '@heroui/react';
import { 
  Trophy, 
  Star, 
  TrendingUp, 
  Award, 
  Target,
  Users,
  Crown,
  Medal,
  Zap,
  Flame
} from 'lucide-react';
import { gamificationService, classService } from '../../../services/expressApi';

interface LeaderboardEntry {
  rank: number;
  student: {
    id: string;
    fullName: string;
    studentId: string;
  };
  totalXp: number;
  level: number;
  levelName: string;
  attendanceStreak: number;
  assignmentStreak: number;
}

interface Class {
  id: string;
  name: string;
  subject?: {
    id: string;
    name: string;
    code: string;
  };
}

const GamificationDashboard = () => {
  const [selectedClass, setSelectedClass] = useState<string>('');
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [classes, setClasses] = useState<Class[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [stats, setStats] = useState({
    totalStudents: 0,
    totalBadges: 0,
    averageGrade: 0,
    topStudent: null as LeaderboardEntry | null
  });

  useEffect(() => {
    loadClasses();
  }, []);

  useEffect(() => {
    if (selectedClass) {
      loadLeaderboard();
    }
  }, [selectedClass]);

  const loadClasses = async () => {
    const response = await classService.getClasses();
    if (response.success && response.data) {
      setClasses(response.data);
    }
  };

  const loadLeaderboard = async () => {
    if (!selectedClass) return;

    setIsLoading(true);
    const response = await gamificationService.getClassLeaderboard(selectedClass, { limit: 20 });
    if (response.success && response.data) {
      setLeaderboard(response.data);

      // Update stats
      const data = response.data;
      setStats({
        totalStudents: data.length,
        totalBadges: 12, // Mock data - should come from API
        averageGrade: data.length > 0 ? Math.round(data.reduce((sum, entry) => sum + entry.totalXp, 0) / data.length) : 0,
        topStudent: data.length > 0 ? data[0] : null
      });
    }
    setIsLoading(false);
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="w-5 h-5 text-yellow-500" />;
      case 2:
        return <Medal className="w-5 h-5 text-gray-400" />;
      case 3:
        return <Award className="w-5 h-5 text-amber-600" />;
      default:
        return <span className="text-sm font-bold text-gray-600">#{rank}</span>;
    }
  };

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1:
        return 'warning'; // Gold
      case 2:
        return 'default'; // Silver
      case 3:
        return 'secondary'; // Bronze
      default:
        return 'primary';
    }
  };

  const getLevelColor = (level: number) => {
    if (level >= 6) return 'danger'; // Legend
    if (level >= 5) return 'secondary'; // Master
    if (level >= 4) return 'warning'; // Juara
    if (level >= 3) return 'success'; // Berprestasi
    if (level >= 2) return 'primary'; // Pelajar
    return 'default'; // Pemula
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card>
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-gradient-to-br from-purple-100 to-pink-100 rounded-full">
                  <Trophy className="w-6 h-6 text-purple-600" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold">Dashboard Gamifikasi</h1>
                  <p className="text-gray-600">Leaderboard dan pencapaian siswa</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Chip color="success" variant="flat" startContent={<Star className="w-4 h-4" />}>
                  Sistem Aktif
                </Chip>
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Class Selection */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Pilih Kelas</h3>
          </CardHeader>
          <CardBody>
            <div className="max-w-md">
              <Select
                label="Kelas"
                placeholder="Pilih kelas untuk melihat leaderboard"
                selectedKeys={selectedClass ? [selectedClass] : []}
                onSelectionChange={(keys) => {
                  const classId = Array.from(keys)[0] as string;
                  setSelectedClass(classId);
                }}
              >
                {classes.map((cls) => (
                  <SelectItem key={cls.id} textValue={`${cls.name} - ${(cls as any).subject?.name || 'Mata Pelajaran'}`}>
                    {cls.name} - {(cls as any).subject?.name || 'Mata Pelajaran'}
                  </SelectItem>
                ))}
              </Select>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Statistics Cards */}
      {selectedClass && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.15 }}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Total Siswa */}
            <Card>
              <CardBody className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total Siswa</p>
                    <p className="text-2xl font-bold text-blue-600">{stats.totalStudents}</p>
                  </div>
                  <div className="p-3 bg-blue-100 rounded-full">
                    <Users className="w-6 h-6 text-blue-600" />
                  </div>
                </div>
              </CardBody>
            </Card>

            {/* Total Badge */}
            <Card>
              <CardBody className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total Badge</p>
                    <p className="text-2xl font-bold text-purple-600">{stats.totalBadges}</p>
                  </div>
                  <div className="p-3 bg-purple-100 rounded-full">
                    <Award className="w-6 h-6 text-purple-600" />
                  </div>
                </div>
              </CardBody>
            </Card>

            {/* Rata-rata XP */}
            <Card>
              <CardBody className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Rata-rata XP</p>
                    <p className="text-2xl font-bold text-green-600">{stats.averageGrade}</p>
                  </div>
                  <div className="p-3 bg-green-100 rounded-full">
                    <Zap className="w-6 h-6 text-green-600" />
                  </div>
                </div>
              </CardBody>
            </Card>

            {/* Siswa Terbaik */}
            <Card>
              <CardBody className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Siswa Terbaik</p>
                    <p className="text-lg font-bold text-yellow-600">
                      {stats.topStudent ? stats.topStudent.student.fullName : '-'}
                    </p>
                    {stats.topStudent && (
                      <p className="text-xs text-gray-500">{stats.topStudent.totalXp} XP</p>
                    )}
                  </div>
                  <div className="p-3 bg-yellow-100 rounded-full">
                    <Crown className="w-6 h-6 text-yellow-600" />
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>
        </motion.div>
      )}

      {/* Leaderboard */}
      {selectedClass && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between w-full">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <Trophy className="w-5 h-5 text-yellow-500" />
                  Leaderboard Kelas
                </h3>
                <Chip color="primary" variant="flat" startContent={<Users className="w-4 h-4" />}>
                  {leaderboard.length} Siswa
                </Chip>
              </div>
            </CardHeader>
            <CardBody>
              {/* Top 3 Podium */}
              {leaderboard.length >= 3 && (
                <div className="mb-6">
                  <div className="flex items-end justify-center gap-4 mb-4">
                    {/* 2nd Place */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                      className="text-center"
                    >
                      <div className="bg-gradient-to-br from-gray-100 to-gray-200 p-4 rounded-lg h-24 flex flex-col justify-end">
                        <Avatar
                          name={getInitials(leaderboard[1].student.fullName)}
                          className="mx-auto mb-2"
                          color="default"
                        />
                        <div className="text-xs font-medium">{leaderboard[1].student.fullName}</div>
                        <div className="text-xs text-gray-600">{leaderboard[1].totalXp} XP</div>
                      </div>
                      <div className="mt-2 text-lg font-bold text-gray-500">#2</div>
                    </motion.div>

                    {/* 1st Place */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                      className="text-center"
                    >
                      <div className="bg-gradient-to-br from-yellow-100 to-yellow-200 p-4 rounded-lg h-32 flex flex-col justify-end">
                        <Crown className="w-6 h-6 text-yellow-600 mx-auto mb-1" />
                        <Avatar
                          name={getInitials(leaderboard[0].student.fullName)}
                          className="mx-auto mb-2"
                          color="warning"
                        />
                        <div className="text-sm font-bold">{leaderboard[0].student.fullName}</div>
                        <div className="text-xs text-yellow-700">{leaderboard[0].totalXp} XP</div>
                      </div>
                      <div className="mt-2 text-xl font-bold text-yellow-600">#1</div>
                    </motion.div>

                    {/* 3rd Place */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4 }}
                      className="text-center"
                    >
                      <div className="bg-gradient-to-br from-amber-100 to-amber-200 p-4 rounded-lg h-20 flex flex-col justify-end">
                        <Avatar
                          name={getInitials(leaderboard[2].student.fullName)}
                          className="mx-auto mb-2"
                          color="secondary"
                        />
                        <div className="text-xs font-medium">{leaderboard[2].student.fullName}</div>
                        <div className="text-xs text-amber-700">{leaderboard[2].totalXp} XP</div>
                      </div>
                      <div className="mt-2 text-lg font-bold text-amber-600">#3</div>
                    </motion.div>
                  </div>
                  <Divider className="my-6" />
                </div>
              )}

              {/* Full Leaderboard Table */}
              <Table aria-label="Leaderboard table">
                <TableHeader>
                  <TableColumn>RANK</TableColumn>
                  <TableColumn>SISWA</TableColumn>
                  <TableColumn>LEVEL</TableColumn>
                  <TableColumn>TOTAL XP</TableColumn>
                  <TableColumn>STREAK</TableColumn>
                </TableHeader>
                <TableBody>
                  {leaderboard.map((entry) => (
                    <TableRow key={entry.student.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getRankIcon(entry.rank)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar
                            name={getInitials(entry.student.fullName)}
                            size="sm"
                            color={getRankColor(entry.rank)}
                          />
                          <div>
                            <div className="font-medium">{entry.student.fullName}</div>
                            <div className="text-xs text-gray-500">{entry.student.studentId}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Chip
                          color={getLevelColor(entry.level)}
                          variant="flat"
                          size="sm"
                        >
                          Lv.{entry.level} {entry.levelName}
                        </Chip>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Zap className="w-4 h-4 text-yellow-500" />
                          <span className="font-medium">{entry.totalXp.toLocaleString()}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center gap-1 text-xs">
                            <Flame className="w-3 h-3 text-orange-500" />
                            <span>Hadir: {entry.attendanceStreak}</span>
                          </div>
                          <div className="flex items-center gap-1 text-xs">
                            <Target className="w-3 h-3 text-blue-500" />
                            <span>Tugas: {entry.assignmentStreak}</span>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardBody>
          </Card>
        </motion.div>
      )}
    </div>
  );
};

export default GamificationDashboard;
