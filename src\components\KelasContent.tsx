import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Card, 
  CardBody, 
  CardHeader, 
  Button, 
  Input, 
  Chip,
  Avatar
} from '@heroui/react';

const KelasContent = () => {
  const [searchTerm, setSearchTerm] = useState('');
  
  const kelasData = [
    {
      id: '9C',
      subject: 'IPA',
      schedule: '08:25 - 09:45',
      students: 31,
      assignments: 0,
      avgGrade: '-',
      color: 'secondary',
      status: 'active',
      lastUpdated: 'Dibuat 6 Jun 2025'
    },
    {
      id: 'XI PA1',
      subject: 'Fisika',
      schedule: '07:00-09:00',
      students: 28,
      assignments: 3,
      avgGrade: '8.2',
      color: 'primary',
      status: 'active',
      lastUpdated: 'Dibuat 9 Jun 2025'
    },
    {
      id: '7A',
      subject: 'IPA',
      schedule: '08:25 - 09:45',
      students: 30,
      assignments: 2,
      avgGrade: '7.8',
      color: 'success',
      status: 'active',
      lastUpdated: 'Dibuat 10 Jun 2025'
    },
    {
      id: '9E',
      subject: 'IPA',
      schedule: '<PERSON><PERSON>',
      students: 0,
      assignments: 0,
      avgGrade: '-',
      color: 'warning',
      status: 'draft',
      lastUpdated: 'Draft'
    }
  ];

  const filteredKelas = kelasData.filter(kelas => 
    kelas.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    kelas.subject.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="p-8">
      {/* Header */}
      <div className="flex justify-between items-start mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Kelas</h1>
          <p className="text-gray-600">Kelola semua kelas Anda dengan mudah</p>
        </div>
        <div className="flex space-x-3">
          <Button 
            variant="bordered" 
            startContent={<span>🔄</span>}
          >
            Refresh
          </Button>
          <Button 
            color="primary" 
            startContent={<span>+</span>}
          >
            Buat Kelas
          </Button>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="mb-8">
        <div className="flex items-center space-x-4">
          <Input
            type="text"
            placeholder="Cari kelas, mata pelajaran, atau deskripsi..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            startContent={<span className="text-gray-400">🔍</span>}
            className="flex-1"
          />
          <Chip color="primary" variant="flat">
            {filteredKelas.length} Kelas
          </Chip>
        </div>
      </div>

      {/* Classes Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredKelas.map((kelas, index) => (
          <motion.div
            key={kelas.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            whileHover={{ scale: 1.02 }}
          >
            <Card className="hover:shadow-lg transition-all duration-300 bg-white border border-gray-200">
              {/* Header Card - Blue Gradient */}
              <CardHeader className="bg-gradient-to-r from-cyan-400 to-blue-500 text-white p-6 relative overflow-hidden">
                <div className="flex justify-between items-start w-full">
                  <div>
                    <h3 className="text-3xl font-bold mb-1">{kelas.id}</h3>
                    <p className="text-white/90 text-sm font-medium">{kelas.subject}</p>
                  </div>
                  <div className="flex gap-1">
                    <div className="w-4 h-4 bg-white/30 rounded"></div>
                    <div className="w-4 h-4 bg-white/30 rounded"></div>
                  </div>
                </div>
              </CardHeader>

              {/* Content */}
              <CardBody className="p-6 bg-gray-50">
                {/* Selasa Label */}
                <div className="mb-6">
                  <h4 className="text-lg font-semibold text-gray-800">Selasa</h4>
                </div>

                {/* Stats Grid */}
                <div className="grid grid-cols-3 gap-6 mb-8">
                  <div className="text-center">
                    <div className="w-12 h-12 mx-auto mb-3 flex items-center justify-center">
                      <svg className="w-8 h-8 text-gray-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                      </svg>
                    </div>
                    <p className="text-2xl font-bold text-gray-900 mb-1">{kelas.students}</p>
                    <p className="text-gray-600 text-sm">Siswa</p>
                  </div>
                  <div className="text-center">
                    <div className="w-12 h-12 mx-auto mb-3 flex items-center justify-center">
                      <svg className="w-8 h-8 text-gray-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                      </svg>
                    </div>
                    <p className="text-2xl font-bold text-gray-900 mb-1">{kelas.assignments}</p>
                    <p className="text-gray-600 text-sm">Tugas</p>
                  </div>
                  <div className="text-center">
                    <div className="w-12 h-12 mx-auto mb-3 flex items-center justify-center">
                      <svg className="w-8 h-8 text-gray-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"/>
                      </svg>
                    </div>
                    <p className="text-2xl font-bold text-gray-900 mb-1">{kelas.avgGrade}</p>
                    <p className="text-gray-600 text-sm">Rata-rata</p>
                  </div>
                </div>

                {/* Footer */}
                <div className="flex justify-between items-center">
                  <p className="text-gray-500 text-sm">{kelas.lastUpdated}</p>
                  <div className="flex gap-2">
                    <Button
                      isIconOnly
                      size="sm"
                      variant="light"
                      className="text-gray-500 hover:text-blue-600"
                    >
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M3.9 12c0-1.71 1.39-3.1 3.1-3.1h4V7H6.99C4.24 7 2 9.24 2 12s2.24 5 4.99 5H11v-1.9H7c-1.71 0-3.1-1.39-3.1-3.1zM8 13h8v-2H8v2zm5-6h4.01C19.76 7 22 9.24 22 12s-2.24 5-4.99 5H13v1.9h4c1.71 0 3.1-1.39 3.1-3.1 0-1.71-1.39-3.1-3.1-3.1H13V7z"/>
                      </svg>
                    </Button>
                    <Button
                      isIconOnly
                      size="sm"
                      variant="light"
                      className="text-gray-500 hover:text-green-600"
                    >
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                      </svg>
                    </Button>
                    <Button
                      isIconOnly
                      size="sm"
                      variant="light"
                      className="text-gray-500 hover:text-red-600"
                    >
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                      </svg>
                    </Button>
                  </div>
                </div>
              </CardBody>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default KelasContent;
