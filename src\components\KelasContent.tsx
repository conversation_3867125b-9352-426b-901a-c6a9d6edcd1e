import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Card, 
  CardBody, 
  CardHeader, 
  Button, 
  Input, 
  Chip,
  Avatar
} from '@heroui/react';

const KelasContent = () => {
  const [searchTerm, setSearchTerm] = useState('');
  
  const kelasData = [
    {
      id: '9C',
      subject: 'IPA',
      schedule: '08:25 - 09:45',
      students: 31,
      assignments: 0,
      avgGrade: '-',
      color: 'secondary',
      status: 'active',
      lastUpdated: 'Dibuat 6 Jun 2025'
    },
    {
      id: 'XI PA1',
      subject: 'Fisika',
      schedule: '07:00-09:00',
      students: 28,
      assignments: 3,
      avgGrade: '8.2',
      color: 'primary',
      status: 'active',
      lastUpdated: 'Dibuat 9 Jun 2025'
    },
    {
      id: '7A',
      subject: 'IPA',
      schedule: '08:25 - 09:45',
      students: 30,
      assignments: 2,
      avgGrade: '7.8',
      color: 'success',
      status: 'active',
      lastUpdated: 'Dibuat 10 Jun 2025'
    },
    {
      id: '9E',
      subject: 'IPA',
      schedule: '<PERSON><PERSON>',
      students: 0,
      assignments: 0,
      avgGrade: '-',
      color: 'warning',
      status: 'draft',
      lastUpdated: 'Draft'
    }
  ];

  const filteredKelas = kelasData.filter(kelas => 
    kelas.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    kelas.subject.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="p-8">
      {/* Header */}
      <div className="flex justify-between items-start mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Kelas</h1>
          <p className="text-gray-600">Kelola semua kelas Anda dengan mudah</p>
        </div>
        <div className="flex space-x-3">
          <Button 
            variant="bordered" 
            startContent={<span>🔄</span>}
          >
            Refresh
          </Button>
          <Button 
            color="primary" 
            startContent={<span>+</span>}
          >
            Buat Kelas
          </Button>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="mb-8">
        <div className="flex items-center space-x-4">
          <Input
            type="text"
            placeholder="Cari kelas, mata pelajaran, atau deskripsi..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            startContent={<span className="text-gray-400">🔍</span>}
            className="flex-1"
          />
          <Chip color="primary" variant="flat">
            {filteredKelas.length} Kelas
          </Chip>
        </div>
      </div>

      {/* Classes Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredKelas.map((kelas, index) => (
          <motion.div
            key={kelas.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            whileHover={{ scale: 1.02 }}
          >
            <Card className="hover:shadow-xl transition-all duration-300">
              {/* Header Card */}
              <CardHeader className={`bg-gradient-to-br from-${kelas.color}-500 to-${kelas.color}-700 text-white relative overflow-hidden`}>
                <div className="relative z-10 w-full">
                  <div className="flex justify-between items-start mb-4">
                    <h3 className="text-2xl font-bold">{kelas.id}</h3>
                    <Chip 
                      color="default" 
                      variant="flat" 
                      className="bg-white/20 text-white"
                    >
                      {kelas.subject}
                    </Chip>
                  </div>
                  <p className="text-white/80">{kelas.schedule}</p>
                </div>
                <div className="absolute -bottom-4 -right-4 w-24 h-24 bg-white/10 rounded-full"></div>
                <div className="absolute -top-2 -left-2 w-16 h-16 bg-white/10 rounded-full"></div>
              </CardHeader>

              {/* Stats */}
              <CardBody className="p-6">
                <div className="grid grid-cols-3 gap-4 mb-6">
                  <div className="text-center">
                    <Avatar 
                      name="👥"
                      className="bg-blue-100 text-blue-600 mx-auto mb-2"
                      size="sm"
                    />
                    <p className="text-2xl font-bold text-gray-900">{kelas.students}</p>
                    <p className="text-gray-600 text-xs">Siswa</p>
                  </div>
                  <div className="text-center">
                    <Avatar 
                      name="📝"
                      className="bg-green-100 text-green-600 mx-auto mb-2"
                      size="sm"
                    />
                    <p className="text-2xl font-bold text-gray-900">{kelas.assignments}</p>
                    <p className="text-gray-600 text-xs">Tugas</p>
                  </div>
                  <div className="text-center">
                    <Avatar 
                      name="📊"
                      className="bg-purple-100 text-purple-600 mx-auto mb-2"
                      size="sm"
                    />
                    <p className="text-2xl font-bold text-gray-900">{kelas.avgGrade}</p>
                    <p className="text-gray-600 text-xs">Rata-rata</p>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex justify-between items-center mb-4">
                  <div className="flex space-x-2">
                    <Button isIconOnly size="sm" variant="light">👁️</Button>
                    <Button isIconOnly size="sm" variant="light">✏️</Button>
                    <Button isIconOnly size="sm" variant="light">🗑️</Button>
                  </div>
                  <Chip 
                    color={kelas.status === 'active' ? 'success' : 'warning'}
                    variant="flat"
                    size="sm"
                  >
                    Lihat
                  </Chip>
                </div>

                {/* Last Updated */}
                <div className="pt-4 border-t border-gray-100">
                  <p className="text-gray-500 text-xs flex items-center">
                    <span className="mr-2">📅</span>
                    {kelas.lastUpdated}
                  </p>
                </div>
              </CardBody>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default KelasContent;
