import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Card, 
  CardBody, 
  CardHeader, 
  Button, 
  Input, 
  Chip,
  Avatar
} from '@heroui/react';

const KelasContent = () => {
  const [searchTerm, setSearchTerm] = useState('');
  
  const kelasData = [
    {
      id: '9C',
      subject: 'IPA',
      schedule: '08:25 - 09:45',
      students: 31,
      assignments: 0,
      avgGrade: '-',
      color: 'secondary',
      status: 'active',
      lastUpdated: 'Dibuat 6 Jun 2025'
    },
    {
      id: 'XI PA1',
      subject: 'Fisika',
      schedule: '07:00-09:00',
      students: 28,
      assignments: 3,
      avgGrade: '8.2',
      color: 'primary',
      status: 'active',
      lastUpdated: 'Dibuat 9 Jun 2025'
    },
    {
      id: '7A',
      subject: 'IPA',
      schedule: '08:25 - 09:45',
      students: 30,
      assignments: 2,
      avgGrade: '7.8',
      color: 'success',
      status: 'active',
      lastUpdated: 'Dibuat 10 Jun 2025'
    },
    {
      id: '9E',
      subject: 'IPA',
      schedule: '<PERSON><PERSON>',
      students: 0,
      assignments: 0,
      avgGrade: '-',
      color: 'warning',
      status: 'draft',
      lastUpdated: 'Draft'
    }
  ];

  const filteredKelas = kelasData.filter(kelas => 
    kelas.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    kelas.subject.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="p-8">
      {/* Header */}
      <div className="flex justify-between items-start mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Kelas</h1>
          <p className="text-gray-600">Kelola semua kelas Anda dengan mudah</p>
        </div>
        <div className="flex space-x-3">
          <Button 
            variant="bordered" 
            startContent={<span>🔄</span>}
          >
            Refresh
          </Button>
          <Button 
            color="primary" 
            startContent={<span>+</span>}
          >
            Buat Kelas
          </Button>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="mb-8">
        <div className="flex items-center space-x-4">
          <Input
            type="text"
            placeholder="Cari kelas, mata pelajaran, atau deskripsi..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            startContent={<span className="text-gray-400">🔍</span>}
            className="flex-1"
          />
          <Chip color="primary" variant="flat">
            {filteredKelas.length} Kelas
          </Chip>
        </div>
      </div>

      {/* Classes Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredKelas.map((kelas, index) => (
          <motion.div
            key={kelas.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            whileHover={{ scale: 1.02 }}
          >
            <Card className="hover:shadow-xl transition-all duration-300 border-0 bg-white">
              {/* Header Card */}
              <CardHeader className={`bg-gradient-to-br from-${kelas.color}-400 to-${kelas.color}-600 text-white relative overflow-hidden p-6`}>
                <div className="relative z-10 w-full">
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm">
                        <span className="text-xl font-bold">{kelas.id.charAt(0)}</span>
                      </div>
                      <div>
                        <h3 className="text-xl font-bold">{kelas.id}</h3>
                        <p className="text-white/80 text-sm">{kelas.subject}</p>
                      </div>
                    </div>
                    <Chip
                      color="default"
                      variant="flat"
                      className="bg-white/20 text-white border-white/30"
                      size="sm"
                    >
                      Aktif
                    </Chip>
                  </div>
                  <div className="flex items-center gap-2 text-white/90">
                    <span className="text-sm">🕐</span>
                    <span className="text-sm">{kelas.schedule}</span>
                  </div>
                </div>
                <div className="absolute -bottom-6 -right-6 w-20 h-20 bg-white/10 rounded-full"></div>
                <div className="absolute -top-4 -left-4 w-12 h-12 bg-white/10 rounded-full"></div>
              </CardHeader>

              {/* Stats */}
              <CardBody className="p-6">
                <div className="grid grid-cols-3 gap-3 mb-6">
                  <div className="text-center p-3 bg-blue-50 rounded-xl">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                      <span className="text-blue-600 text-sm">👥</span>
                    </div>
                    <p className="text-xl font-bold text-gray-900">{kelas.students}</p>
                    <p className="text-gray-600 text-xs">Siswa</p>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-xl">
                    <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                      <span className="text-green-600 text-sm">📝</span>
                    </div>
                    <p className="text-xl font-bold text-gray-900">{kelas.assignments}</p>
                    <p className="text-gray-600 text-xs">Tugas</p>
                  </div>
                  <div className="text-center p-3 bg-purple-50 rounded-xl">
                    <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                      <span className="text-purple-600 text-sm">📊</span>
                    </div>
                    <p className="text-xl font-bold text-gray-900">{kelas.avgGrade}</p>
                    <p className="text-gray-600 text-xs">Rata-rata</p>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex justify-between items-center pt-4 border-t border-gray-100">
                  <div className="flex space-x-1">
                    <Button
                      isIconOnly
                      size="sm"
                      variant="light"
                      className="text-gray-600 hover:text-blue-600 hover:bg-blue-50"
                    >
                      👁️
                    </Button>
                    <Button
                      isIconOnly
                      size="sm"
                      variant="light"
                      className="text-gray-600 hover:text-green-600 hover:bg-green-50"
                    >
                      ✏️
                    </Button>
                    <Button
                      isIconOnly
                      size="sm"
                      variant="light"
                      className="text-gray-600 hover:text-red-600 hover:bg-red-50"
                    >
                      🗑️
                    </Button>
                  </div>
                  <div className="text-right">
                    <p className="text-gray-500 text-xs">{kelas.lastUpdated}</p>
                  </div>
                </div>
              </CardBody>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default KelasContent;
