// Level Management Routes - Guru Digital Pelangi
import express from 'express';
import { PrismaClient } from '@prisma/client';
import { authenticateToken, requireRole } from '../middleware/auth.js';

const router = express.Router();
const prisma = new PrismaClient();

// Get all levels
router.get('/', authenticateToken, async (req, res) => {
  try {
    // For now, return predefined levels since they're system-wide
    const levels = [
      { id: '1', level: 1, name: '<PERSON><PERSON><PERSON>', xpRequired: 0, benefits: 'Akses dasar ke semua fitur' },
      { id: '2', level: 2, name: '<PERSON><PERSON><PERSON><PERSON>', xpRequired: 100, benefits: 'Aks<PERSON> ke quiz tambahan' },
      { id: '3', level: 3, name: '<PERSON><PERSON>', xpRequired: 300, benefits: 'Akses ke materi advanced' },
      { id: '4', level: 4, name: '<PERSON><PERSON>', xpRequired: 600, benefits: '<PERSON>ks<PERSON> ke proyek khusus' },
      { id: '5', level: 5, name: 'Master', xpRequired: 1000, benefits: 'Aks<PERSON> ke semua fitur premium' },
      { id: '6', level: 6, name: '<PERSON><PERSON>', xpRequired: 1500, benefits: 'Akses mentor untuk siswa lain' },
      { id: '7', level: 7, name: 'Legend', xpRequired: 2200, benefits: 'Akses ke kompetisi eksklusif' },
      { id: '8', level: 8, name: 'Mythic', xpRequired: 3000, benefits: 'Akses ke program beasiswa' },
      { id: '9', level: 9, name: 'Divine', xpRequired: 4000, benefits: 'Akses ke universitas partner' },
      { id: '10', level: 10, name: 'Immortal', xpRequired: 5500, benefits: 'Status legend sekolah' }
    ];

    res.json({
      success: true,
      data: levels,
      message: 'Levels retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching levels:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// Update level (Admin only)
router.put('/:id', authenticateToken, requireRole(['ADMIN']), async (req, res) => {
  try {
    const { id } = req.params;
    const { name, xpRequired, benefits } = req.body;

    // Validation
    if (!name || xpRequired === undefined || !benefits) {
      return res.status(400).json({
        success: false,
        error: 'Name, xpRequired, and benefits are required'
      });
    }

    if (xpRequired < 0) {
      return res.status(400).json({
        success: false,
        error: 'XP required cannot be negative'
      });
    }

    // For now, just return success since levels are predefined
    // In a real implementation, you might store custom level configurations in database
    const updatedLevel = {
      id,
      level: parseInt(id),
      name,
      xpRequired: parseInt(xpRequired),
      benefits
    };

    res.json({
      success: true,
      data: updatedLevel,
      message: 'Level updated successfully'
    });
  } catch (error) {
    console.error('Error updating level:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// Delete level (Admin only) - Not allowed for system levels
router.delete('/:id', authenticateToken, requireRole(['ADMIN']), async (req, res) => {
  try {
    const { id } = req.params;

    // System levels cannot be deleted
    if (parseInt(id) >= 1 && parseInt(id) <= 10) {
      return res.status(400).json({
        success: false,
        error: 'System levels cannot be deleted'
      });
    }

    res.json({
      success: true,
      message: 'Level deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting level:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

export default router;
