// Simple MySQL Connection Test
import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

async function testMySQLConnection() {
  try {
    console.log('🔍 Testing MySQL connection...');
    
    const connection = await mysql.createConnection({
      host: 'alpha_guruku',
      port: 3306,
      user: 'blinkihc',
      password: '39255471f76e90383731',
      database: 'guru_digital_pelangi'
    });
    
    console.log('✅ MySQL connection successful!');
    
    // Test query
    const [rows] = await connection.execute('SELECT 1 as test');
    console.log('✅ Query test successful:', rows);
    
    // Check database
    const [databases] = await connection.execute('SHOW DATABASES');
    console.log('📋 Available databases:', databases.map(db => db.Database));
    
    await connection.end();
    console.log('🎉 Connection test completed!');
    
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    console.error('💡 Check:');
    console.error('   - VPS MySQL is running');
    console.error('   - Firewall allows port 3306');
    console.error('   - Credentials are correct');
  }
}

testMySQLConnection();
