// Class Controller
// Handles CRUD operations for classes
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Get all classes
 * GET /api/classes
 */
export const getClasses = async (req, res) => {
  try {
    const { page = 1, limit = 10, search, gradeLevel, schoolId, teacherId, subjectId } = req.query;
    const { user } = req;
    const skip = (page - 1) * limit;

    // Build where clause
    const where = {};

    if (search) {
      where.OR = [
        { name: { contains: search } },
        { gradeLevel: { contains: search } },
        { subject: { name: { contains: search } } }
      ];
    }

    if (gradeLevel) {
      where.gradeLevel = gradeLevel;
    }

    if (schoolId) {
      where.schoolId = schoolId;
    }

    if (subjectId) {
      where.subjectId = subjectId;
    }

    // Role-based filtering
    if (user.role === 'GURU') {
      // Guru hanya bisa lihat kelas yang mereka ajar
      where.classTeachers = {
        some: {
          teacherId: user.id
        }
      };
    } else if (teacherId) {
      // Admin bisa filter berdasarkan guru
      where.classTeachers = {
        some: {
          teacherId: teacherId
        }
      };
    }

    // Get classes with pagination
    const [classes, total] = await Promise.all([
      prisma.class.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          school: {
            select: {
              id: true,
              name: true
            }
          },
          subject: {
            select: {
              id: true,
              name: true,
              code: true
            }
          },
          classTeachers: {
            include: {
              teacher: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true
                }
              }
            }
          },
          _count: {
            select: { students: true }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.class.count({ where })
    ]);

    // Transform data untuk menambahkan teachers info
    const classesWithCount = classes.map(cls => ({
      ...cls,
      studentCount: cls._count.students,
      teachers: cls.classTeachers.map(ct => ({
        id: ct.teacher.id,
        fullName: `${ct.teacher.firstName} ${ct.teacher.lastName}`,
        email: ct.teacher.email
      }))
    }));

    res.json({
      success: true,
      data: {
        classes: classesWithCount,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get classes error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat mengambil data kelas'
    });
  }
};

/**
 * Get class by ID
 * GET /api/classes/:id
 */
export const getClassById = async (req, res) => {
  try {
    const { id } = req.params;
    const { user } = req;

    const classData = await prisma.class.findUnique({
      where: { id },
      include: {
        school: {
          select: {
            id: true,
            name: true,
            address: true
          }
        },
        subject: {
          select: {
            id: true,
            name: true,
            code: true,
            description: true
          }
        },
        classTeachers: {
          include: {
            teacher: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                nip: true
              }
            }
          }
        },
        students: {
          select: {
            id: true,
            studentId: true,
            fullName: true,
            email: true,
            status: true
          },
          orderBy: { fullName: 'asc' }
        },
        _count: {
          select: { students: true }
        }
      }
    });

    if (!classData) {
      return res.status(404).json({
        success: false,
        message: 'Kelas tidak ditemukan'
      });
    }

    // Check permission for GURU role
    if (user.role === 'GURU') {
      const isTeacherOfClass = classData.classTeachers.some(ct => ct.teacherId === user.id);
      if (!isTeacherOfClass) {
        return res.status(403).json({
          success: false,
          message: 'Anda tidak memiliki akses ke kelas ini'
        });
      }
    }

    // Transform teachers data
    const teachers = classData.classTeachers.map(ct => ({
      id: ct.teacher.id,
      fullName: `${ct.teacher.firstName} ${ct.teacher.lastName}`,
      email: ct.teacher.email,
      nip: ct.teacher.nip
    }));

    res.json({
      success: true,
      data: {
        class: {
          ...classData,
          teachers,
          studentCount: classData._count.students
        }
      }
    });

  } catch (error) {
    console.error('Get class by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat mengambil data kelas'
    });
  }
};

/**
 * Create new class
 * POST /api/classes
 */
export const createClass = async (req, res) => {
  try {
    const { name, subjectId, teacherIds, startTime, endTime, description } = req.body;
    const { user } = req;

    // Validation
    if (!name || !subjectId) {
      return res.status(400).json({
        success: false,
        message: 'Nama kelas dan mata pelajaran wajib diisi'
      });
    }

    // Check if subject exists
    const subject = await prisma.subject.findUnique({
      where: { id: subjectId }
    });

    if (!subject) {
      return res.status(400).json({
        success: false,
        message: 'Mata pelajaran tidak ditemukan'
      });
    }

    // Check for duplicate class name + subject
    const existingClass = await prisma.class.findFirst({
      where: {
        name,
        subjectId
      }
    });

    if (existingClass) {
      return res.status(400).json({
        success: false,
        message: 'Kelas dengan nama dan mata pelajaran yang sama sudah ada'
      });
    }

    // Determine teachers to assign
    let finalTeacherIds = [];
    if (user.role === 'ADMIN') {
      // Admin bisa assign guru lain
      finalTeacherIds = teacherIds || [];
    } else {
      // Guru otomatis assign ke diri sendiri
      finalTeacherIds = [user.id];
    }

    // Validate teacher IDs
    if (finalTeacherIds.length > 0) {
      const validTeachers = await prisma.user.findMany({
        where: {
          id: { in: finalTeacherIds },
          role: 'GURU'
        }
      });

      if (validTeachers.length !== finalTeacherIds.length) {
        return res.status(400).json({
          success: false,
          message: 'Beberapa guru yang dipilih tidak valid'
        });
      }
    }

    // Create class with transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create class
      const classData = await tx.class.create({
        data: {
          name,
          subjectId,
          startTime: startTime || null,
          endTime: endTime || null,
          description,
          gradeLevel: '10',
          academicYear: '2024/2025'
        }
      });

      // Create class-teacher relationships
      if (finalTeacherIds.length > 0) {
        await tx.classTeacher.createMany({
          data: finalTeacherIds.map(teacherId => ({
            classId: classData.id,
            teacherId
          }))
        });
      }

      return classData;
    });

    // Fetch complete class data
    const completeClassData = await prisma.class.findUnique({
      where: { id: result.id },
      include: {
        subject: true,
        classTeachers: {
          include: {
            teacher: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true
              }
            }
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      message: 'Kelas berhasil dibuat',
      data: { class: completeClassData }
    });

  } catch (error) {
    console.error('Create class error:', error);
    res.status(400).json({
      success: false,
      error: 'Validation error',
      details: error.message
    });
  }
};

/**
 * Update class
 * PUT /api/classes/:id
 */
export const updateClass = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, subjectId, teacherIds, startTime, endTime, description } = req.body;
    const { user } = req;

    // Check if class exists
    const existingClass = await prisma.class.findUnique({
      where: { id },
      include: {
        classTeachers: true
      }
    });

    if (!existingClass) {
      return res.status(404).json({
        success: false,
        message: 'Kelas tidak ditemukan'
      });
    }

    // Check permission for GURU role
    if (user.role === 'GURU') {
      const isTeacherOfClass = existingClass.classTeachers.some(ct => ct.teacherId === user.id);
      if (!isTeacherOfClass) {
        return res.status(403).json({
          success: false,
          message: 'Anda tidak memiliki akses untuk mengedit kelas ini'
        });
      }
    }

    // Validate subject if provided
    if (subjectId) {
      const subject = await prisma.subject.findUnique({
        where: { id: subjectId }
      });

      if (!subject) {
        return res.status(400).json({
          success: false,
          message: 'Mata pelajaran tidak ditemukan'
        });
      }

      // Check for duplicate class name + subject (excluding current class)
      if (name || subjectId) {
        const duplicateClass = await prisma.class.findFirst({
          where: {
            name: name || existingClass.name,
            subjectId: subjectId || existingClass.subjectId,
            NOT: { id }
          }
        });

        if (duplicateClass) {
          return res.status(400).json({
            success: false,
            message: 'Kelas dengan nama dan mata pelajaran yang sama sudah ada'
          });
        }
      }
    }

    // Update with transaction
    const result = await prisma.$transaction(async (tx) => {
      // Update class basic info
      const classData = await tx.class.update({
        where: { id },
        data: {
          ...(name && { name }),
          ...(subjectId && { subjectId }),
          ...(startTime !== undefined && { startTime }),
          ...(endTime !== undefined && { endTime }),
          ...(description !== undefined && { description })
        }
      });

      // Update teacher assignments (only for ADMIN)
      if (user.role === 'ADMIN' && teacherIds !== undefined) {
        // Delete existing assignments
        await tx.classTeacher.deleteMany({
          where: { classId: id }
        });

        // Create new assignments
        if (teacherIds.length > 0) {
          // Validate teacher IDs
          const validTeachers = await tx.user.findMany({
            where: {
              id: { in: teacherIds },
              role: 'GURU'
            }
          });

          if (validTeachers.length !== teacherIds.length) {
            throw new Error('Beberapa guru yang dipilih tidak valid');
          }

          await tx.classTeacher.createMany({
            data: teacherIds.map(teacherId => ({
              classId: id,
              teacherId
            }))
          });
        }
      }

      return classData;
    });

    // Fetch complete updated class data
    const completeClassData = await prisma.class.findUnique({
      where: { id },
      include: {
        subject: true,
        classTeachers: {
          include: {
            teacher: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true
              }
            }
          }
        }
      }
    });

    res.json({
      success: true,
      message: 'Kelas berhasil diupdate',
      data: { class: completeClassData }
    });

  } catch (error) {
    console.error('Update class error:', error);
    res.status(400).json({
      success: false,
      error: 'Validation error',
      details: error.message
    });
  }
};

/**
 * Delete class
 * DELETE /api/classes/:id
 */
export const deleteClass = async (req, res) => {
  try {
    const { id } = req.params;
    console.log('🔧 Delete Class - ID:', id);

    // Check if class exists and count students
    const existingClass = await prisma.class.findUnique({
      where: { id },
      include: {
        students: true,
        _count: {
          select: { students: true }
        }
      }
    });

    console.log('🔧 Delete Class - Existing class:', existingClass ? 'Found' : 'Not found');
    console.log('🔧 Delete Class - Students array length:', existingClass?.students?.length || 0);
    console.log('🔧 Delete Class - Students count:', existingClass?._count?.students || 0);

    if (!existingClass) {
      return res.status(404).json({
        success: false,
        message: 'Kelas tidak ditemukan'
      });
    }

    // Use count instead of array length for more accurate check
    const studentCount = existingClass._count.students;

    if (studentCount > 0) {
      console.log('🔧 Delete Class - Blocked: Has', studentCount, 'students');
      return res.status(400).json({
        success: false,
        message: `Tidak dapat menghapus kelas yang masih memiliki ${studentCount} siswa`
      });
    }

    console.log('🔧 Delete Class - Proceeding with deletion');
    await prisma.class.delete({
      where: { id }
    });

    console.log('🔧 Delete Class - Successfully deleted');
    res.json({
      success: true,
      message: 'Kelas berhasil dihapus'
    });

  } catch (error) {
    console.error('Delete class error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat menghapus kelas'
    });
  }
};

/**
 * Get students in class
 * GET /api/classes/:id/students
 */
export const getClassStudents = async (req, res) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 20 } = req.query;
    const skip = (page - 1) * limit;

    // Check if class exists
    const classExists = await prisma.class.findUnique({
      where: { id }
    });

    if (!classExists) {
      return res.status(404).json({
        success: false,
        message: 'Kelas tidak ditemukan'
      });
    }

    const [students, total] = await Promise.all([
      prisma.student.findMany({
        where: { classId: id },
        skip: parseInt(skip),
        take: parseInt(limit),
        select: {
          id: true,
          studentId: true,
          firstName: true,
          lastName: true,
          email: true,
          gender: true,
          status: true,
          createdAt: true
        },
        orderBy: { firstName: 'asc' }
      }),
      prisma.student.count({
        where: { classId: id }
      })
    ]);

    res.json({
      success: true,
      data: {
        students,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get class students error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat mengambil data siswa'
    });
  }
};

/**
 * Add student to class
 * POST /api/classes/:id/students
 */
export const addStudentToClass = async (req, res) => {
  try {
    const { id } = req.params;
    const { studentId } = req.body;

    // Check if class exists
    const classExists = await prisma.class.findUnique({
      where: { id }
    });

    if (!classExists) {
      return res.status(404).json({
        success: false,
        message: 'Kelas tidak ditemukan'
      });
    }

    // Update student's class
    const student = await prisma.student.update({
      where: { id: studentId },
      data: { classId: id },
      select: {
        id: true,
        studentId: true,
        firstName: true,
        lastName: true,
        email: true
      }
    });

    res.json({
      success: true,
      message: 'Siswa berhasil ditambahkan ke kelas',
      data: { student }
    });

  } catch (error) {
    console.error('Add student to class error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat menambahkan siswa ke kelas'
    });
  }
};

/**
 * Remove student from class
 * DELETE /api/classes/:id/students/:studentId
 */
export const removeStudentFromClass = async (req, res) => {
  try {
    const { id, studentId } = req.params;

    // Update student's class to null
    const student = await prisma.student.update({
      where: { id: studentId },
      data: { classId: null },
      select: {
        id: true,
        studentId: true,
        firstName: true,
        lastName: true
      }
    });

    res.json({
      success: true,
      message: 'Siswa berhasil dihapus dari kelas',
      data: { student }
    });

  } catch (error) {
    console.error('Remove student from class error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat menghapus siswa dari kelas'
    });
  }
};
