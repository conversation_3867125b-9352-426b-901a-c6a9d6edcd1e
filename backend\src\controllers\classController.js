// Class Controller
// Handles CRUD operations for classes
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Get all classes
 * GET /api/classes
 */
export const getClasses = async (req, res) => {
  try {
    const { page = 1, limit = 10, search, gradeLevel, schoolId } = req.query;
    const skip = (page - 1) * limit;

    // Build where clause
    const where = {};
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { gradeLevel: { contains: search, mode: 'insensitive' } }
      ];
    }
    
    if (gradeLevel) {
      where.gradeLevel = gradeLevel;
    }
    
    if (schoolId) {
      where.schoolId = schoolId;
    }

    // Get classes with pagination
    const [classes, total] = await Promise.all([
      prisma.class.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          school: {
            select: {
              id: true,
              name: true
            }
          },
          teacher: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          },
          _count: {
            select: { students: true }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.class.count({ where })
    ]);

    // Update student count for each class
    const classesWithCount = classes.map(cls => ({
      ...cls,
      studentCount: cls._count.students
    }));

    res.json({
      success: true,
      data: {
        classes: classesWithCount,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get classes error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat mengambil data kelas'
    });
  }
};

/**
 * Get class by ID
 * GET /api/classes/:id
 */
export const getClassById = async (req, res) => {
  try {
    const { id } = req.params;

    const classData = await prisma.class.findUnique({
      where: { id },
      include: {
        school: {
          select: {
            id: true,
            name: true,
            address: true
          }
        },
        teacher: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            nip: true
          }
        },
        students: {
          select: {
            id: true,
            studentId: true,
            firstName: true,
            lastName: true,
            email: true,
            status: true
          },
          orderBy: { firstName: 'asc' }
        },
        _count: {
          select: { students: true }
        }
      }
    });

    if (!classData) {
      return res.status(404).json({
        success: false,
        message: 'Kelas tidak ditemukan'
      });
    }

    res.json({
      success: true,
      data: { 
        class: {
          ...classData,
          studentCount: classData._count.students
        }
      }
    });

  } catch (error) {
    console.error('Get class by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat mengambil data kelas'
    });
  }
};

/**
 * Create new class
 * POST /api/classes
 */
export const createClass = async (req, res) => {
  try {
    console.log('🔧 Create Class - Request body:', req.body);
    const { name, subject, start_time, end_time, description } = req.body;

    console.log('🔧 Create Class - Extracted data:', { name, subject, start_time, end_time, description });

    const classData = await prisma.class.create({
      data: {
        name,
        subject,
        startTime: start_time || null,
        endTime: end_time || null,
        description,
        // Set default values for backward compatibility
        gradeLevel: '10',
        academicYear: '2024/2025'
      },
      include: {
        school: {
          select: {
            id: true,
            name: true
          }
        },
        teacher: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      message: 'Kelas berhasil dibuat',
      data: { class: classData }
    });

  } catch (error) {
    console.error('Create class error:', error);
    res.status(400).json({
      success: false,
      error: 'Validation error',
      details: error.message
    });
  }
};

/**
 * Update class
 * PUT /api/classes/:id
 */
export const updateClass = async (req, res) => {
  try {
    const { id } = req.params;
    console.log('🔧 Update Class - ID:', id);
    console.log('🔧 Update Class - Request body:', req.body);
    const { name, subject, start_time, end_time, description } = req.body;

    console.log('🔧 Update Class - Extracted data:', { name, subject, start_time, end_time, description });

    // Check if class exists
    const existingClass = await prisma.class.findUnique({
      where: { id }
    });

    if (!existingClass) {
      return res.status(404).json({
        success: false,
        message: 'Kelas tidak ditemukan'
      });
    }

    const classData = await prisma.class.update({
      where: { id },
      data: {
        name,
        subject,
        startTime: start_time || null,
        endTime: end_time || null,
        description
      },
      include: {
        school: {
          select: {
            id: true,
            name: true
          }
        },
        teacher: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    });

    res.json({
      success: true,
      message: 'Kelas berhasil diupdate',
      data: { class: classData }
    });

  } catch (error) {
    console.error('Update class error:', error);
    res.status(400).json({
      success: false,
      error: 'Validation error',
      details: error.message
    });
  }
};

/**
 * Delete class
 * DELETE /api/classes/:id
 */
export const deleteClass = async (req, res) => {
  try {
    const { id } = req.params;
    console.log('🔧 Delete Class - ID:', id);

    // Check if class exists and count students
    const existingClass = await prisma.class.findUnique({
      where: { id },
      include: {
        students: true,
        _count: {
          select: { students: true }
        }
      }
    });

    console.log('🔧 Delete Class - Existing class:', existingClass ? 'Found' : 'Not found');
    console.log('🔧 Delete Class - Students array length:', existingClass?.students?.length || 0);
    console.log('🔧 Delete Class - Students count:', existingClass?._count?.students || 0);

    if (!existingClass) {
      return res.status(404).json({
        success: false,
        message: 'Kelas tidak ditemukan'
      });
    }

    // Use count instead of array length for more accurate check
    const studentCount = existingClass._count.students;

    if (studentCount > 0) {
      console.log('🔧 Delete Class - Blocked: Has', studentCount, 'students');
      return res.status(400).json({
        success: false,
        message: `Tidak dapat menghapus kelas yang masih memiliki ${studentCount} siswa`
      });
    }

    console.log('🔧 Delete Class - Proceeding with deletion');
    await prisma.class.delete({
      where: { id }
    });

    console.log('🔧 Delete Class - Successfully deleted');
    res.json({
      success: true,
      message: 'Kelas berhasil dihapus'
    });

  } catch (error) {
    console.error('Delete class error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat menghapus kelas'
    });
  }
};

/**
 * Get students in class
 * GET /api/classes/:id/students
 */
export const getClassStudents = async (req, res) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 20 } = req.query;
    const skip = (page - 1) * limit;

    // Check if class exists
    const classExists = await prisma.class.findUnique({
      where: { id }
    });

    if (!classExists) {
      return res.status(404).json({
        success: false,
        message: 'Kelas tidak ditemukan'
      });
    }

    const [students, total] = await Promise.all([
      prisma.student.findMany({
        where: { classId: id },
        skip: parseInt(skip),
        take: parseInt(limit),
        select: {
          id: true,
          studentId: true,
          firstName: true,
          lastName: true,
          email: true,
          gender: true,
          status: true,
          createdAt: true
        },
        orderBy: { firstName: 'asc' }
      }),
      prisma.student.count({
        where: { classId: id }
      })
    ]);

    res.json({
      success: true,
      data: {
        students,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get class students error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat mengambil data siswa'
    });
  }
};

/**
 * Add student to class
 * POST /api/classes/:id/students
 */
export const addStudentToClass = async (req, res) => {
  try {
    const { id } = req.params;
    const { studentId } = req.body;

    // Check if class exists
    const classExists = await prisma.class.findUnique({
      where: { id }
    });

    if (!classExists) {
      return res.status(404).json({
        success: false,
        message: 'Kelas tidak ditemukan'
      });
    }

    // Update student's class
    const student = await prisma.student.update({
      where: { id: studentId },
      data: { classId: id },
      select: {
        id: true,
        studentId: true,
        firstName: true,
        lastName: true,
        email: true
      }
    });

    res.json({
      success: true,
      message: 'Siswa berhasil ditambahkan ke kelas',
      data: { student }
    });

  } catch (error) {
    console.error('Add student to class error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat menambahkan siswa ke kelas'
    });
  }
};

/**
 * Remove student from class
 * DELETE /api/classes/:id/students/:studentId
 */
export const removeStudentFromClass = async (req, res) => {
  try {
    const { id, studentId } = req.params;

    // Update student's class to null
    const student = await prisma.student.update({
      where: { id: studentId },
      data: { classId: null },
      select: {
        id: true,
        studentId: true,
        firstName: true,
        lastName: true
      }
    });

    res.json({
      success: true,
      message: 'Siswa berhasil dihapus dari kelas',
      data: { student }
    });

  } catch (error) {
    console.error('Remove student from class error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat menghapus siswa dari kelas'
    });
  }
};
