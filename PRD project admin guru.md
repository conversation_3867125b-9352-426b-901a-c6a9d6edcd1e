<img src="https://r2cdn.perplexity.ai/pplx-full-logo-primary-dark%402x.png" class="logo" width="120"/>

# Panduan Praktik Terbaik untuk Pengembangan Website Administrasi Guru

## 🗂️ Ringkasan Tech Stack

Website administrasi guru ini akan dibangun menggunakan teknologi modern berikut untuk memastikan performa dan skalabilitas yang optimal [^1_1][^1_2]:

- **Frontend**: ReactJS dengan HeroUI dan Tailwind CSS
- **State Management**: Zustand
- **Backend**: Directus Headless CMS
- **Database**: PostgreSQL
- **Hosting**: Fleksibel (cloud/on-premise)

![Teacher Administration System Architecture - Comprehensive system design showing all layers from frontend to database](https://pplx-res.cloudinary.com/image/upload/v1749992572/pplx_code_interpreter/184aad95_yqju2a.jpg)

Teacher Administration System Architecture - Comprehensive system design showing all layers from frontend to database

## 🏗️ Arsitektur Sistem

Arsitektur website administrasi guru menggunakan pendekatan modern dengan pemisahan jelas antara frontend dan backend, memungkinkan skalabilitas dan pemeliharaan yang lebih baik [^1_3][^1_4]:

### Frontend (React + HeroUI + Tailwind)

Frontend dibangun dengan React yang mengikuti pola komponen, Tailwind CSS untuk styling, dan HeroUI untuk komponen UI yang konsisten dan mudah digunakan [^1_5][^1_6]:

- **React**: Library JavaScript untuk membangun antarmuka pengguna
- **HeroUI**: Menyediakan komponen yang dapat diakses dan disesuaikan
- **Tailwind CSS**: Framework utility-first CSS untuk styling yang efisien


### Backend (Directus + PostgreSQL)

Backend menggunakan Directus sebagai headless CMS dengan PostgreSQL sebagai database, memberikan fleksibilitas dan kemudahan dalam mengelola data [^1_4][^1_7]:

- **Directus**: Menyediakan RESTful API dan GraphQL endpoints secara otomatis
- **PostgreSQL**: Database relasional yang kuat untuk menyimpan data dengan integritas tinggi


### Komunikasi Frontend-Backend

Komunikasi antara frontend dan backend dilakukan melalui RESTful API yang disediakan oleh Directus, menggunakan format JSON standar untuk pertukaran data [^1_8][^1_9].

## 📋 Struktur Folder dan Organisasi Kode

Struktur folder yang jelas dan terorganisir sangat penting untuk pemeliharaan jangka panjang [^1_10][^1_11]:

```
src/
  /components/  # Komponen UI yang dapat digunakan kembali
    /common/    # Komponen dasar (Button, Card, Input, dll)
    /layout/    # Komponen layout (Navbar, Sidebar, Footer)
    /modules/   # Komponen spesifik modul
      /class/   # Komponen untuk manajemen kelas
      /student/ # Komponen untuk manajemen siswa
      /grade/   # Komponen untuk manajemen nilai
      /journal/ # Komponen untuk jurnal pembelajaran (RPP)
      /quiz/    # Komponen untuk bank soal
      /gamification/ # Komponen untuk gamifikasi
  /hooks/       # Custom React hooks
  /stores/      # Zustand state management
  /services/    # Service untuk API calls
  /utils/       # Fungsi utilitas
  /pages/       # Komponen halaman utama
  /assets/      # Aset statis (gambar, ikon, dll)
  /styles/      # Konfigurasi Tailwind dan file CSS global
  /types/       # Type definitions (TypeScript)
  App.jsx       # Komponen utama
  main.jsx      # Entry point
```

![React Component Hierarchy for Teacher Administration Dashboard - Tree structure showing components and their relationships](https://pplx-res.cloudinary.com/image/upload/v1749993131/pplx_code_interpreter/3575929a_h4s3cu.jpg)

React Component Hierarchy for Teacher Administration Dashboard - Tree structure showing components and their relationships

## 💻 Praktik Terbaik Komponen React

### Pembagian Komponen

Buat komponen baru ketika [^1_10][^1_11][^1_12]:

1. **JSX melebihi ~30 baris**: Komponen yang terlalu panjang sulit dimengerti dan dipelihara
2. **UI digunakan lebih dari sekali**: Komponen yang dapat digunakan kembali mengurangi duplikasi kode
3. **Memiliki tanggung jawab tunggal**: Setiap komponen harus melakukan satu hal dengan baik

### Contoh Komponen

```jsx
// BAD: Komponen yang terlalu besar
export function ClassManagement() {
  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold">Manajemen Kelas</h1>
      {/* Kode yang panjang untuk menampilkan tabel kelas */}
      {/* Kode yang panjang untuk form penambahan kelas */}
      {/* Kode yang panjang untuk filter dan pencarian */}
    </div>
  );
}

// GOOD: Komponen yang dipisahkan dengan baik
export function ClassManagement() {
  return (
    <div className="p-4">
      <PageHeader title="Manajemen Kelas" />
      <ClassFilters />
      <ClassTable />
      <ClassForm />
    </div>
  );
}
```


### Penggunaan HeroUI dan Tailwind

Gabungkan HeroUI dengan Tailwind CSS untuk UI yang konsisten dan responsif [^1_5][^1_13]:

```jsx
import { Button, Input, Card } from "@heroui/react";

function StudentForm({ onSubmit }) {
  return (
    <Card className="p-4 shadow-md rounded-lg">
      <h2 className="text-xl font-semibold mb-4">Tambah Siswa Baru</h2>
      <form onSubmit={onSubmit}>
        <div className="space-y-3">
          <Input 
            label="Nama Siswa" 
            placeholder="Masukkan nama siswa" 
            className="w-full" 
          />
          <Button type="submit" color="primary" className="w-full">
            Simpan
          </Button>
        </div>
      </form>
    </Card>
  );
}
```


## 🔄 Manajemen State dengan Zustand

Zustand menyediakan manajemen state yang sederhana dan efektif untuk aplikasi React [^1_14][^1_15]:

### Organisasi Store

- Buat store terpisah untuk setiap domain/fitur
- Ekspor actions dan getters, bukan store mentah
- Kelompokkan store terkait dalam direktori berdasarkan fitur

![Zustand State Management Flow for Teacher Administration System - Showing stores, state flow, and component interactions](https://pplx-res.cloudinary.com/image/upload/v1749993474/pplx_code_interpreter/fca5ffae_kvhlox.jpg)

Zustand State Management Flow for Teacher Administration System - Showing stores, state flow, and component interactions

### Pola Aksi Store

```javascript
// userStore.js
import { create } from 'zustand';

export const useUserStore = create((set, get) => ({
  user: null,
  isLoading: false,
  error: null,
  
  // Actions
  setUser: (userData) => {
    set({ user: userData });
  },
  
  clearUser: () => {
    set({ user: null });
  },
  
  fetchUser: async (id) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await fetch(`/api/users/${id}`);
      const data = await response.json();
      set({ user: data, isLoading: false });
    } catch (error) {
      set({ error: error.message, isLoading: false });
    }
  }
}));
```


### Penggunaan dalam Komponen

```jsx
import { useUserStore } from '../stores/userStore';

function UserProfile() {
  const { user, isLoading, error, fetchUser } = useUserStore();
  
  useEffect(() => {
    fetchUser(userId);
  }, [fetchUser, userId]);
  
  if (isLoading) return <Spinner />;
  if (error) return <ErrorMessage message={error} />;
  if (!user) return <EmptyState />;
  
  return (
    <div>
      <h2>{user.name}</h2>
      <p>{user.email}</p>
    </div>
  );
}
```


## 📊 Skema Database

Database PostgreSQL dengan struktur yang baik adalah fondasi untuk sistem yang handal [^1_16][^1_17]:

### Entitas Utama

- **users**: Menyimpan data pengguna (guru, admin)
- **schools**: Informasi sekolah
- **classes**: Kelas yang dikelola guru
- **students**: Data siswa
- **subjects**: Mata pelajaran
- **grades**: Nilai siswa
- **learning_plans**: Jurnal pembelajaran (RPP)
- **questions**: Bank soal
- **student_xp**: Poin pengalaman siswa
- **badges**: Badge yang dapat diperoleh siswa
- **leaderboard**: Peringkat siswa

![Teacher Administration Database Schema - Complete entity relationship diagram showing core, academic, and gamification tables](https://pplx-res.cloudinary.com/image/upload/v1749992749/pplx_code_interpreter/7b9f7f5a_dcc55t.jpg)

Teacher Administration Database Schema - Complete entity relationship diagram showing core, academic, and gamification tables

### Relasi Utama

- Satu guru dapat mengelola banyak kelas
- Satu kelas memiliki banyak siswa
- Satu siswa memiliki banyak nilai
- Satu mata pelajaran memiliki banyak soal dan RPP
- Siswa dapat memperoleh banyak badge dan XP


## 🎮 Implementasi Sistem Gamifikasi

Sistem gamifikasi meningkatkan keterlibatan siswa melalui reward dan pengakuan [^1_18][^1_19]:

### Pemberian XP

Siswa mendapatkan XP melalui berbagai aktivitas:

- Menyelesaikan tugas (+50 XP)
- Mengumpulkan quiz tepat waktu (+30 XP)
- Berpartisipasi dalam diskusi (+20 XP)
- Kehadiran sempurna dalam seminggu (+100 XP)
- Membantu siswa lain (+25 XP)


### Level dan Badge

Tingkatan berdasarkan XP:

- Level 1: 0-100 XP (Pemula)
- Level 2: 101-300 XP (Pelajar)
- Level 3: 301-600 XP (Cendekiawan)
- Level 4: 601-1000 XP (Ahli)
- Level 5: 1000+ XP (Master)

![Educational Gamification System Flow - Complete process flow showing how students earn XP, level up, and earn badges](https://pplx-res.cloudinary.com/image/upload/v1749992898/pplx_code_interpreter/0f36b660_hs5wfl.jpg)

Educational Gamification System Flow - Complete process flow showing how students earn XP, level up, and earn badges

### Implementasi dengan Directus

Directus dapat mengelola data gamifikasi dengan struktur collection yang sesuai [^1_4][^1_8]:

```
// Contoh struktur collection di Directus
student_xp:
  - id
  - student_id (relasi ke students)
  - total_xp
  - level
  - updated_at

badges:
  - id
  - name
  - description
  - icon
  - criteria

student_badges:
  - id
  - student_id (relasi ke students)
  - badge_id (relasi ke badges)
  - earned_at

leaderboard:
  - id
  - class_id (relasi ke classes)
  - student_id (relasi ke students)
  - rank
  - total_score
```


## 📝 Implementasi Jurnal Pembelajaran (RPP)

Jurnal pembelajaran memungkinkan guru membuat dan mengelola RPP secara efisien [^1_20][^1_21]:

### Struktur RPP

```javascript
// Contoh struktur data RPP
const learningPlan = {
  id: 'uuid',
  title: 'Pembelajaran Aljabar Dasar',
  class_id: 'class_uuid',
  subject_id: 'subject_uuid',
  content: {
    objectives: ['Siswa dapat memahami konsep dasar aljabar', '...'],
    materials: ['Pengenalan aljabar', 'Operasi dasar aljabar', '...'],
    methods: ['Ceramah', 'Diskusi kelompok', 'Latihan soal'],
    activities: [
      { name: 'Pendahuluan', duration: '15m', description: '...' },
      { name: 'Kegiatan Inti', duration: '60m', description: '...' },
      { name: 'Penutup', duration: '15m', description: '...' }
    ],
    assessments: ['Tes tertulis', 'Observasi', 'Portofolio']
  },
  created_at: '2025-06-15T12:00:00Z',
  updated_at: '2025-06-15T12:00:00Z'
}
```


### Komponen Editor RPP

```jsx
import { useState } from 'react';
import { Card, Tabs, Tab, Button, Input, Textarea } from '@heroui/react';

function RPPEditor({ initialData, onSave }) {
  const [rpp, setRPP] = useState(initialData || {
    title: '',
    content: {
      objectives: [],
      materials: [],
      methods: [],
      activities: [],
      assessments: []
    }
  });

  return (
    <Card className="p-4">
      <h2 className="text-2xl font-bold mb-4">Editor RPP</h2>
      <Input
        label="Judul RPP"
        value={rpp.title}
        onChange={e => setRPP({...rpp, title: e.target.value})}
        className="mb-4"
      />
      
      <Tabs>
        <Tab key="objectives" title="Tujuan Pembelajaran">
          {/* Form untuk tujuan pembelajaran */}
        </Tab>
        <Tab key="materials" title="Materi Pembelajaran">
          {/* Form untuk materi pembelajaran */}
        </Tab>
        {/* Tab lainnya untuk methods, activities, assessments */}
      </Tabs>
      
      <Button color="primary" onClick={() => onSave(rpp)}>
        Simpan RPP
      </Button>
    </Card>
  );
}
```


## 📚 Implementasi Bank Soal

Bank soal memungkinkan guru membuat, mengelola, dan menggunakan soal untuk latihan dan ujian [^1_22][^1_23]:

### Struktur Soal

```javascript
// Contoh struktur data soal
const question = {
  id: 'uuid',
  question_text: 'Berapakah hasil dari 2x + 3 = 7?',
  type: 'multiple_choice', // multiple_choice, essay, true_false
  difficulty: 'medium', // easy, medium, hard
  subject_id: 'subject_uuid',
  correct_answer: 'x = 2',
  options: [
    { id: 'a', text: 'x = 1' },
    { id: 'b', text: 'x = 2' },
    { id: 'c', text: 'x = 3' },
    { id: 'd', text: 'x = 4' }
  ],
  explanation: 'Untuk menyelesaikan persamaan 2x + 3 = 7, kurangkan 3 dari kedua sisi...',
  tags: ['aljabar', 'persamaan linear'],
  created_by: 'user_uuid',
  created_at: '2025-06-15T12:00:00Z'
}
```


### Komponen Preview Soal

```jsx
import { Card, RadioGroup, Radio } from '@heroui/react';

function QuestionPreview({ question }) {
  return (
    <Card className="p-4">
      <h3 className="text-lg font-semibold mb-2">{question.question_text}</h3>
      
      {question.type === 'multiple_choice' && (
        <RadioGroup>
          {question.options.map(option => (
            <Radio key={option.id} value={option.id}>
              {option.text}
            </Radio>
          ))}
        </RadioGroup>
      )}
      
      {question.type === 'essay' && (
        <Textarea placeholder="Jawaban essay" className="w-full" />
      )}
      
      {question.type === 'true_false' && (
        <RadioGroup>
          <Radio value="true">Benar</Radio>
          <Radio value="false">Salah</Radio>
        </RadioGroup>
      )}
      
      <div className="mt-4 p-3 bg-blue-50 rounded-md">
        <h4 className="font-medium">Jawaban Benar:</h4>
        <p>{question.correct_answer}</p>
        
        <h4 className="font-medium mt-2">Penjelasan:</h4>
        <p>{question.explanation}</p>
      </div>
    </Card>
  );
}
```


## 🔒 Integrasi Directus dengan React

Directus memberikan API yang lengkap untuk mengelola data backend [^1_4][^1_24]:

### Konfigurasi Directus

```javascript
// services/directus.js
import { Directus } from '@directus/sdk';

const directus = new Directus(process.env.REACT_APP_DIRECTUS_URL);

export async function login(email, password) {
  try {
    const response = await directus.auth.login({ email, password });
    return response;
  } catch (error) {
    throw error;
  }
}

export async function getItems(collection, params = {}) {
  try {
    const response = await directus.items(collection).readByQuery(params);
    return response.data;
  } catch (error) {
    throw error;
  }
}

export async function createItem(collection, data) {
  try {
    const response = await directus.items(collection).createOne(data);
    return response;
  } catch (error) {
    throw error;
  }
}

export async function updateItem(collection, id, data) {
  try {
    const response = await directus.items(collection).updateOne(id, data);
    return response;
  } catch (error) {
    throw error;
  }
}

export async function deleteItem(collection, id) {
  try {
    await directus.items(collection).deleteOne(id);
    return true;
  } catch (error) {
    throw error;
  }
}
```


### Penggunaan dalam Service

```javascript
// services/classService.js
import { getItems, createItem, updateItem, deleteItem } from './directus';

export async function getClasses(params = {}) {
  return getItems('classes', params);
}

export async function getClassById(id, params = {}) {
  return getItems('classes', {
    ...params,
    filter: { id: { _eq: id } }
  });
}

export async function createClass(data) {
  return createItem('classes', data);
}

export async function updateClass(id, data) {
  return updateItem('classes', id, data);
}

export async function deleteClass(id) {
  return deleteItem('classes', id);
}
```


## 📱 Responsivitas dan Aksesibilitas

Website harus berfungsi dengan baik di berbagai perangkat dan dapat diakses oleh semua pengguna [^1_5][^1_25]:

### Responsivitas dengan Tailwind

```jsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  <Card className="p-4">
    <h3 className="text-lg font-bold">Total Siswa</h3>
    <p className="text-3xl font-semibold">128</p>
  </Card>
  
  <Card className="p-4">
    <h3 className="text-lg font-bold">Rata-rata Nilai</h3>
    <p className="text-3xl font-semibold">85.7</p>
  </Card>
  
  <Card className="p-4">
    <h3 className="text-lg font-bold">Kehadiran</h3>
    <p className="text-3xl font-semibold">92%</p>
  </Card>
</div>
```


### Aksesibilitas dengan HeroUI

HeroUI dibangun di atas React Aria, memastikan dukungan aksesibilitas yang luar biasa [^1_5][^1_25]:

- Navigasi keyboard
- Manajemen fokus
- Komponen yang sadar akan tumbukan
- Perataan yang tepat


## 🚀 Deployment dan Optimasi

Memastikan aplikasi berjalan dengan performa terbaik [^1_26][^1_8]:

### Optimasi Frontend

- Gunakan React.lazy untuk code splitting
- Implementasikan memoization dengan useMemo dan useCallback
- Optimasi rendering dengan React.memo
- Minifikasi bundle dengan alat seperti Webpack atau Vite


### Direktori Project

```
/
  ├── frontend/           # Aplikasi React
  │   ├── src/            # Kode sumber
  │   ├── public/         # Aset statis
  │   ├── package.json    # Dependency frontend
  │   └── vite.config.js  # Konfigurasi build
  │
  ├── backend/            # Direktus CMS
  │   ├── extensions/     # Ekstensi Directus
  │   ├── migrations/     # Migrasi database
  │   ├── .env            # Variabel lingkungan
  │   └── docker-compose.yml # Konfigurasi Docker
  │
  ├── docs/               # Dokumentasi proyek
  ├── .gitignore          # File yang diabaikan git
  └── README.md           # Dokumentasi utama
```


## 🔍 Pengujian dan Debugging

Strategi pengujian untuk memastikan kualitas kode [^1_11][^1_27]:

### Unit Testing dengan Jest dan React Testing Library

```javascript
// __tests__/components/StudentForm.test.js
import { render, screen, fireEvent } from '@testing-library/react';
import StudentForm from '../../components/StudentForm';

describe('StudentForm', () => {
  test('renders form correctly', () => {
    render(<StudentForm />);
    
    expect(screen.getByLabelText(/nama siswa/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /simpan/i })).toBeInTheDocument();
  });
  
  test('calls onSubmit when form is submitted', () => {
    const handleSubmit = jest.fn();
    render(<StudentForm onSubmit={handleSubmit} />);
    
    fireEvent.change(screen.getByLabelText(/nama siswa/i), {
      target: { value: 'John Doe' },
    });
    
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' },
    });
    
    fireEvent.click(screen.getByRole('button', { name: /simpan/i }));
    
    expect(handleSubmit).toHaveBeenCalledTimes(1);
    expect(handleSubmit).toHaveBeenCalledWith({
      name: 'John Doe',
      email: '<EMAIL>',
    });
  });
});
```


## 📋 Kesimpulan

Panduan praktik terbaik ini memberikan fondasi yang kuat untuk mengembangkan website administrasi guru yang komprehensif dan modern [^1_10][^1_1]. Dengan mengikuti praktik-praktik ini, Anda akan dapat membangun sistem yang:

- Terstruktur dengan baik dan mudah dipelihara
- Responsif dan dapat diakses oleh semua pengguna
- Teroptimasi untuk performa terbaik
- Mudah dikembangkan lebih lanjut sesuai kebutuhan

Panduan ini dapat terus dikembangkan seiring dengan kemajuan teknologi dan kebutuhan pengguna yang berkembang [^1_4][^1_11].

<div style="text-align: center">⁂</div>

[^1_1]: https://arxiv.org/abs/2504.18793
[^1_2]: https://pnojournal.wordpress.com/2022/07/03/chudinsky/
[^1_3]: https://github.com/directus/directus/discussions/3164
[^1_4]: https://dev.to/parthspan/optimizing-directus-performance-tips-for-fast-and-efficient-headless-cms-8kf
[^1_5]: https://www.heroui.com
[^1_6]: https://componentlibraries.com/item/heroui
[^1_7]: https://dev.to/abdulrehman18/mastering-postgresql-tips-and-tricks-for-effective-database-management-51ag
[^1_8]: https://www.restack.io/docs/directus-knowledge-directus-headless-cms-features
[^1_9]: https://docs.directus.io/use-cases/headless-cms/introduction
[^1_10]: copilot-instructions.md
[^1_11]: https://rsglobal.pl/index.php/ws/article/view/2812
[^1_12]: https://tunasbangsa.ac.id/pkm/index.php/kesatria/article/view/553
[^1_13]: https://github.com/heroui-inc/heroui
[^1_14]: https://react.dev/learn/managing-state
[^1_15]: https://www.pluralsight.com/courses/react-state-managing
[^1_16]: https://ijsrcseit.com/index.php/home/<USER>/view/CSEIT241051016
[^1_17]: https://esj.eastasouth-institute.com/index.php/esiscs/article/view/488
[^1_18]: https://ieeexplore.ieee.org/document/9533218/
[^1_19]: https://online-journals.org/index.php/i-jet/article/view/24087
[^1_20]: https://journal.lppmunindra.ac.id/index.php/Formatif/article/view/2978
[^1_21]: http://jurnal.abulyatama.ac.id/index.php/dedikasi/article/view/5037
[^1_22]: https://pubs.aip.org/aip/acp/article-pdf/doi/10.1063/1.4982561/13744865/020196_1_online.pdf
[^1_23]: https://docs.moodle.org/dev/Question_database_structure
[^1_24]: https://docs.directus.io/guides/real-time/chat/react
[^1_25]: https://www.heroui.com/docs/guide/introduction
[^1_26]: https://aem.az/uploads/posts/2025/01/E.%C4%B0.%2019.1-103-108.pdf
[^1_27]: https://journals.uran.ua/vestnikpgtu_tech/article/view/310670
[^1_28]: https://journalwjarr.com/node/1141
[^1_29]: https://docs.directus.io/self-hosted/config-options
[^1_30]: https://github.com/directus/directus/discussions/6832
[^1_31]: https://www.youtube.com/watch?v=4dHbrRduH6s
[^1_32]: https://ijsrem.com/download/react-nex-a-modular-component-library-with-ai-driven-code-generation/
[^1_33]: https://journal.maranatha.edu/index.php/jutisi/article/view/3493
[^1_34]: https://dl.acm.org/doi/10.1145/3706890.3707043
[^1_35]: https://www.heroui.com/docs/frameworks/nextjs
[^1_36]: https://www.heroui.com/docs/guide/nextui-to-heroui
[^1_37]: https://github.com/Harsh-git98/Classroom-Dashboard-React
[^1_38]: https://best-of-web.builder.io/library/heroui-inc/heroui
[^1_39]: https://www.youtube.com/watch?v=6luHSutfak4
[^1_40]: https://github.com/ashvinck/student_teacher_dashboard
[^1_41]: https://journals.sagepub.com/doi/10.1177/07356331221127635
[^1_42]: https://arxiv.org/abs/2302.12834
[^1_43]: https://www.atlantis-press.com/article/125940214
[^1_44]: https://www.e3s-conferences.org/10.1051/e3sconf/************
[^1_45]: https://conferences.ittelkom-pwt.ac.id/index.php/centive/article/download/185/212/
[^1_46]: https://journal.binus.ac.id/index.php/jggag/article/view/7218
[^1_47]: https://journals.sagepub.com/doi/abs/10.1177/0193841X241291752
[^1_48]: https://ec.europa.eu/programmes/erasmus-plus/project-result-content/c479d4b8-bcff-4422-97b8-2dccb5ae9c1a/IO3_A3.1._Designing_Gamification_Systems_in_the_Classroom.pdf
[^1_49]: https://www.scaler.com/topics/er-diagram-for-bank-database/
[^1_50]: https://core.ac.uk/download/*********.pdf
[^1_51]: https://academictech.uchicago.edu/2023/11/08/effective-design-principles-and-accessibility-for-gamifying-your-classes/
[^1_52]: https://cs.grinnell.edu/********/fprepared/goto/glimitj/example+1+bank+schema+branch+customer.pdf
[^1_53]: https://journal.radenintan.ac.id/index.php/tadris/article/view/15781
[^1_54]: http://e-journal.iain-palangkaraya.ac.id/index.php/tarib/article/view/1926
[^1_55]: https://www.iosrjournals.org/iosr-jbm/papers/Vol26-issue10/Ser-16/C2610162229.pdf
[^1_56]: https://files1.simpkb.id/guruberbagi/rpp/772122-**********.pdf
[^1_57]: https://ejournal.upi.edu/index.php/CURRICULA/article/view/63747
[^1_58]: https://www.learningstream.com/key-features/learning-plan/
[^1_59]: https://www.cs.rug.nl/~paris/papers/IWCMQ02.pdf
[^1_60]: https://discovery-center.cloud.sap/documents/cmis/json/5e5fd7b197b6556fa80bc826/root?objectId=FiPGzNpSJngsO79waLYxG-CMo5MeY7v8x69amu-dPkU
[^1_61]: https://files.eric.ed.gov/fulltext/ED562354.pdf
[^1_62]: https://www.emerald.com/insight/content/doi/10.1108/GKMC-12-2023-0470/full/html
[^1_63]: https://scindeks.ceon.rs/Article.aspx?artid=1451-20922104876M
[^1_64]: https://pediatrics.jmir.org/2022/4/e38940
[^1_65]: http://preprints.jmir.org/preprint/34355
[^1_66]: https://www.ijsr.net/getabstract.php?paperid=SR24054120639
[^1_67]: https://stel.bmj.com/lookup/doi/10.1136/bmjstel-2020-aspihconf.141
[^1_68]: https://www.codecademy.com/learn/learn-react-state-management
[^1_69]: https://www.youtube.com/watch?v=qqqyUTTS-9g
[^1_70]: https://www.creative-tim.com/ai/community/modern-education-platform-dashboard-template-9gnss2f6xi0lhk1
[^1_71]: https://arxiv.org/pdf/1802.02663.pdf
[^1_72]: https://www.coursera.org/projects/rudi-hinds-react-fundamentals-of-state-management-in-class-components
[^1_73]: https://www.colorado.edu/research/ai-institute/sites/default/files/attached-files/hcii2022-shortpaper-elsymeis.pdf
[^1_74]: https://link.springer.com/10.1007/s10758-022-09598-7
[^1_75]: https://ieeexplore.ieee.org/document/10260876/
[^1_76]: http://210.101.116.36/JournalSearch/ISS_Detail.asp?key=3261640&tname=kiss2002&code=9099
[^1_77]: https://un-pub.eu/ojs/index.php/wjet/article/view/6267
[^1_78]: https://un-pub.eu/ojs/index.php/wjet/article/view/6233
[^1_79]: http://dl.acm.org/citation.cfm?doid=3312714.3312736
[^1_80]: https://dribbble.com/tags/education-dashboard
[^1_81]: https://dribbble.com/tags/educational-dashboard
[^1_82]: https://dashboarddesignpatterns.github.io
[^1_83]: https://www.pinterest.com/ideas/education-dashboard/894791911449/
[^1_84]: https://unicornplatform.com/blog/best-practices-for-designing-online-course-dashboards/
[^1_85]: https://www.syncfusion.com/react-components/react-progressbar
[^1_86]: https://learnexus.com/learning-analytics-dashboard-design/
[^1_87]: https://dribbble.com/shots/24479113-E-learning-Educational-Dashboard-Design-Web-App
[^1_88]: https://dev.to/04anilr/how-to-make-dynamic-progress-bar-in-reactjs-300c
[^1_89]: https://learnexus.com/user-friendly-learning-analytics-dashboards/
[^1_90]: http://link.springer.com/10.1007/978-1-4842-5663-3
[^1_91]: http://link.springer.com/10.1007/978-1-4842-5663-3_4
[^1_92]: https://journalijsra.com/node/1042
[^1_93]: https://arxiv.org/html/2504.09288v1
[^1_94]: https://directus.io/docs/getting-started/data-model
[^1_95]: https://jtec.utem.edu.my/jtec/article/view/6192
[^1_96]: https://ieeexplore.ieee.org/document/10874885/
[^1_97]: https://gurukuljournal.com/library-linx-bridging-education-with-efficient-library-management/
[^1_98]: https://ijsrem.com/download/evaluating-modern-android-frameworks-a-comparative-study-of-flutter-kotlin-multiplatform-jetpack-compose-and-react-native/
[^1_99]: https://www.semanticscholar.org/paper/9acafa9bab3003ec15cd78a5eff3c1d2d5681d33
[^1_100]: https://www.semanticscholar.org/paper/e1d181e5fb0e76d0f13c8fa5535fbee55fd831bd
[^1_101]: https://www.semanticscholar.org/paper/5cbfecacdedcb61e7d383fd5030f067b9336ce0c
[^1_102]: http://ijcs.stmikindonesia.ac.id/ijcs/index.php/ijcs/article/view/3374
[^1_103]: https://www.sciencedirect.com/journal/journal-of-systems-architecture
[^1_104]: https://www.semanticscholar.org/paper/1097aad4c013d5d0bcb018d19a9f89e02774e378
[^1_105]: https://www.semanticscholar.org/paper/c7677d7bd27f81b935be9998de1809bbed81ddfd
[^1_106]: https://www.semanticscholar.org/paper/9b79d5c2d92c2e6b4a77b330dd99743b5f61ccb3
[^1_107]: https://www.semanticscholar.org/paper/299205c000c4b34805db940a64316ebaf83b6f0d
[^1_108]: https://www.semanticscholar.org/paper/347ee525796b544c6bf9ca186dc1315990b03a6f
[^1_109]: https://ejournal.unesa.ac.id/index.php/jurnal-pendidikan-teknik-elektro/article/download/20502/18792/24533
[^1_110]: https://lmsspada.kemdiktisaintek.go.id/mod/resource/view.php?id=60683
[^1_111]: https://www.scitepress.org/DigitalLibrary/Link.aspx?doi=10.5220/0010966700003260
[^1_112]: https://www.semanticscholar.org/paper/70d6ed3cabdda2357fac725fbc7843a46dced1ad
[^1_113]: https://www.semanticscholar.org/paper/3364f9185d93f65f51499eb87adf363a3dfedecd
[^1_114]: https://www.ijsat.org/research-paper.php?id=5865
[^1_115]: https://www.nimblechapps.com/blog/15-react-state-management-libraries-to-use-in-2025
[^1_116]: https://www.redhat.com/en/blog/14-software-architecture-patterns
[^1_117]: https://link.springer.com/10.1007/978-3-662-44188-6_21
[^1_118]: https://ieeexplore.ieee.org/document/9903550/
[^1_119]: https://scholar.kyobobook.co.kr/article/detail/*************
[^1_120]: https://ieeexplore.ieee.org/document/9251219/

---

# Implementasi Modul Bank Soal Efisien di Directus dengan Sinkronisasi ReactJS

## Pendahuluan

Bank soal merupakan komponen penting dalam sistem administrasi guru, memungkinkan pengelolaan koleksi pertanyaan dan jawaban yang dapat digunakan untuk latihan dan ujian siswa [^2_1]. Dengan memanfaatkan Directus sebagai headless CMS dan PostgreSQL sebagai database, kita dapat membangun bank soal yang efisien, terstruktur, dan mudah diakses melalui antarmuka ReactJS [^2_2]. Implementasi yang tepat akan memungkinkan pengacakan soal, penilaian otomatis, dan analisis performa siswa [^2_3].

## Desain Skema Database untuk Bank Soal

### Struktur Koleksi di Directus

Untuk membuat modul bank soal yang efisien, kita perlu merancang struktur database yang optimal dengan memanfaatkan fitur relasional Directus [^2_4]. Berikut struktur koleksi utama yang diperlukan:

1. **questions** - Koleksi untuk menyimpan semua pertanyaan
2. **options** - Koleksi untuk pilihan jawaban (untuk soal pilihan ganda)
3. **categories** - Koleksi untuk kategori/topik pertanyaan
4. **difficulty_levels** - Koleksi untuk tingkat kesulitan
5. **exams** - Koleksi untuk konfigurasi ujian
6. **exam_questions** - Koleksi junction untuk relasi many-to-many antara exam dan questions
7. **exercises** - Koleksi untuk konfigurasi latihan
8. **exercise_questions** - Koleksi junction untuk relasi many-to-many antara exercise dan questions
9. **student_attempts** - Koleksi untuk menyimpan percobaan siswa
10. **attempt_answers** - Koleksi untuk jawaban siswa per percobaan [^2_5]

### Skema Detail Koleksi

#### Koleksi Questions

```sql
CREATE TABLE questions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  question_text TEXT NOT NULL,
  question_type VARCHAR(20) NOT NULL CHECK (question_type IN ('multiple_choice', 'true_false', 'essay', 'fill_blank')),
  correct_answer TEXT,
  explanation TEXT,
  difficulty_level_id UUID REFERENCES difficulty_levels(id),
  category_id UUID REFERENCES categories(id),
  created_by UUID REFERENCES directus_users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

Ini akan membuat tabel pertanyaan dengan berbagai tipe soal, penjelasan, tingkat kesulitan, dan metadata lainnya [^2_6]. Penting untuk menyertakan kolom difficulty_level_id dan category_id untuk memudahkan pengkategorian dan penyaringan soal [^2_1].

#### Koleksi Options

```sql
CREATE TABLE options (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  question_id UUID REFERENCES questions(id) ON DELETE CASCADE,
  option_text TEXT NOT NULL,
  is_correct BOOLEAN DEFAULT FALSE,
  order_index INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

Koleksi options berhubungan dengan questions dengan relasi Many-to-One, dimana setiap pertanyaan pilihan ganda memiliki beberapa opsi jawaban [^2_7]. Pengaturan cascade delete memastikan opsi jawaban akan terhapus jika pertanyaan terkait dihapus [^2_2].

### Relasi antar Koleksi

Dalam Directus, relasi antar koleksi sangat penting untuk membangun struktur bank soal yang efisien [^2_5]. Berikut beberapa relasi utama:

1. **Many-to-One (M2O)**: Questions ke Categories dan DifficultyLevels [^2_2]
2. **One-to-Many (O2M)**: Categories ke Questions dan DifficultyLevels ke Questions [^2_1]
3. **Many-to-Many (M2M)**: Exams ke Questions (melalui tabel junction exam_questions) dan Exercises ke Questions (melalui tabel junction exercise_questions) [^2_7]

Relasi ini memungkinkan kita untuk mengelompokkan soal berdasarkan kategori, tingkat kesulitan, serta menggunakannya dalam berbagai ujian dan latihan secara fleksibel [^2_2].

## Implementasi Modul Latihan dan Ujian

### Struktur Modul Latihan

Modul latihan umumnya bersifat lebih santai dan memungkinkan siswa untuk belajar tanpa tekanan waktu [^2_3]. Berikut struktur koleksi exercises:

```sql
CREATE TABLE exercises (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(255) NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  show_answers BOOLEAN DEFAULT TRUE,
  allow_retries BOOLEAN DEFAULT TRUE,
  created_by UUID REFERENCES directus_users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

Kolom `show_answers` memungkinkan konfigurasi apakah jawaban benar ditampilkan setelah siswa menjawab, sedangkan `allow_retries` mengizinkan siswa mencoba kembali soal yang sama [^2_3].

### Struktur Modul Ujian

Modul ujian memerlukan konfigurasi tambahan seperti batas waktu dan aturan keamanan [^2_8]:

```sql
CREATE TABLE exams (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(255) NOT NULL,
  description TEXT,
  time_limit INTEGER, -- in minutes
  pass_score INTEGER, -- minimum score to pass (percentage)
  is_active BOOLEAN DEFAULT TRUE,
  start_date TIMESTAMP WITH TIME ZONE,
  end_date TIMESTAMP WITH TIME ZONE,
  shuffle_questions BOOLEAN DEFAULT TRUE,
  shuffle_options BOOLEAN DEFAULT TRUE,
  created_by UUID REFERENCES directus_users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

Kolom `shuffle_questions` dan `shuffle_options` memungkinkan pengacakan soal dan pilihan jawaban untuk mencegah kecurangan, sementara `time_limit` mengatur batas waktu pengerjaan [^2_8].

### Pengacakan Soal dengan Fisher-Yates Shuffle

Untuk meningkatkan keamanan dan mengurangi kemungkinan kecurangan, kita dapat mengimplementasikan algoritma Fisher-Yates Shuffle untuk mengacak urutan soal dan pilihan jawaban [^2_9]. Algoritma ini menjamin pengacakan yang tidak bias dan efisien:

```javascript
function fisherYatesShuffle(array) {
  let currentIndex = array.length;
  let randomIndex;

  while (currentIndex !== 0) {
    randomIndex = Math.floor(Math.random() * currentIndex);
    currentIndex--;
    [array[currentIndex], array[randomIndex]] = [array[randomIndex], array[currentIndex]];
  }

  return array;
}
```

Algoritma ini memiliki kompleksitas O(n) dan sangat efisien untuk pengacakan soal ujian [^2_10]. Implementasi Fisher-Yates Shuffle terbukti lebih optimal dibandingkan metode pengacakan lainnya dalam aplikasi ujian [^2_11].

## Optimasi Performa di Directus

### Caching Strategy

Directus menyediakan sistem caching bawaan yang dapat meningkatkan performa secara signifikan [^2_12]. Berikut beberapa konfigurasi yang disarankan:

```
CACHE_ENABLED=true
CACHE_TTL=5m
CACHE_AUTO_PURGE=true
CACHE_STORE=redis
```

Untuk bank soal dengan banyak data, penggunaan Redis sebagai cache store akan memberikan performa yang lebih baik dibandingkan memory cache [^2_13]. Konfigurasi `CACHE_AUTO_PURGE=true` memastikan cache otomatis diperbarui ketika data berubah [^2_12].

### Indexing pada PostgreSQL

Untuk meningkatkan performa query, penting untuk membuat indeks pada kolom yang sering digunakan dalam pencarian:

```sql
CREATE INDEX idx_questions_category ON questions(category_id);
CREATE INDEX idx_questions_difficulty ON questions(difficulty_level_id);
CREATE INDEX idx_options_question ON options(question_id);
CREATE INDEX idx_exam_questions_exam ON exam_questions(exam_id);
CREATE INDEX idx_exam_questions_question ON exam_questions(question_id);
```

Pengindeksan ini akan mempercepat proses pencarian soal berdasarkan kategori, tingkat kesulitan, dan relasi antar tabel [^2_13].

## Integrasi dengan ReactJS

### Setup Direktus SDK di React

Untuk mengintegrasikan bank soal Direktus dengan aplikasi React, kita perlu mengatur Direktus SDK [^2_14]:

```javascript
import { createDirectus, rest, authentication } from '@directus/sdk';

// Definisi tipe untuk TypeScript
interface Question {
  id: string;
  question_text: string;
  question_type: string;
  options?: Option[];
}

interface Option {
  id: string;
  option_text: string;
  is_correct: boolean;
}

interface Schema {
  questions: Question[];
  options: Option[];
  // Definisikan koleksi lainnya
}

// Buat instance Direktus client
const directus = createDirectus<Schema>('https://direktus-anda.com')
  .with(rest())
  .with(authentication());

// Login
const login = async () => {
  await directus.login('<EMAIL>', 'password');
};
```

SDK ini memungkinkan kita untuk mengakses data dari Direktus dengan cara yang type-safe dan efisien [^2_15].

### Fetching Data Bank Soal

Untuk mengambil data soal dari Direktus API:

```javascript
import { readItems } from '@directus/sdk';

// Ambil semua soal dengan opsi jawabannya
const fetchQuestions = async () => {
  try {
    const questions = await directus.request(
      readItems('questions', {
        fields: ['id', 'question_text', 'question_type', 'options.*'],
        filter: {
          category_id: {
            _eq: selectedCategoryId
          }
        },
        limit: 10
      })
    );
    return questions;
  } catch (error) {
    console.error('Error fetching questions:', error);
    return [];
  }
};
```

Kode ini mengambil 10 soal dari kategori tertentu, termasuk semua opsi jawabannya, memanfaatkan fitur relasi otomatis dari Direktus [^2_15].

### Implementasi Komponen Quiz di React

Berikut struktur komponen dasar untuk membuat aplikasi quiz dengan React:

```jsx
import React, { useState, useEffect } from 'react';
import { readItems } from '@directus/sdk';

const Quiz = ({ examId }) => {
  const [questions, setQuestions] = useState([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState({});
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadQuestions = async () => {
      setIsLoading(true);
      try {
        // Ambil soal-soal untuk ujian tertentu
        const examQuestions = await directus.request(
          readItems('exam_questions', {
            fields: ['question.id', 'question.question_text', 'question.question_type', 'question.options.*'],
            filter: { exam_id: { _eq: examId } }
          })
        );
        
        // Ekstrak data pertanyaan dan acak urutannya
        const questionData = examQuestions.map(eq => eq.question);
        const shuffledQuestions = fisherYatesShuffle([...questionData]);
        
        setQuestions(shuffledQuestions);
      } catch (error) {
        console.error('Error loading questions:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadQuestions();
  }, [examId]);

  // Handler untuk menyimpan jawaban
  const handleAnswer = (questionId, answer) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  // Handler untuk pindah ke pertanyaan berikutnya
  const handleNext = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  // Render komponen loading
  if (isLoading) {
    return <div>Loading...</div>;
  }

  // Render pertanyaan kosong
  if (questions.length === 0) {
    return <div>No questions available.</div>;
  }

  const currentQuestion = questions[currentQuestionIndex];

  return (
    <div className="quiz-container">
      <div className="question-counter">
        Question {currentQuestionIndex + 1} of {questions.length}
      </div>
      <div className="question">
        <h3>{currentQuestion.question_text}</h3>
        
        <div className="options">
          {currentQuestion.options.map(option => (
            <div 
              key={option.id} 
              className={`option ${answers[currentQuestion.id] === option.id ? 'selected' : ''}`}
              onClick={() => handleAnswer(currentQuestion.id, option.id)}
            >
              {option.option_text}
            </div>
          ))}
        </div>
      </div>
      
      <div className="navigation">
        <button onClick={handleNext} disabled={currentQuestionIndex === questions.length - 1}>
          Next
        </button>
      </div>
    </div>
    );
};

export default Quiz;

```

Komponen ini menampilkan soal satu per satu, menyimpan jawaban pengguna, dan memungkinkan navigasi antar soal [^2_16]. Penggunaan Fisher-Yates Shuffle memastikan soal muncul dalam urutan acak setiap kali ujian dimulai [^2_10].

### Implementasi Timer untuk Ujian

Untuk soal ujian yang memiliki batasan waktu, kita perlu mengimplementasikan timer:

```jsx
import React, { useState, useEffect } from 'react';

const ExamTimer = ({ timeLimit, onTimeUp }) => {
  const [timeRemaining, setTimeRemaining] = useState(timeLimit * 60); // Convert to seconds

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeRemaining(prevTime => {
        if (prevTime <= 1) {
          clearInterval(timer);
          onTimeUp();
          return 0;
        }
        return prevTime - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [onTimeUp, timeLimit]);

  // Format time as mm:ss
  const minutes = Math.floor(timeRemaining / 60);
  const seconds = timeRemaining % 60;

  return (
    <div className="exam-timer">
      Time Remaining: {minutes.toString().padStart(2, '0')}:{seconds.toString().padStart(2, '0')}
    </div>
  );
};

export default ExamTimer;
```

Komponen ini menghitung mundur dari batas waktu yang ditentukan dan memanggil callback `onTimeUp` ketika waktu habis [^2_17]. Untuk keamanan timer, perlu diimplementasikan validasi waktu di sisi server untuk mencegah manipulasi [^2_18].

### State Management dengan React Context API

Untuk aplikasi quiz yang lebih kompleks, menggunakan Context API dapat membantu mengelola state dengan lebih efisien:

```jsx
import React, { createContext, useContext, useReducer } from 'react';

// Define initial state
const initialState = {
  questions: [],
  currentQuestionIndex: 0,
  answers: {},
  isLoading: true,
  timeRemaining: 0,
  examCompleted: false
};

// Create reducer function
function quizReducer(state, action) {
  switch (action.type) {
    case 'SET_QUESTIONS':
      return {
        ...state,
        questions: action.payload,
        isLoading: false
      };
    case 'SET_ANSWER':
      return {
        ...state,
        answers: {
          ...state.answers,
          [action.payload.questionId]: action.payload.answer
        }
      };
    case 'NEXT_QUESTION':
      return {
        ...state,
        currentQuestionIndex: state.currentQuestionIndex + 1
      };
    case 'SET_TIME_REMAINING':
      return {
        ...state,
        timeRemaining: action.payload
      };
    case 'COMPLETE_EXAM':
      return {
        ...state,
        examCompleted: true
      };
    default:
      return state;
  }
}

// Create context
const QuizContext = createContext();

// Create provider component
export function QuizProvider({ children }) {
  const [state, dispatch] = useReducer(quizReducer, initialState);
  
  return (
    <QuizContext.Provider value={{ state, dispatch }}>
      {children}
    </QuizContext.Provider>
  );
}

// Create custom hook for using the context
export function useQuiz() {
  const context = useContext(QuizContext);
  if (!context) {
    throw new Error('useQuiz must be used within a QuizProvider');
  }
  return context;
}
```

Pendekatan ini memungkinkan komponen-komponen yang berbeda dalam aplikasi quiz untuk berbagi state tanpa prop drilling, menjadikan aplikasi lebih modular dan mudah dikelola [^2_19].

## Keamanan dan Integritas Data

### Mencegah Kecurangan dalam Ujian

Beberapa strategi untuk meningkatkan keamanan ujian:

1. **Pengacakan Soal dan Jawaban**: Implementasikan Fisher-Yates Shuffle untuk mengacak urutan soal dan opsi jawaban [^2_10]
2. **Batasan Waktu**: Tetapkan batas waktu yang wajar dan validasi waktu di server [^2_20]
3. **Pencegahan Tab Switching**: Deteksi ketika pengguna berpindah tab atau jendela [^2_20]
4. **Validasi Server**: Selalu validasi jawaban di server, bukan hanya di client [^2_21]
5. **Rate Limiting**: Batasi jumlah permintaan API untuk mencegah serangan brute force [^2_22]

### Manajemen Sesi yang Aman

Pengelolaan sesi yang baik sangat penting untuk keamanan aplikasi ujian [^2_23]:

1. **Gunakan Direktus Authentication**: Manfaatkan sistem otentikasi bawaan Direktus [^2_24]
2. **Tetapkan Expiry Time**: Atur waktu kedaluwarsa token dengan durasi yang sesuai [^2_25]
3. **Validasi Token**: Selalu validasi token di server sebelum mengizinkan akses ke soal ujian [^2_22]
4. **Pemeriksaan Hak Akses**: Terapkan kontrol akses berdasarkan peran pengguna di Direktus [^2_24]

## Kesimpulan

Membangun modul bank soal yang efisien dengan Directus dan ReactJS memerlukan perencanaan struktur database yang matang, implementasi keamanan yang tepat, dan desain antarmuka yang user-friendly [^2_2]. Dengan memanfaatkan relasi antar koleksi di Directus, kita dapat membuat sistem bank soal yang fleksibel dan skalabel [^2_5].

Penggunaan algoritma Fisher-Yates Shuffle untuk pengacakan soal meningkatkan keamanan ujian dengan meminimalisir kemungkinan kecurangan [^2_9]. Sementara itu, integrasi dengan ReactJS menggunakan Directus SDK memungkinkan pengembangan antarmuka yang responsif dan interaktif [^2_14].

Optimasi performa melalui caching dan indexing memastikan sistem tetap cepat bahkan ketika bank soal berisi ribuan pertanyaan [^2_13]. Kombinasi dari semua elemen ini akan menghasilkan modul bank soal yang efisien, aman, dan mudah digunakan baik oleh guru maupun siswa [^2_6].

<div style="text-align: center">⁂</div>

[^2_1]: https://directus.io/docs/guides/data-model/relationships

[^2_2]: https://docs.directus.io/app/data-model/relationships

[^2_3]: https://journal.unilak.ac.id/index.php/dz/article/view/636

[^2_4]: https://directus.io/docs/guides/data-model/collections

[^2_5]: https://austinvernsonger.gitbooks.io/directus-docs/content/03-interfaces/02-relational.html

[^2_6]: https://docs.directus.io/app/data-model/collections

[^2_7]: https://github.com/directus/directus/discussions/12930

[^2_8]: https://journals.ums.ac.id/index.php/khif/article/view/11761

[^2_9]: https://pubs.aip.org/aip/acp/article/986103

[^2_10]: https://ejournal.uinsgd.ac.id/index.php/kjrt/article/view/159

[^2_11]: https://ojs.uma.ac.id/index.php/jite/article/view/3863

[^2_12]: https://docs.directus.io/self-hosted/config-options

[^2_13]: https://www.restack.io/docs/directus-knowledge-directus-sql-query-insights

[^2_14]: https://directus.io/docs/guides/connect/sdk

[^2_15]: https://docs.directus.io/guides/sdk/getting-started

[^2_16]: https://codereview.stackexchange.com/questions/174703/build-a-quiz-game-with-reactjs

[^2_17]: https://stackoverflow.com/questions/67072817/i-want-to-start-a-countdown-timerhourminutesecond-for-my-quiz-react-app-how

[^2_18]: https://www.usenix.org/system/files/cset19-paper_anwar.pdf

[^2_19]: https://github.com/mostafahamedbesher/React-Quiz-app

[^2_20]: https://subscription.packtpub.com/book/web-development/9781849519885/1/ch01lvl1sec20/quiz-security-should-know

[^2_21]: https://www.keepersecurity.com/blog/2024/05/17/security-question-and-answer-best-practices/

[^2_22]: https://www.screenconnect.com/blog/session-management-best-practices

[^2_23]: https://stytch.com/blog/session-management-best-practices/

[^2_24]: https://directus.io/docs/tutorials/getting-started/using-authentication-in-react

[^2_25]: https://cheatsheetseries.owasp.org/cheatsheets/Session_Management_Cheat_Sheet.html

[^2_26]: copilot-instructions.md

[^2_27]: https://journal.unisza.edu.my/jimk/index.php/jimk/article/view/569

[^2_28]: https://docs.directus.io/packages/@directus/sdk/schema/type-aliases/directuscollection

[^2_29]: https://www.restack.io/docs/directus-knowledge-directus-endpoint-types

[^2_30]: https://www.wisp.blog/blog/how-to-add-related-content-for-directus

[^2_31]: https://directus.io/docs/tutorials/tips-and-tricks/advanced-types-with-the-directus-sdk

[^2_32]: https://docs.directus.io/guides/real-time/chat/react

[^2_33]: https://directus.io/docs/tutorials/getting-started/implementing-live-preview-in-react

[^2_34]: https://github.com/gremo/react-directus

[^2_35]: https://stackoverflow.com/questions/56501078/randomizing-quiz-answers-fetched-from-a-rest-api

[^2_36]: https://www.ijmh.org/wp-content/uploads/papers/v5i9/I1315055921.pdf

[^2_37]: https://iopscience.iop.org/article/10.1088/1742-6596/1933/1/012008

[^2_38]: https://online-journals.org/index.php/i-jim/article/view/50705

[^2_39]: https://journal.ittelkom-pwt.ac.id/index.php/dinda/article/view/1584

[^2_40]: https://journals.ums.ac.id/khif/article/view/11761

[^2_41]: https://scispace.com/pdf/fisher-yates-shuffle-algorithm-for-randomization-math-exam-3uebub1h5x.pdf

[^2_42]: https://ojs.stmik-banjarbaru.ac.id/index.php/progresif/article/view/1168

[^2_43]: https://en.wikipedia.org/wiki/Fisher-Yates_shuffle

[^2_44]: https://dev.to/parthspan/optimizing-directus-performance-tips-for-fast-and-efficient-headless-cms-8kf

[^2_45]: https://www.quiz-maker.com/cp-np-cyber-security-awareness

[^2_46]: https://arxiv.org/abs/2406.08426

[^2_47]: https://arxiv.org/abs/2504.06473

[^2_48]: https://dl.acm.org/doi/10.1145/3534678.3539294

[^2_49]: https://www.semanticscholar.org/paper/1637c6b1db09a18c5a96b0da8be599e66bec6ace

[^2_50]: https://ieeexplore.ieee.org/document/9936580/

[^2_51]: https://arxiv.org/abs/2310.13575

[^2_52]: https://www.ijeast.com/papers/39-43, Tesma0911,IJEAST.pdf

[^2_53]: https://docs.directus.io/app/data-model

[^2_54]: https://github.com/directus/api/issues/1924

[^2_55]: https://stackoverflow.com/questions/60322703/how-can-i-edit-existing-mysql-tables-with-directus-cms

[^2_56]: https://github.com/bcc-code/directus-schema-sync

[^2_57]: https://directus.io/docs/api/schema

[^2_58]: https://mostaql.com/portfolio/1976327-examination-system-database

[^2_59]: https://www.bytebase.com/blog/top-database-schema-design-best-practices/

[^2_60]: https://www.restack.io/docs/directus-knowledge-restful-endpoints-directus

[^2_61]: https://severalnines.com/blog/understanding-postgresql-architecture/

[^2_62]: https://eurasiaconferences.com/proceedings/abstracts-of-the-5th-world-conference-on-arts-humanities-social-sciences-and-education/abstracts/345

[^2_63]: https://onlinelibrary.wiley.com/doi/10.1002/bit.27881

[^2_64]: https://www.emjreviews.com/dermatology/congress-review/updates-on-best-practices-for-onychomycosis-hitting-the-nail-on-the-head-j030122/

[^2_65]: https://onepetro.org/SPENAIC/proceedings/24NAIC/24NAIC/D021S011R001/547835

[^2_66]: http://peer.asee.org/49344

[^2_67]: https://quizlet.com/724807605/session-management-flash-cards/

[^2_68]: http://www.instruction.uh.edu/wp-content/uploads/knowledgeBase/Assmt-Best-Practices.pdf

[^2_69]: https://docs.moodle.org/403/en/Effective_quiz_practices

[^2_70]: https://csitjournal.khmnu.edu.ua/index.php/csit/article/view/192

[^2_71]: https://github.com/directus/directus/discussions/23147

[^2_72]: https://docs.directus.io/blog/getting-started-with-directus-and-remix

[^2_73]: https://www.sitepoint.com/community/t/cant-save-data-from-directus-in-react-state/449624

[^2_74]: https://www.codementor.io/@jignanmer/data-and-database-management-with-postgresql-assignment-23qun9dppo

[^2_75]: https://www.youtube.com/watch?v=UX5HIrxbRUc

[^2_76]: https://react.dev/learn/managing-state

[^2_77]: https://www.codecademy.com/learn/decp-sql-fundamentals-for-data-engineers/modules/designing-a-database-schema/cheatsheet

[^2_78]: https://github.com/directus/directus/discussions/5434

[^2_79]: https://github.com/directus/directus/discussions/4791

[^2_80]: https://www.semanticscholar.org/paper/89af4c2883a113b1bb3e096d4e0a5797ad5f1cf8

[^2_81]: https://ejournal.nusamandiri.ac.id/index.php/jitk/article/view/5256

[^2_82]: https://pubs.aip.org/aip/acp/article-pdf/doi/10.1063/5.0042534/13150082/060015_1_online.pdf

[^2_83]: https://www.semanticscholar.org/paper/604537f3ce48811c708a6d902681a9c436acd0a5

[^2_84]: http://link.springer.com/10.1007/978-3-540-76631-5_116

[^2_85]: https://link.springer.com/10.1007/978-3-642-38070-9_5

[^2_86]: https://www.npmjs.com/package/@directus/schema

[^2_87]: https://linkinghub.elsevier.com/retrieve/pii/S1353485803006111

[^2_88]: https://www.semanticscholar.org/paper/810d9be35a95963852450aba59f2ac89936ed12b

[^2_89]: https://www.semanticscholar.org/paper/70fdbcf8a106669704b43d945cd8e5e30e612b56

[^2_90]: https://www.semanticscholar.org/paper/f81deb14671017725b9bc31f9e13560d74a638c1

[^2_91]: https://link.springer.com/10.1007/s12098-021-03689-6

[^2_92]: https://support.exam.net/s/article/security-settings-in-exams?language=en_US

[^2_93]: http://ieeexplore.ieee.org/document/6613827/

[^2_94]: https://www.semanticscholar.org/paper/b7ec8ca25e7946c3b07adad7e3575d1a0d6c996b

[^2_95]: https://www.semanticscholar.org/paper/8bfe003bc777c96217a09114cc79a75cc692e562

[^2_96]: https://dl.acm.org/doi/10.1145/1570433.1570434

[^2_97]: https://www.semanticscholar.org/paper/0170c2746dde3ed5bff4c2c1b0ccb3e0037cfe9e

[^2_98]: http://link.springer.com/10.1007/978-3-319-48992-6

[^2_99]: https://www.semanticscholar.org/paper/c4cd1054b59f3c57777fabfa3edf9e65f8a10dea

[^2_100]: https://www.semanticscholar.org/paper/b843c736422d1d95c10d9bde76018e4d6aa7e056

[^2_101]: https://directus.io/docs/guides/connect/query-parameters


---

# oh iya tambahkan tech stack untuk animasinya dengan framer motion 11 ya dan bagaimana implementasinya ke sistem dan kalau sudah semua buatkan PRDnya dalam bentuk file markdown yg bisa di download dan dipindahkan ke folder project untuk dijadikan panduan projectnya

**SOLUSI LENGKAP UNTUK IMPLEMENTASI FRAMER MOTION V11 DAN PRD MARKDOWN**

---

### **1. Update Tech Stack dengan Framer Motion 11**

**Frontend:**

```markdown
- React 18+
- Tailwind CSS 3.3+
- HeroUI 2.1+
- Framer Motion 11.0.2 (dengan React 18+ compatibility)
```

**Backend:**

```markdown
- Directus 10.8+ (Headless CMS)
- PostgreSQL 15+
- Node.js 18+
```


---

### **2. Implementasi Framer Motion v11**

#### **Instalasi**

```bash
npm install motion@11.0.2
```


#### **Contoh Implementasi Animasi**

```jsx
// components/AnimatedCard.jsx
import { motion } from "motion/react"

export function AnimatedCard({ children }) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      whileHover={{ scale: 1.05 }}
      className="p-4 bg-white rounded-lg shadow-md"
    >
      {children}
    </motion.div>
  )
}
```


#### **Animasi Kompleks dengan Variants**

```jsx
// components/GamificationBadge.jsx
const badgeVariants = {
  initial: { scale: 0, rotate: -45 },
  animate: { 
    scale: 1, 
    rotate: 0,
    transition: { type: "spring", stiffness: 260, damping: 20 }
  }
}

export function GamificationBadge({ type }) {
  return (
    <motion.div
      variants={badgeVariants}
      initial="initial"
      animate="animate"
      className="badge-gold"
    >
      <Icon type={type} />
    </motion.div>
  )
}
```


---

### **3. PRD (Product Requirements Document) dalam Markdown**

**Buat file `PRD.md` di root project:**

```markdown
# Product Requirements Document - Sistem Administrasi Guru

## **1. Executive Summary**
Pengembangan platform administrasi guru terintegrasi dengan fitur manajemen kelas, penilaian, gamifikasi, dan bank soal.

## **2. Objectives**
- Digitalisasi proses administrasi sekolah
- Meningkatkan engagement siswa melalui gamifikasi
- Sentralisasi data pendidikan

## **3. Scope**
### **Fitur Utama**
| Modul          | Deskripsi                                                                 |
|----------------|---------------------------------------------------------------------------|
| Manajemen Kelas| CRUD data kelas dengan drag & drop interface                             |
| Gamifikasi     | Sistem XP, badge, dan leaderboard real-time                              |
| Bank Soal      | Generator ujian otomatis dengan algoritma randomisasi soal               |

## **4. Technical Requirements**
### **Frontend**
```

{
"dependencies": {
"motion": "^11.0.2",
"react": "^18.2.0",
"directus-sdk": "^10.8.0"
}
}

```

### **Backend**
```

database:
engine: postgresql
version: 15
services:

- directus:10.8

```

## **5. User Stories**
```


### **EPIC: Gamifikasi**

- **US01:** Sebagai guru, saya ingin memberikan badge kepada siswa yang mencapai target nilai
- **US02:** Sebagai siswa, saya ingin melihat peringkat saya di leaderboard kelas


### **EPIC: Bank Soal**

- **US03:** Sebagai admin, saya ingin upload soal dalam format .docx dengan auto-parser

```

## **6. Success Metrics**
```

- Pengurangan waktu administrasi guru sebesar 40%
- Peningkatan partisipasi siswa sebesar 25% dalam 3 bulan

```

## **7. Deployment Plan**
```

graph TD
A[Setup PostgreSQL] --> B[Install Directus]
B --> C[Konfigurasi Roles/Permissions]
C --> D[Deploy Frontend]

```

## **8. Referensi**
- [Framer Motion v11 Docs](https://motion.dev/docs/react-upgrade-guide)
- [Directus API Spec](https://docs.directus.io/reference/introduction/)
```


---

### **4. Struktur Folder untuk Animasi**

```
src/
  /animations
    /variants
      fadeIn.js
      slideUp.js
    /hooks
      useScrollAnimation.js
```


---

### **5. Contoh Implementasi Lengkap**

```jsx
// hooks/useScrollAnimation.js
import { useAnimation } from "motion/react"
import { useInView } from "react-intersection-observer"

export function useScrollAnimation() {
  const controls = useAnimation()
  const [ref, inView] = useInView()

  React.useEffect(() => {
    if (inView) controls.start("visible")
  }, [controls, inView])

  return [ref, controls]
}
```


---

**INSTRUKSI PENGGUNAAN:**

1. Download file `PRD.md`
2. Letakkan di root folder project
3. Jalankan `npm install` untuk dependency baru
4. Implementasi komponen animasi sesuai contoh

Dokumen ini menyediakan panduan lengkap mulai dari technical spec hingga product roadmap untuk tim pengembang.

<div style="text-align: center">⁂</div>

[^3_1]: http://itiis.org/digital-library/manuscript/1619

[^3_2]: https://www.emerald.com/insight/content/doi/10.1108/RIA-01-2023-0008/full/html

[^3_3]: https://ieeexplore.ieee.org/document/8857597/

[^3_4]: https://www.mdpi.com/2075-5309/15/6/990

[^3_5]: https://ieeexplore.ieee.org/document/8365810/

[^3_6]: https://arxiv.org/abs/2403.08580

[^3_7]: https://ieeexplore.ieee.org/document/9561290/

[^3_8]: https://ieeexplore.ieee.org/document/10901954/

[^3_9]: https://motion.dev/docs/react-upgrade-guide

[^3_10]: https://www.youtube.com/watch?v=sClYgoPOpaI

[^3_11]: https://blog.logrocket.com/creating-react-animations-with-motion/

[^3_12]: https://blog.maximeheckel.com/posts/advanced-animation-patterns-with-framer-motion/

[^3_13]: https://motion.mighty.guide/api-overview/

[^3_14]: https://www.framer.com/motion/examples/?via=cptv8

[^3_15]: https://convertai.framer.website/docs/creating-animations

[^3_16]: https://www.framer.com/motion/guide-upgrade/?srsltid=AfmBOopaYB-BOR-y9GDz4pGNsIeBB-JrR6pBCEY2_-EB5rz5X4PETIKb

[^3_17]: https://dev.to/aniruddhaadak/framer-motion-smooth-animations-for-react-apps-44ho

[^3_18]: https://www.youtube.com/watch?v=WpD5oJ2PrlM

[^3_19]: https://www.framer.com/motion/introduction/?srsltid=AfmBOookFVtmo4a8dOmozclc1pB8wnXIi9K0v-0fPt1IcSLAkbpesfbB

[^3_20]: https://www.skypack.dev/view/framer-motion

[^3_21]: http://doi.wiley.com/10.1118/1.4925238

[^3_22]: http://ieeexplore.ieee.org/document/8050295/

[^3_23]: https://arxiv.org/abs/2411.14505

[^3_24]: https://ieeexplore.ieee.org/document/8579164/

[^3_25]: https://www.semanticscholar.org/paper/1fbed96bf4cf38cd455363697eba4797133a8f6f

[^3_26]: https://www.npmjs.com/package/framer-motion

[^3_27]: https://andrejgajdos.com/orchestrating-animations-with-framer-motion-in-react-js/

[^3_28]: https://www.framer.com/motion/examples/?srsltid=AfmBOorwooG2CDfmYva_th0o-UDzCT-ZSQ876WNO2-QhWDTeQ1OGSIi1

[^3_29]: https://www.udemy.com/course/animating-react-apps-with-framer-motion/

[^3_30]: https://namastedev.com/blog/creating-animated-ui-with-framer-motion-4/

[^3_31]: https://app.studyraid.com/en/read/7850/206037/setting-up-framer-motion-in-a-react-project

[^3_32]: https://motion.mighty.guide

[^3_33]: https://www.framer.com/motion/examples/?srsltid=AfmBOoqkdGGf-qizJgZ44iSgipnetebmYzZhOEeSxZmA-JD-r1oa1fQM

[^3_34]: https://pmc.ncbi.nlm.nih.gov/articles/PMC8596077/

[^3_35]: https://pmc.ncbi.nlm.nih.gov/articles/PMC11973368/

[^3_36]: https://pmc.ncbi.nlm.nih.gov/articles/PMC9985101/

[^3_37]: https://pmc.ncbi.nlm.nih.gov/articles/PMC9952072/

[^3_38]: https://pmc.ncbi.nlm.nih.gov/articles/PMC9777465/

[^3_39]: https://pmc.ncbi.nlm.nih.gov/articles/PMC9962138/

[^3_40]: https://pmc.ncbi.nlm.nih.gov/articles/PMC7344778/

[^3_41]: https://pmc.ncbi.nlm.nih.gov/articles/PMC9880393/

[^3_42]: https://newsletter.frontend.fyi/posts/start-learning-framer-motion-today-my-course-just-went-live

[^3_43]: https://www.framer.com/blog/website-animation-examples/

[^3_44]: https://www.frontendmentor.io/solutions/react-js-game-styledcomponents-framer-motion-DhbuQWW-h

[^3_45]: https://flearningstudio.com/gamified-elearning-examples/

[^3_46]: https://www.ijiet.org/vol15/IJIET-V15N1-2217.pdf

[^3_47]: https://stackoverflow.com/questions/67723764/can-i-create-a-blob-animation-using-framer-motion

[^3_48]: https://www.framer.com/marketplace/templates/icourses/

[^3_49]: https://blog.stackademic.com/how-to-create-a-scroll-animation-in-react-with-the-framer-motion-library-354f7569de3e?gi=86ab05828e98

[^3_50]: https://www.emerald.com/insight/content/doi/10.1108/ITSE-12-2017-0069/full/html

[^3_51]: https://www.mdpi.com/2076-3417/15/7/3502

[^3_52]: https://www.acjournal.ru/jour/article/view/2591

[^3_53]: https://meridian.allenpress.com/jfwm/article/15/1/251/501496/A-Reproducible-Manuscript-Workflow-With-a-Quarto

[^3_54]: https://psyjournals.ru/en/journals/mda/archive/2024_n2/Gudkova

[^3_55]: https://dl.acm.org/doi/10.1145/3639474.3640057

[^3_56]: https://www.sae.org/content/2025-01-8658

[^3_57]: https://www.spiedigitallibrary.org/conference-proceedings-of-spie/13099/3018260/CUBES-the-next-generation-spectrograph-for-VLT--a-comprehensive/10.1117/12.3018260.full

[^3_58]: https://www.unwe.bg/doi/alternativi/2025.1/ISA.2025.1.07.pdf

[^3_59]: https://productschool.com/blog/product-strategy/product-template-requirements-document-prd

[^3_60]: https://www.notion.com/templates/collections/best-prd-product-requirements-doc-templates-for-product-managers

[^3_61]: https://www.figma.com/templates/prd-template/

[^3_62]: https://airfocus.com/templates/product-requirements-document/

[^3_63]: https://blogs.glowscotland.org.uk/ab/public/sali/uploads/sites/1389/2018/04/Draft-PRD-Policy-v6.pdf

[^3_64]: https://miro.com/miroverse/project-gamification-with-ai-assistance/

[^3_65]: https://technicalwritingmp.com/docs/markdown-best-practices/

[^3_66]: https://www.pendo.io/product-led/artifacts/product-requirements-document-prd-template/

[^3_67]: http://gtcsnew.gtcs.org.uk/web/FILES/professional-development/gui-si-operational-guidelines-for-prd-of-teaching-staff.pdf

[^3_68]: https://pmc.ncbi.nlm.nih.gov/articles/PMC9244108/

[^3_69]: https://arxiv.org/pdf/1903.00438.pdf

[^3_70]: https://arxiv.org/pdf/2502.04103.pdf

[^3_71]: https://pmc.ncbi.nlm.nih.gov/articles/PMC7822761/

[^3_72]: https://arxiv.org/abs/2412.06793

[^3_73]: https://arxiv.org/pdf/1312.1824.pdf

[^3_74]: https://pmc.ncbi.nlm.nih.gov/articles/PMC10328315/

[^3_75]: https://ijari.publicascientificsolution.com/index.php/rv/article/view/40

[^3_76]: https://yellowslice.in/bed/the-impact-of-edtech-ux-design/

[^3_77]: https://pixune.com/blog/animation-in-education/

[^3_78]: https://visionarycios.com/animation-in-the-edtech-industry/

[^3_79]: https://www.linkedin.com/pulse/role-animation-expanding-e-learning-platforms-bestanimationstudios-rhwac

[^3_80]: https://dev3lop.com/tag/animated-dashboards/

[^3_81]: https://app.studyraid.com/en/read/11439/358428/animation-performance-best-practices

[^3_82]: https://www.webdesign-inspiration.com/article/microinteractions-in-e-learning-enhancing-user-engagement-through-small-design-details/

[^3_83]: https://floatui.com/blog/ui-animation-feedback-8-best-practices

[^3_84]: https://www.semanticscholar.org/paper/88d55c8e7daddb0dc681494a934465b9d6630148

[^3_85]: https://ieeexplore.ieee.org/document/9051239/

[^3_86]: http://ieeexplore.ieee.org/document/6572236/

[^3_87]: https://www.semanticscholar.org/paper/9364b48b83244e432d970414cb3659cd0d0a23da

[^3_88]: https://www.semanticscholar.org/paper/8d115f675b78d25de2692631fbb14a59ba9371c8

[^3_89]: https://link.springer.com/10.1007/s42107-023-00831-x

[^3_90]: https://pmc.ncbi.nlm.nih.gov/articles/PMC7892912/

[^3_91]: https://pmc.ncbi.nlm.nih.gov/articles/PMC8715305/

[^3_92]: https://mdpi-res.com/bookfiles/book/7270/GameBased_Learning_and_Gamification_for_Education.pdf?v=1744291368

[^3_93]: https://www.semanticscholar.org/paper/958573a3f8c5dac4e9c9591fd3b5fc94b361b292

[^3_94]: https://journals.usm.ac.id/index.php/transformatika/article/view/5877

[^3_95]: https://www.reforge.com/artifacts/c/product-development/product-requirement-document-prd

[^3_96]: https://www.semanticscholar.org/paper/371782475d0858b7976c710d985bf4b8019fb3ed

[^3_97]: http://link.springer.com/10.1007/s10055-019-00394-w

[^3_98]: https://www.semanticscholar.org/paper/4f348e18b026259076cce64bc49f343482770f72

[^3_99]: https://www.vyond.com/blog/how-to-make-education-more-effective-with-elearning-animation/

