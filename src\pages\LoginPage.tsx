// Updated: Login page for Guru Digital Pelangi
import React from 'react';
import { LoginForm } from '../components/auth/LoginForm';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';

const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuthStore();

  // Redirect if already authenticated
  React.useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  const handleLoginSuccess = () => {
    console.log('🔐 LoginPage: Login success, navigating to dashboard...');
    navigate('/dashboard');
  };

  return (
    <LoginForm onSuccess={handleLoginSuccess} />
  );
};

export default LoginPage;
