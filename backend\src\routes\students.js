// Students Routes
import express from 'express';
import {
  getStudents,
  getStudentById,
  createStudent,
  updateStudent,
  deleteStudent,
  getStudentGrades,
  getStudentAttendance
} from '../controllers/studentController.js';
import { authenticateToken, adminAndGuru } from '../middleware/auth.js';
import { validateStudent } from '../middleware/validation.js';

const router = express.Router();

// Semua routes memerlukan authentication
router.use(authenticateToken);

// GET /api/students - Get all students
router.get('/', getStudents);

// GET /api/students/:id - Get student by ID
router.get('/:id', getStudentById);

// GET /api/students/:id/grades - Get student grades
router.get('/:id/grades', getStudentGrades);

// GET /api/students/:id/attendance - Get student attendance
router.get('/:id/attendance', getStudentAttendance);

// POST /api/students - Create new student
router.post('/', adminAndGuru, validateStudent, createStudent);

// PUT /api/students/:id - Update student
router.put('/:id', adminAndGuru, validateStudent, updateStudent);

// DELETE /api/students/:id - Delete student
router.delete('/:id', adminAndGuru, deleteStudent);

export default router;
