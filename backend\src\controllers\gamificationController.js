// Gamification Controller for Guru Digital Pelangi
// Handles gamification settings, XP, levels, and achievements
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Get gamification settings (Admin only)
 * GET /api/gamification/settings
 */
export const getGamificationSettings = async (req, res) => {
  try {
    const settings = await prisma.gamificationSettings.findMany({
      orderBy: { createdAt: 'desc' }
    });

    res.json({
      success: true,
      data: { settings }
    });

  } catch (error) {
    console.error('Get gamification settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat mengambil pengaturan gamifikasi'
    });
  }
};

/**
 * Create gamification settings (Admin only)
 * POST /api/gamification/settings
 */
export const createGamificationSettings = async (req, res) => {
  try {
    const { 
      name, 
      description, 
      xpPerGrade, 
      xpAttendanceBonus, 
      xpAbsentPenalty, 
      levelThresholds 
    } = req.body;

    // Deactivate other settings if this is set as active
    if (req.body.isActive) {
      await prisma.gamificationSettings.updateMany({
        where: { isActive: true },
        data: { isActive: false }
      });
    }

    const settings = await prisma.gamificationSettings.create({
      data: {
        name,
        description,
        xpPerGrade: parseInt(xpPerGrade),
        xpAttendanceBonus: parseInt(xpAttendanceBonus),
        xpAbsentPenalty: parseInt(xpAbsentPenalty),
        levelThresholds,
        isActive: req.body.isActive || false
      }
    });

    res.status(201).json({
      success: true,
      message: 'Pengaturan gamifikasi berhasil dibuat',
      data: { settings }
    });

  } catch (error) {
    console.error('Create gamification settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat membuat pengaturan gamifikasi'
    });
  }
};

/**
 * Update gamification settings (Admin only)
 * PUT /api/gamification/settings/:id
 */
export const updateGamificationSettings = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      name, 
      description, 
      xpPerGrade, 
      xpAttendanceBonus, 
      xpAbsentPenalty, 
      levelThresholds,
      isActive
    } = req.body;

    // Deactivate other settings if this is set as active
    if (isActive) {
      await prisma.gamificationSettings.updateMany({
        where: { 
          isActive: true,
          id: { not: id }
        },
        data: { isActive: false }
      });
    }

    const settings = await prisma.gamificationSettings.update({
      where: { id },
      data: {
        name,
        description,
        xpPerGrade: parseInt(xpPerGrade),
        xpAttendanceBonus: parseInt(xpAttendanceBonus),
        xpAbsentPenalty: parseInt(xpAbsentPenalty),
        levelThresholds,
        isActive
      }
    });

    res.json({
      success: true,
      message: 'Pengaturan gamifikasi berhasil diupdate',
      data: { settings }
    });

  } catch (error) {
    console.error('Update gamification settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat update pengaturan gamifikasi'
    });
  }
};

/**
 * Get student XP and level
 * GET /api/gamification/student/:studentId
 */
export const getStudentXp = async (req, res) => {
  try {
    const { studentId } = req.params;

    const studentXp = await prisma.studentXp.findUnique({
      where: { studentId },
      include: {
        student: {
          select: {
            id: true,
            fullName: true,
            studentId: true
          }
        }
      }
    });

    if (!studentXp) {
      // Create default XP record if not exists
      const newStudentXp = await prisma.studentXp.create({
        data: {
          studentId,
          totalXp: 0,
          level: 1,
          levelName: 'Pemula'
        },
        include: {
          student: {
            select: {
              id: true,
              fullName: true,
              studentId: true
            }
          }
        }
      });

      return res.json({
        success: true,
        data: { studentXp: newStudentXp }
      });
    }

    res.json({
      success: true,
      data: { studentXp }
    });

  } catch (error) {
    console.error('Get student XP error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat mengambil data XP siswa'
    });
  }
};

/**
 * Get class leaderboard
 * GET /api/gamification/leaderboard/:classId
 */
export const getClassLeaderboard = async (req, res) => {
  try {
    const { classId } = req.params;
    const { limit = 10 } = req.query;

    const students = await prisma.student.findMany({
      where: { classId },
      include: {
        studentXp: true
      },
      orderBy: {
        studentXp: {
          totalXp: 'desc'
        }
      },
      take: parseInt(limit)
    });

    const leaderboard = students.map((student, index) => ({
      rank: index + 1,
      student: {
        id: student.id,
        fullName: student.fullName,
        studentId: student.studentId
      },
      totalXp: student.studentXp?.totalXp || 0,
      level: student.studentXp?.level || 1,
      levelName: student.studentXp?.levelName || 'Pemula',
      attendanceStreak: student.studentXp?.attendanceStreak || 0,
      assignmentStreak: student.studentXp?.assignmentStreak || 0
    }));

    res.json({
      success: true,
      data: { leaderboard }
    });

  } catch (error) {
    console.error('Get class leaderboard error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat mengambil leaderboard kelas'
    });
  }
};

/**
 * Get student achievements
 * GET /api/gamification/achievements/:studentId
 */
export const getStudentAchievements = async (req, res) => {
  try {
    const { studentId } = req.params;

    const achievements = await prisma.studentAchievement.findMany({
      where: { studentId },
      orderBy: { earnedAt: 'desc' }
    });

    res.json({
      success: true,
      data: { achievements }
    });

  } catch (error) {
    console.error('Get student achievements error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat mengambil pencapaian siswa'
    });
  }
};

/**
 * Award achievement to student (Admin/Guru)
 * POST /api/gamification/achievements
 */
export const awardAchievement = async (req, res) => {
  try {
    const { studentId, type, title, description, xpReward, metadata } = req.body;

    // Check if achievement already exists
    const existingAchievement = await prisma.studentAchievement.findFirst({
      where: {
        studentId,
        type
      }
    });

    if (existingAchievement) {
      return res.status(400).json({
        success: false,
        message: 'Pencapaian ini sudah diberikan kepada siswa'
      });
    }

    const achievement = await prisma.studentAchievement.create({
      data: {
        studentId,
        type,
        title,
        description,
        xpReward: parseInt(xpReward),
        metadata
      }
    });

    // Add XP reward
    if (xpReward > 0) {
      await prisma.studentXp.upsert({
        where: { studentId },
        update: {
          totalXp: {
            increment: parseInt(xpReward)
          }
        },
        create: {
          studentId,
          totalXp: parseInt(xpReward)
        }
      });

      // Update level
      await updateStudentLevel(studentId);
    }

    res.status(201).json({
      success: true,
      message: 'Pencapaian berhasil diberikan',
      data: { achievement }
    });

  } catch (error) {
    console.error('Award achievement error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat memberikan pencapaian'
    });
  }
};

/**
 * Get all badges
 * GET /api/gamification/badges
 */
export const getBadges = async (req, res) => {
  try {
    const badges = await prisma.badge.findMany({
      where: { isActive: true },
      orderBy: { createdAt: 'desc' }
    });

    res.json({
      success: true,
      data: badges
    });

  } catch (error) {
    console.error('Get badges error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat mengambil data badges'
    });
  }
};

/**
 * Create new badge
 * POST /api/gamification/badges
 */
export const createBadge = async (req, res) => {
  try {
    const { name, description, icon, xpReward } = req.body;
    const schoolId = req.user.schoolId;

    const badge = await prisma.badge.create({
      data: {
        name,
        description,
        icon,
        xpReward: parseInt(xpReward),
        schoolId,
        isActive: true
      }
    });

    res.status(201).json({
      success: true,
      message: 'Badge berhasil dibuat',
      data: badge
    });

  } catch (error) {
    console.error('Create badge error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat membuat badge'
    });
  }
};

/**
 * Get all challenges
 * GET /api/gamification/challenges
 */
export const getChallenges = async (req, res) => {
  try {
    const challenges = await prisma.challenge.findMany({
      where: { isActive: true },
      orderBy: { createdAt: 'desc' }
    });

    res.json({
      success: true,
      data: challenges
    });

  } catch (error) {
    console.error('Get challenges error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat mengambil data challenges'
    });
  }
};

/**
 * Create new challenge
 * POST /api/gamification/challenges
 */
export const createChallenge = async (req, res) => {
  try {
    const { title, description, duration, targetType, xpReward } = req.body;
    const schoolId = req.user.schoolId;
    const createdBy = req.user.id;

    const startDate = new Date();
    const endDate = new Date();
    endDate.setDate(startDate.getDate() + parseInt(duration));

    const challenge = await prisma.challenge.create({
      data: {
        title,
        description,
        duration: parseInt(duration),
        targetType,
        xpReward: parseInt(xpReward),
        startDate,
        endDate,
        schoolId,
        createdBy,
        isActive: true
      }
    });

    res.status(201).json({
      success: true,
      message: 'Challenge berhasil dibuat',
      data: challenge
    });

  } catch (error) {
    console.error('Create challenge error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat membuat challenge'
    });
  }
};

/**
 * Give reward to student
 * POST /api/gamification/rewards
 */
export const giveReward = async (req, res) => {
  try {
    const { studentId, type, xpAmount, badgeId, description } = req.body;
    const awardedBy = req.user.id;

    if (type === 'xp') {
      // Give XP reward
      await prisma.studentXp.upsert({
        where: { studentId },
        update: {
          totalXp: {
            increment: parseInt(xpAmount)
          }
        },
        create: {
          studentId,
          totalXp: parseInt(xpAmount),
          level: 1,
          levelName: 'Pemula'
        }
      });

      // Update level
      await updateStudentLevel(studentId);

      res.json({
        success: true,
        message: `${xpAmount} XP berhasil diberikan`,
        data: { type: 'xp', amount: xpAmount }
      });

    } else if (type === 'badge') {
      // Give badge reward
      const badge = await prisma.badge.findUnique({
        where: { id: badgeId }
      });

      if (!badge) {
        return res.status(404).json({
          success: false,
          message: 'Badge tidak ditemukan'
        });
      }

      // Check if student already has this badge
      const existingBadge = await prisma.studentBadge.findFirst({
        where: {
          studentId,
          badgeId
        }
      });

      if (existingBadge) {
        return res.status(400).json({
          success: false,
          message: 'Siswa sudah memiliki badge ini'
        });
      }

      // Award badge
      await prisma.studentBadge.create({
        data: {
          studentId,
          badgeId,
          awardedBy,
          reason: description || 'Reward dari guru'
        }
      });

      // Give XP from badge
      if (badge.xpReward > 0) {
        await prisma.studentXp.upsert({
          where: { studentId },
          update: {
            totalXp: {
              increment: badge.xpReward
            }
          },
          create: {
            studentId,
            totalXp: badge.xpReward,
            level: 1,
            levelName: 'Pemula'
          }
        });

        await updateStudentLevel(studentId);
      }

      res.json({
        success: true,
        message: `Badge "${badge.name}" berhasil diberikan`,
        data: { type: 'badge', badge }
      });
    }

  } catch (error) {
    console.error('Give reward error:', error);
    res.status(500).json({
      success: false,
      message: 'Error saat memberikan reward'
    });
  }
};

/**
 * Helper function to update student level
 */
const updateStudentLevel = async (studentId) => {
  try {
    const studentXp = await prisma.studentXp.findUnique({
      where: { studentId }
    });

    if (!studentXp) return;

    const settings = await prisma.gamificationSettings.findFirst({
      where: { isActive: true }
    });

    if (!settings || !settings.levelThresholds) return;

    const thresholds = settings.levelThresholds;
    let newLevel = 1;
    let newLevelName = 'Pemula';

    // Find appropriate level based on total XP
    for (let i = 0; i < thresholds.length; i++) {
      if (studentXp.totalXp >= thresholds[i].xp) {
        newLevel = thresholds[i].level;
        newLevelName = thresholds[i].name;
      }
    }

    // Update level if changed
    if (newLevel !== studentXp.level) {
      await prisma.studentXp.update({
        where: { studentId },
        data: {
          level: newLevel,
          levelName: newLevelName
        }
      });
    }

  } catch (error) {
    console.error('Error updating student level:', error);
  }
};
