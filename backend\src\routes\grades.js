// Grades Routes
import express from 'express';
import {
  getGrades,
  getGradeById,
  createGrade,
  updateGrade,
  deleteGrade,
  getGradesByClass,
  getGradesByStudent
} from '../controllers/gradeController.js';
import { authenticateToken, adminAndGuru } from '../middleware/auth.js';
import { validateGrade } from '../middleware/validation.js';

const router = express.Router();

// Semua routes memerlukan authentication
router.use(authenticateToken);

// GET /api/grades - Get all grades
router.get('/', getGrades);

// GET /api/grades/:id - Get grade by ID
router.get('/:id', getGradeById);

// GET /api/grades/class/:classId - Get grades by class
router.get('/class/:classId', getGradesByClass);

// GET /api/grades/student/:studentId - Get grades by student
router.get('/student/:studentId', getGradesByStudent);

// POST /api/grades - Create new grade
router.post('/', adminAndGuru, validateGrade, createGrade);

// PUT /api/grades/:id - Update grade
router.put('/:id', adminAndGuru, validateGrade, updateGrade);

// DELETE /api/grades/:id - Delete grade
router.delete('/:id', adminAndGuru, deleteGrade);

export default router;
