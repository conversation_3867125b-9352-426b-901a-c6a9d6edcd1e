
// Updated: Comprehensive types for Guru Digital Pelangi admin system with Directus backend
// Core user and authentication types
export interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: 'admin' | 'guru' | 'siswa';
  avatar?: string;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

// School and class management types
export interface School {
  id: string;
  name: string;
  address: string;
  phone?: string;
  email?: string;
  created_at: string;
  updated_at: string;
}

export interface Class {
  id: string;
  name: string;
  grade_level: string;
  school_id: string;
  teacher_id: string;
  academic_year: string;
  student_count: number;
  created_at: string;
  updated_at: string;
  // Relations
  school?: School;
  teacher?: User;
  students?: Student[];
}

export interface Student {
  id: string;
  student_id: string; // NIS/NISN
  first_name: string;
  last_name: string;
  email?: string;
  class_id: string;
  date_of_birth?: string;
  gender: 'L' | 'P';
  address?: string;
  phone?: string;
  parent_name?: string;
  parent_phone?: string;
  status: 'active' | 'inactive' | 'graduated';
  created_at: string;
  updated_at: string;
  // Relations
  class?: Class;
  xp_data?: StudentXP;
  grades?: Grade[];
}

// Subject and academic types
export interface Subject {
  id: string;
  name: string;
  code: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface Grade {
  id: string;
  student_id: string;
  subject_id: string;
  class_id: string;
  grade_type: 'tugas' | 'quiz' | 'ujian' | 'praktik';
  score: number;
  max_score: number;
  description?: string;
  date: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  // Relations
  student?: Student;
  subject?: Subject;
  class?: Class;
  teacher?: User;
}

// Gamification types
export interface StudentXP {
  id: string;
  student_id: string;
  total_xp: number;
  level: number;
  level_name: 'Pemula' | 'Pelajar' | 'Cendekiawan' | 'Ahli' | 'Master';
  updated_at: string;
  // Relations
  student?: Student;
}

export interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  criteria: string;
  xp_reward: number;
  created_at: string;
  updated_at: string;
}

export interface StudentBadge {
  id: string;
  student_id: string;
  badge_id: string;
  earned_at: string;
  // Relations
  student?: Student;
  badge?: Badge;
}

export interface Leaderboard {
  id: string;
  student_id: string;
  class_id: string;
  rank: number;
  total_score: number;
  total_xp: number;
  updated_at: string;
  // Relations
  student?: Student;
  class?: Class;
}

// Learning plan (RPP) types
export interface LearningPlan {
  id: string;
  title: string;
  class_id: string;
  subject_id: string;
  objectives: string[];
  materials: string[];
  methods: string[];
  activities: LearningActivity[];
  assessments: string[];
  duration: number; // in minutes
  date: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  // Relations
  class?: Class;
  subject?: Subject;
  teacher?: User;
}

export interface LearningActivity {
  name: string;
  duration: number;
  description: string;
}

// Question bank types
export interface Question {
  id: string;
  question_text: string;
  question_type: 'multiple_choice' | 'essay' | 'true_false' | 'fill_blank';
  difficulty: 'easy' | 'medium' | 'hard';
  subject_id: string;
  category_id?: string;
  correct_answer: string;
  explanation?: string;
  tags: string[];
  created_by: string;
  created_at: string;
  updated_at: string;
  // Relations
  subject?: Subject;
  options?: QuestionOption[];
}

export interface QuestionOption {
  id: string;
  question_id: string;
  option_text: string;
  is_correct: boolean;
  order_index: number;
  created_at: string;
  updated_at: string;
}

export interface QuestionCategory {
  id: string;
  name: string;
  description?: string;
  subject_id: string;
  created_at: string;
  updated_at: string;
  // Relations
  subject?: Subject;
}

// Dashboard and statistics types
export interface DashboardStats {
  totalClasses: number;
  totalStudents: number;
  totalQuestions: number;
  averageGrade: number;
  activeExercises: number;
  topStudents: Student[];
  recentActivities: Activity[];
}

export interface Activity {
  id: string;
  type: 'grade' | 'xp' | 'badge' | 'question' | 'exercise';
  title: string;
  description: string;
  user_id: string;
  created_at: string;
  // Relations
  user?: User;
}

// API Response wrapper
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Directus Schema type for SDK
export interface DirectusSchema {
  users: User[];
  schools: School[];
  classes: Class[];
  students: Student[];
  subjects: Subject[];
  grades: Grade[];
  student_xp: StudentXP[];
  badges: Badge[];
  student_badges: StudentBadge[];
  leaderboard: Leaderboard[];
  learning_plans: LearningPlan[];
  questions: Question[];
  question_options: QuestionOption[];
  question_categories: QuestionCategory[];
  activities: Activity[];
}
