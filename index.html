
<!DOCTYPE html>
<html lang="id">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON> Guru - Sistem Administrasi Pembelajaran</title>
    <meta name="description" content="Sistem Administrasi Guru untuk <PERSON>" />
    <meta name="author" content="Kelas Guru" />

    <!-- Tailwind CSS 3.4 -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
      body {
        font-family: 'Inter', sans-serif;
      }
      
      .gradient-bg {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      
      .card-gradient-blue {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      }
      
      .card-gradient-green {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      }
      
      .card-gradient-orange {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
      }
      
      .card-gradient-red {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
      }
      
      .card-gradient-purple {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
      }
      
      .card-gradient-cyan {
        background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
      }
      
      .glass-effect {
        backdrop-filter: blur(16px);
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .sidebar-gradient {
        background: linear-gradient(180deg, #1e293b 0%, #334155 100%);
      }
    </style>
  </head>

  <body class="bg-gray-50">
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
