// Gamification Routes
import express from 'express';
import {
  getGamificationSettings,
  createGamificationSettings,
  updateGamificationSettings,
  getStudentXp,
  getClassLeaderboard,
  getStudentAchievements,
  awardAchievement
} from '../controllers/gamificationController.js';
import { authenticateToken, adminOnly, adminAndGuru } from '../middleware/auth.js';

const router = express.Router();

// Semua routes memerlukan authentication
router.use(authenticateToken);

// GET /api/gamification/settings - Get gamification settings (Admin only)
router.get('/settings', adminOnly, getGamificationSettings);

// POST /api/gamification/settings - Create gamification settings (Admin only)
router.post('/settings', adminOnly, createGamificationSettings);

// PUT /api/gamification/settings/:id - Update gamification settings (Admin only)
router.put('/settings/:id', adminOnly, updateGamificationSettings);

// GET /api/gamification/student/:studentId - Get student XP and level
router.get('/student/:studentId', getStudentXp);

// GET /api/gamification/leaderboard/:classId - Get class leaderboard
router.get('/leaderboard/:classId', getClassLeaderboard);

// GET /api/gamification/achievements/:studentId - Get student achievements
router.get('/achievements/:studentId', getStudentAchievements);

// POST /api/gamification/achievements - Award achievement to student
router.post('/achievements', adminAndGuru, awardAchievement);

export default router;
