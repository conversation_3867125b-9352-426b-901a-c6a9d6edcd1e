// Quick Setup Script for Guru Digital Pelangi
// Run with: node quick-setup.js

const { createDirectus, rest, authentication, createCollection, createField, createItem } = require('@directus/sdk');

// Configuration - UPDATE THESE VALUES
const DIRECTUS_URL = 'http://directus-u0ss4oosws88ckgo4swwg00o.************.sslip.io/'; // e.g., 'https://your-directus.com'
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_PASSWORD = 'AjiPuluco100%';

const directus = createDirectus(DIRECTUS_URL).with(rest()).with(authentication());

async function setupCollections() {
  try {
    console.log('🚀 Starting Directus setup...');
    
    // Login as admin
    console.log('🔐 Logging in...');
    await directus.login(ADMIN_EMAIL, ADMIN_PASSWORD);
    console.log('✅ Login successful!');

    // Collections to create
    const collections = [
      { name: 'schools', icon: 'school' },
      { name: 'subjects', icon: 'book' },
      { name: 'classes', icon: 'groups' },
      { name: 'students', icon: 'person' },
      { name: 'grades', icon: 'grade' },
      { name: 'student_xp', icon: 'star' },
      { name: 'badges', icon: 'military_tech' },
      { name: 'student_badges', icon: 'workspace_premium' },
      { name: 'learning_plans', icon: 'assignment' },
      { name: 'question_categories', icon: 'category' },
      { name: 'questions', icon: 'quiz' },
      { name: 'question_options', icon: 'radio_button_checked' },
      { name: 'activities', icon: 'timeline' }
    ];

    // Create collections
    for (const collection of collections) {
      try {
        console.log(`📁 Creating collection: ${collection.name}`);
        await directus.request(createCollection({
          collection: collection.name,
          meta: {
            icon: collection.icon,
            accountability: 'all'
          }
        }));
        console.log(`✅ Created: ${collection.name}`);
      } catch (error) {
        if (error.message.includes('already exists')) {
          console.log(`⚠️  Collection ${collection.name} already exists, skipping...`);
        } else {
          console.error(`❌ Error creating ${collection.name}:`, error.message);
        }
      }
    }

    // Create fields for schools
    console.log('📝 Creating fields for schools...');
    const schoolFields = [
      { field: 'name', type: 'string', meta: { required: true, interface: 'input' } },
      { field: 'address', type: 'text', meta: { interface: 'textarea' } },
      { field: 'phone', type: 'string', meta: { interface: 'input' } },
      { field: 'email', type: 'string', meta: { interface: 'input' } }
    ];

    for (const field of schoolFields) {
      try {
        await directus.request(createField('schools', field));
        console.log(`✅ Created field: schools.${field.field}`);
      } catch (error) {
        console.log(`⚠️  Field schools.${field.field} might already exist`);
      }
    }

    // Create fields for subjects
    console.log('📝 Creating fields for subjects...');
    const subjectFields = [
      { field: 'name', type: 'string', meta: { required: true, interface: 'input' } },
      { field: 'code', type: 'string', meta: { required: true, interface: 'input' } },
      { field: 'description', type: 'text', meta: { interface: 'textarea' } }
    ];

    for (const field of subjectFields) {
      try {
        await directus.request(createField('subjects', field));
        console.log(`✅ Created field: subjects.${field.field}`);
      } catch (error) {
        console.log(`⚠️  Field subjects.${field.field} might already exist`);
      }
    }

    // Insert sample data
    console.log('📊 Inserting sample data...');
    
    // Sample school
    try {
      await directus.request(createItem('schools', {
        name: 'SMA Digital Pelangi',
        address: 'Jl. Pendidikan No. 123, Jakarta',
        phone: '021-12345678',
        email: '<EMAIL>'
      }));
      console.log('✅ Sample school created');
    } catch (error) {
      console.log('⚠️  Sample school might already exist');
    }

    // Sample subjects
    const subjects = [
      { name: 'Matematika', code: 'MTK', description: 'Mata pelajaran Matematika' },
      { name: 'Bahasa Indonesia', code: 'BIN', description: 'Mata pelajaran Bahasa Indonesia' },
      { name: 'IPA', code: 'IPA', description: 'Ilmu Pengetahuan Alam' },
      { name: 'IPS', code: 'IPS', description: 'Ilmu Pengetahuan Sosial' },
      { name: 'Bahasa Inggris', code: 'ENG', description: 'Mata pelajaran Bahasa Inggris' }
    ];

    for (const subject of subjects) {
      try {
        await directus.request(createItem('subjects', subject));
        console.log(`✅ Created subject: ${subject.name}`);
      } catch (error) {
        console.log(`⚠️  Subject ${subject.name} might already exist`);
      }
    }

    console.log('🎉 Setup completed successfully!');
    console.log('');
    console.log('📋 Next steps:');
    console.log('1. Check your Directus admin panel');
    console.log('2. Complete the remaining fields manually if needed');
    console.log('3. Test the frontend application');
    console.log('');
    console.log('🔗 Access your Directus admin: ' + DIRECTUS_URL + '/admin');

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    console.log('');
    console.log('💡 Troubleshooting:');
    console.log('1. Check your DIRECTUS_URL, ADMIN_EMAIL, and ADMIN_PASSWORD');
    console.log('2. Make sure Directus is running and accessible');
    console.log('3. Verify admin credentials are correct');
  }
}

// Run setup
setupCollections();
