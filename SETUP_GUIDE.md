# 🚀 **GURU DIGITAL PELANGI - FULL STACK SETUP GUIDE**

Panduan lengkap untuk setup aplikasi Guru Digital Pelangi dengan Express + MySQL + React.

## 📋 **Prerequisites**

- **Bun** >= 1.0.0 (recommended) atau Node.js >= 18.0
- **MySQL** >= 8.0
- **Git**

## 🏗️ **ARCHITECTURE OVERVIEW**

```
guru-digital-pelangi/
├── frontend/          # React + Vite + HeroUI
├── backend/           # Express + Prisma + MySQL
└── docs/              # Documentation
```

---

## 🔧 **STEP 1: CLONE & SETUP PROJECT**

```bash
# Clone repository
git clone https://github.com/blinkihc/guru-digital-pelangi.git
cd guru-digital-pelangi

# Install frontend dependencies
bun install

# Install backend dependencies
cd backend
bun install
cd ..
```

---

## 🗄️ **STEP 2: DATABASE SETUP**

### **Create MySQL Database**
```bash
# Login to MySQL
mysql -u root -p

# Create database
CREATE DATABASE guru_digital_pelangi;
CREATE USER 'guru_user'@'localhost' IDENTIFIED BY 'guru_password';
GRANT ALL PRIVILEGES ON guru_digital_pelangi.* TO 'guru_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### **Configure Backend Environment**
```bash
# Copy environment file
cd backend
cp .env.example .env

# Edit .env file dengan database credentials
DATABASE_URL="mysql://blinkihc:39255471f76e90383731@alpha_guruku:3306/guru_digital_pelangi"
```

---

## ⚡ **STEP 3: BACKEND SETUP**

```bash
cd backend

# Generate Prisma client
bun run db:generate

# Run database migrations
bun run db:migrate

# Seed database dengan sample data
bun run db:seed

# Start backend server
bun run dev
```

**Backend akan berjalan di**: http://localhost:5000

### **✅ Test Backend**
```bash
# Health check
curl http://localhost:5000/health

# Test login
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"identifier":"<EMAIL>","password":"admin123"}'
```

---

## 🎨 **STEP 4: FRONTEND SETUP**

```bash
# Kembali ke root directory
cd ..

# Configure frontend environment
cp .env.example .env

# Edit .env file
VITE_API_URL=http://localhost:5000/api
VITE_DEMO_MODE=false

# Start frontend development server
bun run dev
```

**Frontend akan berjalan di**: http://localhost:5173

---

## 🔐 **STEP 5: TEST LOGIN**

### **Sample Credentials:**

#### **👨‍💼 Admin**
- **Email**: <EMAIL>
- **Password**: admin123

#### **👨‍🏫 Guru**
- **Email**: <EMAIL>
- **Password**: guru123

#### **👨‍🎓 Siswa**
- **NISN**: **********
- **Password**: **********

---

## 🧪 **STEP 6: TESTING FEATURES**

### **Test CRUD Operations:**
1. **Login** dengan credentials admin/guru
2. **Navigate** ke menu Kelas
3. **Create** kelas baru
4. **Navigate** ke menu Siswa
5. **Create** siswa baru
6. **Navigate** ke menu Presensi
7. **Create** presensi untuk siswa

### **Test API Endpoints:**
```bash
# Get classes (need auth token)
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:5000/api/classes

# Get students
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:5000/api/students

# Get attendance
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:5000/api/attendance
```

---

## 🔄 **DEVELOPMENT WORKFLOW**

### **Backend Development:**
```bash
cd backend

# Development with hot reload
bun run dev

# Database operations
bun run db:studio      # Open Prisma Studio
bun run db:migrate     # Run new migrations
bun run db:reset       # Reset database (careful!)

# Production
bun run start
```

### **Frontend Development:**
```bash
# Development server
bun run dev

# Build for production
bun run build

# Preview production build
bun run preview
```

---

## 🚀 **PRODUCTION DEPLOYMENT**

### **Backend (VPS):**
```bash
# Install PM2
npm install -g pm2

# Start backend with PM2
cd backend
pm2 start ecosystem.config.js

# Setup nginx reverse proxy
# Point to localhost:5000
```

### **Frontend (Vercel/Netlify):**
```bash
# Build frontend
bun run build

# Deploy dist/ folder to hosting
```

---

## 🐛 **TROUBLESHOOTING**

### **Database Connection Error:**
```bash
# Check MySQL service
sudo systemctl status mysql

# Check database exists
mysql -u root -p -e "SHOW DATABASES;"

# Verify credentials in .env
```

### **Backend Port Error:**
```bash
# Change port in backend/.env
PORT=5001

# Update frontend .env
VITE_API_URL=http://localhost:5001/api
```

### **Frontend API Error:**
```bash
# Check backend is running
curl http://localhost:5000/health

# Check CORS settings in backend
# Verify FRONTEND_URL in backend/.env
```

### **Prisma Issues:**
```bash
cd backend

# Regenerate client
bun run db:generate

# Reset database
bun run db:reset
```

---

## 📚 **AVAILABLE SCRIPTS**

### **Frontend:**
- `bun run dev` - Development server
- `bun run build` - Production build
- `bun run preview` - Preview build
- `bun run lint` - Lint code

### **Backend:**
- `bun run dev` - Development server
- `bun run start` - Production server
- `bun run db:generate` - Generate Prisma client
- `bun run db:migrate` - Run migrations
- `bun run db:seed` - Seed database
- `bun run db:studio` - Open Prisma Studio

---

## 🎯 **NEXT STEPS**

1. **✅ Setup Complete** - Backend + Frontend running
2. **🧪 Test Features** - CRUD operations working
3. **🎮 Gamifikasi** - Implement XP system (HARI 3)
4. **📚 RPP Module** - Learning plans feature
5. **❓ Bank Soal** - Question bank system
6. **🚀 Production** - Deploy to VPS

---

## 📞 **SUPPORT**

Jika ada masalah:
1. **Check logs**: Backend console & browser console
2. **Verify environment**: Database connection & API URL
3. **Test endpoints**: Use curl atau Postman
4. **Reset database**: `bun run db:reset` (last resort)

**Happy Coding! 🎉**
