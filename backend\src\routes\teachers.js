// Routes untuk manajemen guru (Admin only)
import express from 'express';
import bcrypt from 'bcryptjs';
import { PrismaClient } from '@prisma/client';
import { authenticateToken, requireRole } from '../middleware/auth.js';

const router = express.Router();
const prisma = new PrismaClient();

// Middleware: hanya admin yang bisa akses
router.use(authenticateToken);
router.use(requireRole(['ADMIN']));

// GET /api/teachers - Get all teachers
router.get('/', async (req, res) => {
  try {
    const { search, status } = req.query;
    
    const where = {
      role: 'GURU',
      ...(search && {
        OR: [
          { firstName: { contains: search } },
          { lastName: { contains: search } },
          { email: { contains: search } },
          { nip: { contains: search } }
        ]
      }),
      ...(status && { status })
    };

    const teachers = await prisma.user.findMany({
      where,
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        nip: true,
        status: true,
        createdAt: true,
        classTeachers: {
          include: {
            class: {
              include: {
                subject: true
              }
            }
          }
        },
        _count: {
          select: {
            classTeachers: true,
            grades: true
          }
        }
      },
      orderBy: [
        { firstName: 'asc' },
        { lastName: 'asc' }
      ]
    });

    // Transform data untuk menambahkan fullName dan classes info
    const transformedTeachers = teachers.map(teacher => ({
      ...teacher,
      fullName: `${teacher.firstName} ${teacher.lastName}`,
      classes: teacher.classTeachers.map(ct => ({
        id: ct.class.id,
        name: ct.class.name,
        subject: ct.class.subject?.name || 'Unknown'
      })),
      totalClasses: teacher._count.classTeachers,
      totalGrades: teacher._count.grades
    }));

    res.json({
      success: true,
      data: transformedTeachers
    });
  } catch (error) {
    console.error('Error fetching teachers:', error);
    res.status(500).json({
      success: false,
      message: 'Gagal mengambil data guru'
    });
  }
});

// GET /api/teachers/:id - Get teacher by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const teacher = await prisma.user.findUnique({
      where: { 
        id,
        role: 'GURU'
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        nip: true,
        status: true,
        createdAt: true,
        updatedAt: true,
        classTeachers: {
          include: {
            class: {
              include: {
                subject: true,
                students: true
              }
            }
          }
        },
        grades: {
          include: {
            student: true,
            subject: true,
            class: true
          },
          orderBy: { createdAt: 'desc' },
          take: 10
        }
      }
    });

    if (!teacher) {
      return res.status(404).json({
        success: false,
        message: 'Guru tidak ditemukan'
      });
    }

    // Transform data
    const transformedTeacher = {
      ...teacher,
      fullName: `${teacher.firstName} ${teacher.lastName}`,
      classes: teacher.classTeachers.map(ct => ({
        id: ct.class.id,
        name: ct.class.name,
        subject: ct.class.subject?.name || 'Unknown',
        studentCount: ct.class.students.length
      })),
      recentGrades: teacher.grades
    };

    res.json({
      success: true,
      data: transformedTeacher
    });
  } catch (error) {
    console.error('Error fetching teacher:', error);
    res.status(500).json({
      success: false,
      message: 'Gagal mengambil data guru'
    });
  }
});

// POST /api/teachers - Create new teacher
router.post('/', async (req, res) => {
  try {
    const { firstName, lastName, email, nip, password } = req.body;

    // Validation
    if (!firstName || !lastName || !email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Nama depan, nama belakang, email, dan password wajib diisi'
      });
    }

    // Check if email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'Email sudah digunakan'
      });
    }

    // Check if NIP already exists (if provided)
    if (nip) {
      const existingNip = await prisma.user.findUnique({
        where: { nip }
      });

      if (existingNip) {
        return res.status(400).json({
          success: false,
          message: 'NIP sudah digunakan'
        });
      }
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    const teacher = await prisma.user.create({
      data: {
        firstName,
        lastName,
        email,
        nip,
        password: hashedPassword,
        role: 'GURU',
        status: 'ACTIVE'
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        nip: true,
        status: true,
        createdAt: true
      }
    });

    res.status(201).json({
      success: true,
      message: 'Guru berhasil dibuat',
      data: {
        ...teacher,
        fullName: `${teacher.firstName} ${teacher.lastName}`
      }
    });
  } catch (error) {
    console.error('Error creating teacher:', error);
    res.status(500).json({
      success: false,
      message: 'Gagal membuat akun guru'
    });
  }
});

// PUT /api/teachers/:id - Update teacher
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { firstName, lastName, email, nip, status, password } = req.body;

    // Check if teacher exists
    const existingTeacher = await prisma.user.findUnique({
      where: { 
        id,
        role: 'GURU'
      }
    });

    if (!existingTeacher) {
      return res.status(404).json({
        success: false,
        message: 'Guru tidak ditemukan'
      });
    }

    // Check email uniqueness (if being changed)
    if (email && email !== existingTeacher.email) {
      const emailExists = await prisma.user.findUnique({
        where: { email }
      });

      if (emailExists) {
        return res.status(400).json({
          success: false,
          message: 'Email sudah digunakan'
        });
      }
    }

    // Check NIP uniqueness (if being changed)
    if (nip && nip !== existingTeacher.nip) {
      const nipExists = await prisma.user.findUnique({
        where: { nip }
      });

      if (nipExists) {
        return res.status(400).json({
          success: false,
          message: 'NIP sudah digunakan'
        });
      }
    }

    // Prepare update data
    const updateData = {
      ...(firstName && { firstName }),
      ...(lastName && { lastName }),
      ...(email && { email }),
      ...(nip !== undefined && { nip }),
      ...(status && { status })
    };

    // Hash new password if provided
    if (password) {
      updateData.password = await bcrypt.hash(password, 12);
    }

    const teacher = await prisma.user.update({
      where: { id },
      data: updateData,
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        nip: true,
        status: true,
        updatedAt: true
      }
    });

    res.json({
      success: true,
      message: 'Data guru berhasil diperbarui',
      data: {
        ...teacher,
        fullName: `${teacher.firstName} ${teacher.lastName}`
      }
    });
  } catch (error) {
    console.error('Error updating teacher:', error);
    res.status(500).json({
      success: false,
      message: 'Gagal memperbarui data guru'
    });
  }
});

export default router;
